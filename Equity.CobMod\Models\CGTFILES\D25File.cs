using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D25File Data Structure

public class D25File
{
    private static int _size = 12;
    // [DEBUG] Class: D25File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler190, is_external=, is_static_class=False, static_prefix=
    private string _Filler190 ="$";
    
    
    
    
    // [DEBUG] Field: D25UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D25UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler191, is_external=, is_static_class=False, static_prefix=
    private string _Filler191 ="R3";
    
    
    
    
    // [DEBUG] Field: D25ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D25ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler192, is_external=, is_static_class=False, static_prefix=
    private string _Filler192 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD25FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler190.PadRight(1));
        result.Append(_D25UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler191.PadRight(2));
        result.Append(_D25ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler192.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD25FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller190(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD25UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller191(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD25ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller192(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD25FileAsString();
    }
    // Set<>String Override function
    public void SetD25File(string value)
    {
        SetD25FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller190()
    {
        return _Filler190;
    }
    
    // Standard Setter
    public void SetFiller190(string value)
    {
        _Filler190 = value;
    }
    
    // Get<>AsString()
    public string GetFiller190AsString()
    {
        return _Filler190.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller190AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler190 = value;
    }
    
    // Standard Getter
    public int GetD25UserNo()
    {
        return _D25UserNo;
    }
    
    // Standard Setter
    public void SetD25UserNo(int value)
    {
        _D25UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD25UserNoAsString()
    {
        return _D25UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD25UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D25UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller191()
    {
        return _Filler191;
    }
    
    // Standard Setter
    public void SetFiller191(string value)
    {
        _Filler191 = value;
    }
    
    // Get<>AsString()
    public string GetFiller191AsString()
    {
        return _Filler191.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller191AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler191 = value;
    }
    
    // Standard Getter
    public int GetD25ReportNo()
    {
        return _D25ReportNo;
    }
    
    // Standard Setter
    public void SetD25ReportNo(int value)
    {
        _D25ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD25ReportNoAsString()
    {
        return _D25ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD25ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D25ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller192()
    {
        return _Filler192;
    }
    
    // Standard Setter
    public void SetFiller192(string value)
    {
        _Filler192 = value;
    }
    
    // Get<>AsString()
    public string GetFiller192AsString()
    {
        return _Filler192.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller192AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler192 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}