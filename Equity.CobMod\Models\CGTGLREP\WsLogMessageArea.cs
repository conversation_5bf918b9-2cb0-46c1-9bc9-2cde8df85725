using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsLogMessageArea Data Structure

public class WsLogMessageArea
{
    private static int _size = 75;
    // [DEBUG] Class: WsLogMessageArea, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsLogMessageNo, is_external=, is_static_class=False, static_prefix=
    private int _WsLogMessageNo =0;
    
    
    
    
    // [DEBUG] Field: WsLogMessageType, is_external=, is_static_class=False, static_prefix=
    private string _WsLogMessageType ="";
    
    
    // 88-level condition checks for WsLogMessageType
    public bool IsQuitRun()
    {
        if (this._WsLogMessageType == "Q") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsLogMessage, is_external=, is_static_class=False, static_prefix=
    private string _WsLogMessage ="";
    
    
    
    
    
    // Serialization methods
    public string GetWsLogMessageAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsLogMessageNo.ToString().PadLeft(5, '0'));
        result.Append(_WsLogMessageType.PadRight(1));
        result.Append(_WsLogMessage.PadRight(69));
        
        return result.ToString();
    }
    
    public void SetWsLogMessageAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsLogMessageNo(parsedInt);
        }
        offset += 5;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsLogMessageType(extracted);
        }
        offset += 1;
        if (offset + 69 <= data.Length)
        {
            string extracted = data.Substring(offset, 69).Trim();
            SetWsLogMessage(extracted);
        }
        offset += 69;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsLogMessageAreaAsString();
    }
    // Set<>String Override function
    public void SetWsLogMessageArea(string value)
    {
        SetWsLogMessageAreaAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWsLogMessageNo()
    {
        return _WsLogMessageNo;
    }
    
    // Standard Setter
    public void SetWsLogMessageNo(int value)
    {
        _WsLogMessageNo = value;
    }
    
    // Get<>AsString()
    public string GetWsLogMessageNoAsString()
    {
        return _WsLogMessageNo.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWsLogMessageNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsLogMessageNo = parsed;
    }
    
    // Standard Getter
    public string GetWsLogMessageType()
    {
        return _WsLogMessageType;
    }
    
    // Standard Setter
    public void SetWsLogMessageType(string value)
    {
        _WsLogMessageType = value;
    }
    
    // Get<>AsString()
    public string GetWsLogMessageTypeAsString()
    {
        return _WsLogMessageType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsLogMessageTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsLogMessageType = value;
    }
    
    // Standard Getter
    public string GetWsLogMessage()
    {
        return _WsLogMessage;
    }
    
    // Standard Setter
    public void SetWsLogMessage(string value)
    {
        _WsLogMessage = value;
    }
    
    // Get<>AsString()
    public string GetWsLogMessageAsString()
    {
        return _WsLogMessage.PadRight(69);
    }
    
    // Set<>AsString()
    public void SetWsLogMessageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsLogMessage = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}