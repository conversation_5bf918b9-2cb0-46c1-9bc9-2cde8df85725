using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D36Record Data Structure

public class D36Record
{
    private static int _size = 79;
    // [DEBUG] Class: D36Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D36Key, is_external=, is_static_class=False, static_prefix=
    private D36Key _D36Key = new D36Key();
    
    
    
    
    // [DEBUG] Field: D36Message, is_external=, is_static_class=False, static_prefix=
    private string _D36Message ="";
    
    
    
    
    
    // Serialization methods
    public string GetD36RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D36Key.GetD36KeyAsString());
        result.Append(_D36Message.PadRight(75));
        
        return result.ToString();
    }
    
    public void SetD36RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _D36Key.SetD36KeyAsString(data.Substring(offset, 4));
        }
        else
        {
            _D36Key.SetD36KeyAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 75 <= data.Length)
        {
            string extracted = data.Substring(offset, 75).Trim();
            SetD36Message(extracted);
        }
        offset += 75;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD36RecordAsString();
    }
    // Set<>String Override function
    public void SetD36Record(string value)
    {
        SetD36RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D36Key GetD36Key()
    {
        return _D36Key;
    }
    
    // Standard Setter
    public void SetD36Key(D36Key value)
    {
        _D36Key = value;
    }
    
    // Get<>AsString()
    public string GetD36KeyAsString()
    {
        return _D36Key != null ? _D36Key.GetD36KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD36KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D36Key == null)
        {
            _D36Key = new D36Key();
        }
        _D36Key.SetD36KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD36Message()
    {
        return _D36Message;
    }
    
    // Standard Setter
    public void SetD36Message(string value)
    {
        _D36Message = value;
    }
    
    // Get<>AsString()
    public string GetD36MessageAsString()
    {
        return _D36Message.PadRight(75);
    }
    
    // Set<>AsString()
    public void SetD36MessageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D36Message = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD36Key(string value)
    {
        _D36Key.SetD36KeyAsString(value);
    }
    // Nested Class: D36Key
    public class D36Key
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D36MessageCode, is_external=, is_static_class=False, static_prefix=
        private int _D36MessageCode =0;
        
        
        
        
    public D36Key() {}
    
    public D36Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD36MessageCode(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetD36KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D36MessageCode.ToString().PadLeft(4, '0'));
        
        return result.ToString();
    }
    
    public void SetD36KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD36MessageCode(parsedInt);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD36MessageCode()
    {
        return _D36MessageCode;
    }
    
    // Standard Setter
    public void SetD36MessageCode(int value)
    {
        _D36MessageCode = value;
    }
    
    // Get<>AsString()
    public string GetD36MessageCodeAsString()
    {
        return _D36MessageCode.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD36MessageCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D36MessageCode = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}