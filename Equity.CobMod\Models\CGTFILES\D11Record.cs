using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D11Record Data Structure

public class D11Record
{
    private static int _size = 133;
    // [DEBUG] Class: D11Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D11PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D11PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D11ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D11ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD11RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D11PrintControl.PadRight(1));
        result.Append(_D11ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD11RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD11PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD11ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD11RecordAsString();
    }
    // Set<>String Override function
    public void SetD11Record(string value)
    {
        SetD11RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD11PrintControl()
    {
        return _D11PrintControl;
    }
    
    // Standard Setter
    public void SetD11PrintControl(string value)
    {
        _D11PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD11PrintControlAsString()
    {
        return _D11PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD11PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D11PrintControl = value;
    }
    
    // Standard Getter
    public string GetD11ReportLine()
    {
        return _D11ReportLine;
    }
    
    // Standard Setter
    public void SetD11ReportLine(string value)
    {
        _D11ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD11ReportLineAsString()
    {
        return _D11ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD11ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D11ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}