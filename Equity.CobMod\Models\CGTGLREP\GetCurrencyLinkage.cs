using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing GetCurrencyLinkage Data Structure

public class GetCurrencyLinkage
{
    private static int _size = 22;
    // [DEBUG] Class: GetCurrencyLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: GetCurrencyFundType, is_external=, is_static_class=False, static_prefix=
    private string _GetCurrencyFundType ="";
    
    
    // 88-level condition checks for GetCurrencyFundType
    public bool IsSterlingFund()
    {
        if (this._GetCurrencyFundType == "'C'") return true;
        if (this._GetCurrencyFundType == "'I'") return true;
        if (this._GetCurrencyFundType == "'L'") return true;
        if (this._GetCurrencyFundType == "'T'") return true;
        if (this._GetCurrencyFundType == "'J'") return true;
        if (this._GetCurrencyFundType == "'Z'") return true;
        return false;
    }
    public bool IsEuroFund()
    {
        if (this._GetCurrencyFundType == "'X'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: GetCurrencyStatus, is_external=, is_static_class=False, static_prefix=
    private int _GetCurrencyStatus =0;
    
    
    // 88-level condition checks for GetCurrencyStatus
    public bool IsCurrencyHasNotChanged()
    {
        if (this._GetCurrencyStatus == 0) return true;
        return false;
    }
    public bool IsCurrencyHasChanged()
    {
        if (this._GetCurrencyStatus == 1) return true;
        return false;
    }
    
    
    // [DEBUG] Field: GetCurrencyPrintString, is_external=, is_static_class=False, static_prefix=
    private string _GetCurrencyPrintString ="";
    
    
    
    
    // [DEBUG] Field: LastGetCurrencyPrintString, is_external=, is_static_class=False, static_prefix=
    private string _LastGetCurrencyPrintString =" ";
    
    
    // 88-level condition checks for LastGetCurrencyPrintString
    public bool IsGetCurrencyFirstTime()
    {
        if (this._LastGetCurrencyPrintString == "spaces") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetGetCurrencyLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_GetCurrencyFundType.PadRight(1));
        result.Append(_GetCurrencyStatus.ToString().PadLeft(1, '0'));
        result.Append(_GetCurrencyPrintString.PadRight(10));
        result.Append(_LastGetCurrencyPrintString.PadRight(10));
        
        return result.ToString();
    }
    
    public void SetGetCurrencyLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetGetCurrencyFundType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetGetCurrencyStatus(parsedInt);
        }
        offset += 1;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetGetCurrencyPrintString(extracted);
        }
        offset += 10;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetLastGetCurrencyPrintString(extracted);
        }
        offset += 10;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetGetCurrencyLinkageAsString();
    }
    // Set<>String Override function
    public void SetGetCurrencyLinkage(string value)
    {
        SetGetCurrencyLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetGetCurrencyFundType()
    {
        return _GetCurrencyFundType;
    }
    
    // Standard Setter
    public void SetGetCurrencyFundType(string value)
    {
        _GetCurrencyFundType = value;
    }
    
    // Get<>AsString()
    public string GetGetCurrencyFundTypeAsString()
    {
        return _GetCurrencyFundType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetGetCurrencyFundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GetCurrencyFundType = value;
    }
    
    // Standard Getter
    public int GetGetCurrencyStatus()
    {
        return _GetCurrencyStatus;
    }
    
    // Standard Setter
    public void SetGetCurrencyStatus(int value)
    {
        _GetCurrencyStatus = value;
    }
    
    // Get<>AsString()
    public string GetGetCurrencyStatusAsString()
    {
        return _GetCurrencyStatus.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetGetCurrencyStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _GetCurrencyStatus = parsed;
    }
    
    // Standard Getter
    public string GetGetCurrencyPrintString()
    {
        return _GetCurrencyPrintString;
    }
    
    // Standard Setter
    public void SetGetCurrencyPrintString(string value)
    {
        _GetCurrencyPrintString = value;
    }
    
    // Get<>AsString()
    public string GetGetCurrencyPrintStringAsString()
    {
        return _GetCurrencyPrintString.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetGetCurrencyPrintStringAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GetCurrencyPrintString = value;
    }
    
    // Standard Getter
    public string GetLastGetCurrencyPrintString()
    {
        return _LastGetCurrencyPrintString;
    }
    
    // Standard Setter
    public void SetLastGetCurrencyPrintString(string value)
    {
        _LastGetCurrencyPrintString = value;
    }
    
    // Get<>AsString()
    public string GetLastGetCurrencyPrintStringAsString()
    {
        return _LastGetCurrencyPrintString.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetLastGetCurrencyPrintStringAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LastGetCurrencyPrintString = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}