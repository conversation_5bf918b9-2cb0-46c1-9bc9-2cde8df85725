using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing Cgtdate2LinkageDate1 Data Structure

public class Cgtdate2LinkageDate1
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd1 =0;
    
    
    
    
    // [DEBUG] Field: Filler358, is_external=, is_static_class=False, static_prefix=
    private Filler358 _Filler358 = new Filler358();
    
    
    
    
    // [DEBUG] Field: Filler359, is_external=, is_static_class=False, static_prefix=
    private Filler359 _Filler359 = new Filler359();
    
    
    
    
    // [DEBUG] Field: Filler360, is_external=, is_static_class=False, static_prefix=
    private Filler360 _Filler360 = new Filler360();
    
    
    
    
    // [DEBUG] Field: Filler361, is_external=, is_static_class=False, static_prefix=
    private Filler361 _Filler361 = new Filler361();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd1.ToString().PadLeft(8, '0'));
        result.Append(_Filler358.GetFiller358AsString());
        result.Append(_Filler359.GetFiller359AsString());
        result.Append(_Filler360.GetFiller360AsString());
        result.Append(_Filler361.GetFiller361AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd1(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler358.SetFiller358AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler358.SetFiller358AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler359.SetFiller359AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler359.SetFiller359AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler360.SetFiller360AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler360.SetFiller360AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler361.SetFiller361AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler361.SetFiller361AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate1AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate1(string value)
    {
        SetCgtdate2LinkageDate1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd1()
    {
        return _Cgtdate2Ccyymmdd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd1(int value)
    {
        _Cgtdate2Ccyymmdd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd1AsString()
    {
        return _Cgtdate2Ccyymmdd1.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd1 = parsed;
    }
    
    // Standard Getter
    public Filler358 GetFiller358()
    {
        return _Filler358;
    }
    
    // Standard Setter
    public void SetFiller358(Filler358 value)
    {
        _Filler358 = value;
    }
    
    // Get<>AsString()
    public string GetFiller358AsString()
    {
        return _Filler358 != null ? _Filler358.GetFiller358AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller358AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler358 == null)
        {
            _Filler358 = new Filler358();
        }
        _Filler358.SetFiller358AsString(value);
    }
    
    // Standard Getter
    public Filler359 GetFiller359()
    {
        return _Filler359;
    }
    
    // Standard Setter
    public void SetFiller359(Filler359 value)
    {
        _Filler359 = value;
    }
    
    // Get<>AsString()
    public string GetFiller359AsString()
    {
        return _Filler359 != null ? _Filler359.GetFiller359AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller359AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler359 == null)
        {
            _Filler359 = new Filler359();
        }
        _Filler359.SetFiller359AsString(value);
    }
    
    // Standard Getter
    public Filler360 GetFiller360()
    {
        return _Filler360;
    }
    
    // Standard Setter
    public void SetFiller360(Filler360 value)
    {
        _Filler360 = value;
    }
    
    // Get<>AsString()
    public string GetFiller360AsString()
    {
        return _Filler360 != null ? _Filler360.GetFiller360AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller360AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler360 == null)
        {
            _Filler360 = new Filler360();
        }
        _Filler360.SetFiller360AsString(value);
    }
    
    // Standard Getter
    public Filler361 GetFiller361()
    {
        return _Filler361;
    }
    
    // Standard Setter
    public void SetFiller361(Filler361 value)
    {
        _Filler361 = value;
    }
    
    // Get<>AsString()
    public string GetFiller361AsString()
    {
        return _Filler361 != null ? _Filler361.GetFiller361AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller361AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler361 == null)
        {
            _Filler361 = new Filler361();
        }
        _Filler361.SetFiller361AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller358(string value)
    {
        _Filler358.SetFiller358AsString(value);
    }
    // Nested Class: Filler358
    public class Filler358
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc1 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd1, is_external=, is_static_class=False, static_prefix=
        private Filler358.Cgtdate2Yymmdd1 _Cgtdate2Yymmdd1 = new Filler358.Cgtdate2Yymmdd1();
        
        
        
        
    public Filler358() {}
    
    public Filler358(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc1(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(data.Substring(offset, Cgtdate2Yymmdd1.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller358AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc1.PadRight(2));
        result.Append(_Cgtdate2Yymmdd1.GetCgtdate2Yymmdd1AsString());
        
        return result.ToString();
    }
    
    public void SetFiller358AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc1(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc1()
    {
        return _Cgtdate2Cc1;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc1(string value)
    {
        _Cgtdate2Cc1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc1AsString()
    {
        return _Cgtdate2Cc1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc1 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd1 GetCgtdate2Yymmdd1()
    {
        return _Cgtdate2Yymmdd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd1(Cgtdate2Yymmdd1 value)
    {
        _Cgtdate2Yymmdd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd1AsString()
    {
        return _Cgtdate2Yymmdd1 != null ? _Cgtdate2Yymmdd1.GetCgtdate2Yymmdd1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd1 == null)
        {
            _Cgtdate2Yymmdd1 = new Cgtdate2Yymmdd1();
        }
        _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd1
    public class Cgtdate2Yymmdd1
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy1 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd1, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd1.Cgtdate2Mmdd1 _Cgtdate2Mmdd1 = new Cgtdate2Yymmdd1.Cgtdate2Mmdd1();
        
        
        
        
    public Cgtdate2Yymmdd1() {}
    
    public Cgtdate2Yymmdd1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy1(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(data.Substring(offset, Cgtdate2Mmdd1.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy1.PadRight(2));
        result.Append(_Cgtdate2Mmdd1.GetCgtdate2Mmdd1AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy1(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy1()
    {
        return _Cgtdate2Yy1;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy1(string value)
    {
        _Cgtdate2Yy1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy1AsString()
    {
        return _Cgtdate2Yy1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy1 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd1 GetCgtdate2Mmdd1()
    {
        return _Cgtdate2Mmdd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd1(Cgtdate2Mmdd1 value)
    {
        _Cgtdate2Mmdd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd1AsString()
    {
        return _Cgtdate2Mmdd1 != null ? _Cgtdate2Mmdd1.GetCgtdate2Mmdd1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd1 == null)
        {
            _Cgtdate2Mmdd1 = new Cgtdate2Mmdd1();
        }
        _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd1
    public class Cgtdate2Mmdd1
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm1 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd1 ="";
        
        
        
        
    public Cgtdate2Mmdd1() {}
    
    public Cgtdate2Mmdd1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm1(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd1(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm1.PadRight(2));
        result.Append(_Cgtdate2Dd1.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm1(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd1(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm1()
    {
        return _Cgtdate2Mm1;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm1(string value)
    {
        _Cgtdate2Mm1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm1AsString()
    {
        return _Cgtdate2Mm1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm1 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd1()
    {
        return _Cgtdate2Dd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd1(string value)
    {
        _Cgtdate2Dd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd1AsString()
    {
        return _Cgtdate2Dd1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd1 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller359(string value)
{
    _Filler359.SetFiller359AsString(value);
}
// Nested Class: Filler359
public class Filler359
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd1 =0;
    
    
    
    
public Filler359() {}

public Filler359(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy1(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd1(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller359AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy1.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd1.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller359AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy1(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd1(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy1()
{
    return _Cgtdate2CCcyy1;
}

// Standard Setter
public void SetCgtdate2CCcyy1(int value)
{
    _Cgtdate2CCcyy1 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy1AsString()
{
    return _Cgtdate2CCcyy1.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy1 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd1()
{
    return _Cgtdate2CMmdd1;
}

// Standard Setter
public void SetCgtdate2CMmdd1(int value)
{
    _Cgtdate2CMmdd1 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd1AsString()
{
    return _Cgtdate2CMmdd1.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd1 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller360(string value)
{
    _Filler360.SetFiller360AsString(value);
}
// Nested Class: Filler360
public class Filler360
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd1 =0;
    
    
    
    
public Filler360() {}

public Filler360(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller360AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc1.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy1.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm1.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd1.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller360AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc1(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy1(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm1(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd1(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc1()
{
    return _Cgtdate2CCc1;
}

// Standard Setter
public void SetCgtdate2CCc1(int value)
{
    _Cgtdate2CCc1 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc1AsString()
{
    return _Cgtdate2CCc1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc1 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy1()
{
    return _Cgtdate2CYy1;
}

// Standard Setter
public void SetCgtdate2CYy1(int value)
{
    _Cgtdate2CYy1 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy1AsString()
{
    return _Cgtdate2CYy1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy1 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm1()
{
    return _Cgtdate2CMm1;
}

// Standard Setter
public void SetCgtdate2CMm1(int value)
{
    _Cgtdate2CMm1 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm1AsString()
{
    return _Cgtdate2CMm1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm1 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd1()
{
    return _Cgtdate2CDd1;
}

// Standard Setter
public void SetCgtdate2CDd1(int value)
{
    _Cgtdate2CDd1 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd1AsString()
{
    return _Cgtdate2CDd1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd1 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller361(string value)
{
    _Filler361.SetFiller361AsString(value);
}
// Nested Class: Filler361
public class Filler361
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm1 =0;
    
    
    
    
    // [DEBUG] Field: Filler362, is_external=, is_static_class=False, static_prefix=
    private string _Filler362 ="";
    
    
    
    
public Filler361() {}

public Filler361(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm1(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller362(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller361AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm1.ToString().PadLeft(6, '0'));
    result.Append(_Filler362.PadRight(2));
    
    return result.ToString();
}

public void SetFiller361AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm1(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller362(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm1()
{
    return _Cgtdate2CCcyymm1;
}

// Standard Setter
public void SetCgtdate2CCcyymm1(int value)
{
    _Cgtdate2CCcyymm1 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm1AsString()
{
    return _Cgtdate2CCcyymm1.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm1 = parsed;
}

// Standard Getter
public string GetFiller362()
{
    return _Filler362;
}

// Standard Setter
public void SetFiller362(string value)
{
    _Filler362 = value;
}

// Get<>AsString()
public string GetFiller362AsString()
{
    return _Filler362.PadRight(2);
}

// Set<>AsString()
public void SetFiller362AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler362 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}