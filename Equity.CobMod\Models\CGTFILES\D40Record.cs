using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D40Record Data Structure

public class D40Record
{
    private static int _size = 12;
    // [DEBUG] Class: D40Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D40Key, is_external=, is_static_class=False, static_prefix=
    private D40Key _D40Key = new D40Key();
    
    
    
    
    
    // Serialization methods
    public string GetD40RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D40Key.GetD40KeyAsString());
        
        return result.ToString();
    }
    
    public void SetD40RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 12 <= data.Length)
        {
            _D40Key.SetD40KeyAsString(data.Substring(offset, 12));
        }
        else
        {
            _D40Key.SetD40KeyAsString(data.Substring(offset));
        }
        offset += 12;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD40RecordAsString();
    }
    // Set<>String Override function
    public void SetD40Record(string value)
    {
        SetD40RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D40Key GetD40Key()
    {
        return _D40Key;
    }
    
    // Standard Setter
    public void SetD40Key(D40Key value)
    {
        _D40Key = value;
    }
    
    // Get<>AsString()
    public string GetD40KeyAsString()
    {
        return _D40Key != null ? _D40Key.GetD40KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD40KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D40Key == null)
        {
            _D40Key = new D40Key();
        }
        _D40Key.SetD40KeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD40Key(string value)
    {
        _D40Key.SetD40KeyAsString(value);
    }
    // Nested Class: D40Key
    public class D40Key
    {
        private static int _size = 12;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D40UserNo, is_external=, is_static_class=False, static_prefix=
        private int _D40UserNo =0;
        
        
        
        
        // [DEBUG] Field: D40Program, is_external=, is_static_class=False, static_prefix=
        private string _D40Program ="";
        
        
        
        
    public D40Key() {}
    
    public D40Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD40UserNo(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        SetD40Program(data.Substring(offset, 8).Trim());
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetD40KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D40UserNo.ToString().PadLeft(4, '0'));
        result.Append(_D40Program.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetD40KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD40UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD40Program(extracted);
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD40UserNo()
    {
        return _D40UserNo;
    }
    
    // Standard Setter
    public void SetD40UserNo(int value)
    {
        _D40UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD40UserNoAsString()
    {
        return _D40UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD40UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D40UserNo = parsed;
    }
    
    // Standard Getter
    public string GetD40Program()
    {
        return _D40Program;
    }
    
    // Standard Setter
    public void SetD40Program(string value)
    {
        _D40Program = value;
    }
    
    // Get<>AsString()
    public string GetD40ProgramAsString()
    {
        return _D40Program.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD40ProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D40Program = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}