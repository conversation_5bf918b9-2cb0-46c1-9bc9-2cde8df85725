using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsReportFile Data Structure

public class WsReportFile
{
    private static int _size = 12;
    // [DEBUG] Class: WsReportFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
    private string _Filler28 ="$";
    
    
    
    
    // [DEBUG] Field: GainlossUserNo, is_external=, is_static_class=False, static_prefix=
    private string _GainlossUserNo ="";
    
    
    
    
    // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
    private string _Filler29 ="RL";
    
    
    
    
    // [DEBUG] Field: GainlossReportNo, is_external=, is_static_class=False, static_prefix=
    private int _GainlossReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler30, is_external=, is_static_class=False, static_prefix=
    private string _Filler30 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetWsReportFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler28.PadRight(1));
        result.Append(_GainlossUserNo.PadRight(4));
        result.Append(_Filler29.PadRight(2));
        result.Append(_GainlossReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler30.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetWsReportFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller28(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetGainlossUserNo(extracted);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller29(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetGainlossReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller30(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsReportFileAsString();
    }
    // Set<>String Override function
    public void SetWsReportFile(string value)
    {
        SetWsReportFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller28()
    {
        return _Filler28;
    }
    
    // Standard Setter
    public void SetFiller28(string value)
    {
        _Filler28 = value;
    }
    
    // Get<>AsString()
    public string GetFiller28AsString()
    {
        return _Filler28.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller28AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler28 = value;
    }
    
    // Standard Getter
    public string GetGainlossUserNo()
    {
        return _GainlossUserNo;
    }
    
    // Standard Setter
    public void SetGainlossUserNo(string value)
    {
        _GainlossUserNo = value;
    }
    
    // Get<>AsString()
    public string GetGainlossUserNoAsString()
    {
        return _GainlossUserNo.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetGainlossUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GainlossUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller29()
    {
        return _Filler29;
    }
    
    // Standard Setter
    public void SetFiller29(string value)
    {
        _Filler29 = value;
    }
    
    // Get<>AsString()
    public string GetFiller29AsString()
    {
        return _Filler29.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller29AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler29 = value;
    }
    
    // Standard Getter
    public int GetGainlossReportNo()
    {
        return _GainlossReportNo;
    }
    
    // Standard Setter
    public void SetGainlossReportNo(int value)
    {
        _GainlossReportNo = value;
    }
    
    // Get<>AsString()
    public string GetGainlossReportNoAsString()
    {
        return _GainlossReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetGainlossReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _GainlossReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller30()
    {
        return _Filler30;
    }
    
    // Standard Setter
    public void SetFiller30(string value)
    {
        _Filler30 = value;
    }
    
    // Get<>AsString()
    public string GetFiller30AsString()
    {
        return _Filler30.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller30AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler30 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}