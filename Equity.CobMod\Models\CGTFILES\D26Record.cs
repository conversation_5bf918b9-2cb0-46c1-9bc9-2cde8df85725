using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D26Record Data Structure

public class D26Record
{
    private static int _size = 133;
    // [DEBUG] Class: D26Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D26PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D26PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D26ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D26ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD26RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D26PrintControl.PadRight(1));
        result.Append(_D26ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD26RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD26PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD26ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD26RecordAsString();
    }
    // Set<>String Override function
    public void SetD26Record(string value)
    {
        SetD26RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD26PrintControl()
    {
        return _D26PrintControl;
    }
    
    // Standard Setter
    public void SetD26PrintControl(string value)
    {
        _D26PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD26PrintControlAsString()
    {
        return _D26PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD26PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D26PrintControl = value;
    }
    
    // Standard Getter
    public string GetD26ReportLine()
    {
        return _D26ReportLine;
    }
    
    // Standard Setter
    public void SetD26ReportLine(string value)
    {
        _D26ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD26ReportLineAsString()
    {
        return _D26ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD26ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D26ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}