using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D161Record Data Structure

public class D161Record
{
    private static int _size = 1;
    // [DEBUG] Class: D161Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D161RecordByte, is_external=, is_static_class=False, static_prefix=
    private string _D161RecordByte ="";
    
    
    
    
    
    // Serialization methods
    public string GetD161RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D161RecordByte.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD161RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD161RecordByte(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD161RecordAsString();
    }
    // Set<>String Override function
    public void SetD161Record(string value)
    {
        SetD161RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD161RecordByte()
    {
        return _D161RecordByte;
    }
    
    // Standard Setter
    public void SetD161RecordByte(string value)
    {
        _D161RecordByte = value;
    }
    
    // Get<>AsString()
    public string GetD161RecordByteAsString()
    {
        return _D161RecordByte.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD161RecordByteAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D161RecordByte = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}