using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D29Record Data Structure

public class D29Record
{
    private static int _size = 482;
    // [DEBUG] Class: D29Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D29ErrorCode, is_external=, is_static_class=False, static_prefix=
    private int _D29ErrorCode =0;
    
    
    
    
    // [DEBUG] Field: D29ErrorTableNo, is_external=, is_static_class=False, static_prefix=
    private int _D29ErrorTableNo =0;
    
    
    
    
    // [DEBUG] Field: D29ErrorProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D29ErrorProceeds =0;
    
    
    
    
    // [DEBUG] Field: D29ErrorMasterRecord, is_external=, is_static_class=False, static_prefix=
    private string _D29ErrorMasterRecord ="";
    
    
    
    
    
    // Serialization methods
    public string GetD29RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D29ErrorCode.ToString().PadLeft(2, '0'));
        result.Append(_D29ErrorTableNo.ToString().PadLeft(2, '0'));
        result.Append(_D29ErrorProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D29ErrorMasterRecord.PadRight(470));
        
        return result.ToString();
    }
    
    public void SetD29RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD29ErrorCode(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD29ErrorTableNo(parsedInt);
        }
        offset += 2;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD29ErrorProceeds(parsedDec);
        }
        offset += 8;
        if (offset + 470 <= data.Length)
        {
            string extracted = data.Substring(offset, 470).Trim();
            SetD29ErrorMasterRecord(extracted);
        }
        offset += 470;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD29RecordAsString();
    }
    // Set<>String Override function
    public void SetD29Record(string value)
    {
        SetD29RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD29ErrorCode()
    {
        return _D29ErrorCode;
    }
    
    // Standard Setter
    public void SetD29ErrorCode(int value)
    {
        _D29ErrorCode = value;
    }
    
    // Get<>AsString()
    public string GetD29ErrorCodeAsString()
    {
        return _D29ErrorCode.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD29ErrorCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D29ErrorCode = parsed;
    }
    
    // Standard Getter
    public int GetD29ErrorTableNo()
    {
        return _D29ErrorTableNo;
    }
    
    // Standard Setter
    public void SetD29ErrorTableNo(int value)
    {
        _D29ErrorTableNo = value;
    }
    
    // Get<>AsString()
    public string GetD29ErrorTableNoAsString()
    {
        return _D29ErrorTableNo.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD29ErrorTableNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D29ErrorTableNo = parsed;
    }
    
    // Standard Getter
    public decimal GetD29ErrorProceeds()
    {
        return _D29ErrorProceeds;
    }
    
    // Standard Setter
    public void SetD29ErrorProceeds(decimal value)
    {
        _D29ErrorProceeds = value;
    }
    
    // Get<>AsString()
    public string GetD29ErrorProceedsAsString()
    {
        return _D29ErrorProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD29ErrorProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D29ErrorProceeds = parsed;
    }
    
    // Standard Getter
    public string GetD29ErrorMasterRecord()
    {
        return _D29ErrorMasterRecord;
    }
    
    // Standard Setter
    public void SetD29ErrorMasterRecord(string value)
    {
        _D29ErrorMasterRecord = value;
    }
    
    // Get<>AsString()
    public string GetD29ErrorMasterRecordAsString()
    {
        return _D29ErrorMasterRecord.PadRight(470);
    }
    
    // Set<>AsString()
    public void SetD29ErrorMasterRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D29ErrorMasterRecord = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}