using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D28Record Data Structure

public class D28Record
{
    private static int _size = 133;
    // [DEBUG] Class: D28Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D28PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D28PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D28ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D28ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD28RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D28PrintControl.PadRight(1));
        result.Append(_D28ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD28RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD28PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD28ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD28RecordAsString();
    }
    // Set<>String Override function
    public void SetD28Record(string value)
    {
        SetD28RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD28PrintControl()
    {
        return _D28PrintControl;
    }
    
    // Standard Setter
    public void SetD28PrintControl(string value)
    {
        _D28PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD28PrintControlAsString()
    {
        return _D28PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD28PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D28PrintControl = value;
    }
    
    // Standard Getter
    public string GetD28ReportLine()
    {
        return _D28ReportLine;
    }
    
    // Standard Setter
    public void SetD28ReportLine(string value)
    {
        _D28ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD28ReportLineAsString()
    {
        return _D28ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD28ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D28ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}