using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D110File Data Structure

public class D110File
{
    private static int _size = 12;
    // [DEBUG] Class: D110File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler315, is_external=, is_static_class=False, static_prefix=
    private string _Filler315 ="$";
    
    
    
    
    // [DEBUG] Field: D110UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D110UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler316, is_external=, is_static_class=False, static_prefix=
    private string _Filler316 ="R5";
    
    
    
    
    // [DEBUG] Field: D110ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D110ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler317, is_external=, is_static_class=False, static_prefix=
    private string _Filler317 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD110FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler315.PadRight(1));
        result.Append(_D110UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler316.PadRight(2));
        result.Append(_D110ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler317.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD110FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller315(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD110UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller316(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD110ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller317(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD110FileAsString();
    }
    // Set<>String Override function
    public void SetD110File(string value)
    {
        SetD110FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller315()
    {
        return _Filler315;
    }
    
    // Standard Setter
    public void SetFiller315(string value)
    {
        _Filler315 = value;
    }
    
    // Get<>AsString()
    public string GetFiller315AsString()
    {
        return _Filler315.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller315AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler315 = value;
    }
    
    // Standard Getter
    public int GetD110UserNo()
    {
        return _D110UserNo;
    }
    
    // Standard Setter
    public void SetD110UserNo(int value)
    {
        _D110UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD110UserNoAsString()
    {
        return _D110UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD110UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D110UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller316()
    {
        return _Filler316;
    }
    
    // Standard Setter
    public void SetFiller316(string value)
    {
        _Filler316 = value;
    }
    
    // Get<>AsString()
    public string GetFiller316AsString()
    {
        return _Filler316.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller316AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler316 = value;
    }
    
    // Standard Getter
    public int GetD110ReportNo()
    {
        return _D110ReportNo;
    }
    
    // Standard Setter
    public void SetD110ReportNo(int value)
    {
        _D110ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD110ReportNoAsString()
    {
        return _D110ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD110ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D110ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller317()
    {
        return _Filler317;
    }
    
    // Standard Setter
    public void SetFiller317(string value)
    {
        _Filler317 = value;
    }
    
    // Get<>AsString()
    public string GetFiller317AsString()
    {
        return _Filler317.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller317AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler317 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}