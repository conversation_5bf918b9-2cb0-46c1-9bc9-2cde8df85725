using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D23Record Data Structure

public class D23Record
{
    private static int _size = 155;
    // [DEBUG] Class: D23Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D23PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D23PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D23ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D23ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD23RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D23PrintControl.PadRight(1));
        result.Append(_D23ReportLine.PadRight(154));
        
        return result.ToString();
    }
    
    public void SetD23RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD23PrintControl(extracted);
        }
        offset += 1;
        if (offset + 154 <= data.Length)
        {
            string extracted = data.Substring(offset, 154).Trim();
            SetD23ReportLine(extracted);
        }
        offset += 154;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD23RecordAsString();
    }
    // Set<>String Override function
    public void SetD23Record(string value)
    {
        SetD23RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD23PrintControl()
    {
        return _D23PrintControl;
    }
    
    // Standard Setter
    public void SetD23PrintControl(string value)
    {
        _D23PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD23PrintControlAsString()
    {
        return _D23PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD23PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D23PrintControl = value;
    }
    
    // Standard Getter
    public string GetD23ReportLine()
    {
        return _D23ReportLine;
    }
    
    // Standard Setter
    public void SetD23ReportLine(string value)
    {
        _D23ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD23ReportLineAsString()
    {
        return _D23ReportLine.PadRight(154);
    }
    
    // Set<>AsString()
    public void SetD23ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D23ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}