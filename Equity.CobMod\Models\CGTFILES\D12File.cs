using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D12File Data Structure

public class D12File
{
    private static int _size = 12;
    // [DEBUG] Class: D12File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler167, is_external=, is_static_class=False, static_prefix=
    private string _Filler167 ="$";
    
    
    
    
    // [DEBUG] Field: D12UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D12UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler168, is_external=, is_static_class=False, static_prefix=
    private string _Filler168 ="OUT.LST";
    
    
    
    
    
    // Serialization methods
    public string GetD12FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler167.PadRight(1));
        result.Append(_D12UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler168.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD12FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller167(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD12UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller168(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD12FileAsString();
    }
    // Set<>String Override function
    public void SetD12File(string value)
    {
        SetD12FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller167()
    {
        return _Filler167;
    }
    
    // Standard Setter
    public void SetFiller167(string value)
    {
        _Filler167 = value;
    }
    
    // Get<>AsString()
    public string GetFiller167AsString()
    {
        return _Filler167.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller167AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler167 = value;
    }
    
    // Standard Getter
    public int GetD12UserNo()
    {
        return _D12UserNo;
    }
    
    // Standard Setter
    public void SetD12UserNo(int value)
    {
        _D12UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD12UserNoAsString()
    {
        return _D12UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD12UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D12UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller168()
    {
        return _Filler168;
    }
    
    // Standard Setter
    public void SetFiller168(string value)
    {
        _Filler168 = value;
    }
    
    // Get<>AsString()
    public string GetFiller168AsString()
    {
        return _Filler168.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller168AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler168 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}