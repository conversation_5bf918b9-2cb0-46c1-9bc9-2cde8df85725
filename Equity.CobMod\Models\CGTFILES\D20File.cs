using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D20File Data Structure

public class D20File
{
    private static int _size = 12;
    // [DEBUG] Class: D20File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler175, is_external=, is_static_class=False, static_prefix=
    private string _Filler175 ="$";
    
    
    
    
    // [DEBUG] Field: D20UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D20UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler176, is_external=, is_static_class=False, static_prefix=
    private string _Filler176 ="D6";
    
    
    
    
    // [DEBUG] Field: D20ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D20ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler177, is_external=, is_static_class=False, static_prefix=
    private string _Filler177 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD20FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler175.PadRight(1));
        result.Append(_D20UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler176.PadRight(2));
        result.Append(_D20ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler177.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD20FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller175(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD20UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller176(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD20ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller177(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD20FileAsString();
    }
    // Set<>String Override function
    public void SetD20File(string value)
    {
        SetD20FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller175()
    {
        return _Filler175;
    }
    
    // Standard Setter
    public void SetFiller175(string value)
    {
        _Filler175 = value;
    }
    
    // Get<>AsString()
    public string GetFiller175AsString()
    {
        return _Filler175.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller175AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler175 = value;
    }
    
    // Standard Getter
    public int GetD20UserNo()
    {
        return _D20UserNo;
    }
    
    // Standard Setter
    public void SetD20UserNo(int value)
    {
        _D20UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD20UserNoAsString()
    {
        return _D20UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD20UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D20UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller176()
    {
        return _Filler176;
    }
    
    // Standard Setter
    public void SetFiller176(string value)
    {
        _Filler176 = value;
    }
    
    // Get<>AsString()
    public string GetFiller176AsString()
    {
        return _Filler176.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller176AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler176 = value;
    }
    
    // Standard Getter
    public int GetD20ReportNo()
    {
        return _D20ReportNo;
    }
    
    // Standard Setter
    public void SetD20ReportNo(int value)
    {
        _D20ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD20ReportNoAsString()
    {
        return _D20ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD20ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D20ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller177()
    {
        return _Filler177;
    }
    
    // Standard Setter
    public void SetFiller177(string value)
    {
        _Filler177 = value;
    }
    
    // Get<>AsString()
    public string GetFiller177AsString()
    {
        return _Filler177.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller177AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler177 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}