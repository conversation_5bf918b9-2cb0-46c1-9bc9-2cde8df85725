using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D30Record Data Structure

public class D30Record
{
    private static int _size = 133;
    // [DEBUG] Class: D30Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D30PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D30PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D30ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D30ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD30RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D30PrintControl.PadRight(1));
        result.Append(_D30ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD30RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD30PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD30ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD30RecordAsString();
    }
    // Set<>String Override function
    public void SetD30Record(string value)
    {
        SetD30RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD30PrintControl()
    {
        return _D30PrintControl;
    }
    
    // Standard Setter
    public void SetD30PrintControl(string value)
    {
        _D30PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD30PrintControlAsString()
    {
        return _D30PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD30PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D30PrintControl = value;
    }
    
    // Standard Getter
    public string GetD30ReportLine()
    {
        return _D30ReportLine;
    }
    
    // Standard Setter
    public void SetD30ReportLine(string value)
    {
        _D30ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD30ReportLineAsString()
    {
        return _D30ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD30ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D30ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}