using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D33File Data Structure

public class D33File
{
    private static int _size = 12;
    // [DEBUG] Class: D33File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler208, is_external=, is_static_class=False, static_prefix=
    private string _Filler208 ="$";
    
    
    
    
    // [DEBUG] Field: D33UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D33UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler209, is_external=, is_static_class=False, static_prefix=
    private string _Filler209 ="DB";
    
    
    
    
    // [DEBUG] Field: D33ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D33ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler210, is_external=, is_static_class=False, static_prefix=
    private string _Filler210 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD33FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler208.PadRight(1));
        result.Append(_D33UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler209.PadRight(2));
        result.Append(_D33ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler210.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD33FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller208(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD33UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller209(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD33ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller210(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD33FileAsString();
    }
    // Set<>String Override function
    public void SetD33File(string value)
    {
        SetD33FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller208()
    {
        return _Filler208;
    }
    
    // Standard Setter
    public void SetFiller208(string value)
    {
        _Filler208 = value;
    }
    
    // Get<>AsString()
    public string GetFiller208AsString()
    {
        return _Filler208.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller208AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler208 = value;
    }
    
    // Standard Getter
    public int GetD33UserNo()
    {
        return _D33UserNo;
    }
    
    // Standard Setter
    public void SetD33UserNo(int value)
    {
        _D33UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD33UserNoAsString()
    {
        return _D33UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD33UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D33UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller209()
    {
        return _Filler209;
    }
    
    // Standard Setter
    public void SetFiller209(string value)
    {
        _Filler209 = value;
    }
    
    // Get<>AsString()
    public string GetFiller209AsString()
    {
        return _Filler209.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller209AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler209 = value;
    }
    
    // Standard Getter
    public int GetD33ReportNo()
    {
        return _D33ReportNo;
    }
    
    // Standard Setter
    public void SetD33ReportNo(int value)
    {
        _D33ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD33ReportNoAsString()
    {
        return _D33ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD33ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D33ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller210()
    {
        return _Filler210;
    }
    
    // Standard Setter
    public void SetFiller210(string value)
    {
        _Filler210 = value;
    }
    
    // Get<>AsString()
    public string GetFiller210AsString()
    {
        return _Filler210.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller210AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler210 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}