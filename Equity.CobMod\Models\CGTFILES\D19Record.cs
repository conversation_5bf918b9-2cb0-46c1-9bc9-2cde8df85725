using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D19Record Data Structure

public class D19Record
{
    private static int _size = 120;
    // [DEBUG] Class: D19Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D19KeyRecord, is_external=, is_static_class=False, static_prefix=
    private D19KeyRecord _D19KeyRecord = new D19KeyRecord();
    
    
    
    
    
    // Serialization methods
    public string GetD19RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D19KeyRecord.GetD19KeyRecordAsString());
        
        return result.ToString();
    }
    
    public void SetD19RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 120 <= data.Length)
        {
            _D19KeyRecord.SetD19KeyRecordAsString(data.Substring(offset, 120));
        }
        else
        {
            _D19KeyRecord.SetD19KeyRecordAsString(data.Substring(offset));
        }
        offset += 120;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD19RecordAsString();
    }
    // Set<>String Override function
    public void SetD19Record(string value)
    {
        SetD19RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D19KeyRecord GetD19KeyRecord()
    {
        return _D19KeyRecord;
    }
    
    // Standard Setter
    public void SetD19KeyRecord(D19KeyRecord value)
    {
        _D19KeyRecord = value;
    }
    
    // Get<>AsString()
    public string GetD19KeyRecordAsString()
    {
        return _D19KeyRecord != null ? _D19KeyRecord.GetD19KeyRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19KeyRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19KeyRecord == null)
        {
            _D19KeyRecord = new D19KeyRecord();
        }
        _D19KeyRecord.SetD19KeyRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD19KeyRecord(string value)
    {
        _D19KeyRecord.SetD19KeyRecordAsString(value);
    }
    // Nested Class: D19KeyRecord
    public class D19KeyRecord
    {
        private static int _size = 120;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D19SplitKey01, is_external=, is_static_class=False, static_prefix=
        private D19KeyRecord.D19SplitKey01 _D19SplitKey01 = new D19KeyRecord.D19SplitKey01();
        
        
        
        
        // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
        private string _Filler4 ="";
        
        
        
        
        // [DEBUG] Field: D19SplitKey02, is_external=, is_static_class=False, static_prefix=
        private D19KeyRecord.D19SplitKey02 _D19SplitKey02 = new D19KeyRecord.D19SplitKey02();
        
        
        
        
        // [DEBUG] Field: D19SplitKey04, is_external=, is_static_class=False, static_prefix=
        private D19KeyRecord.D19SplitKey04 _D19SplitKey04 = new D19KeyRecord.D19SplitKey04();
        
        
        
        
        // [DEBUG] Field: D19SplitKey06, is_external=, is_static_class=False, static_prefix=
        private D19KeyRecord.D19SplitKey06 _D19SplitKey06 = new D19KeyRecord.D19SplitKey06();
        
        
        
        
        // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
        private string _Filler5 ="";
        
        
        
        
        // [DEBUG] Field: D19SplitKey07, is_external=, is_static_class=False, static_prefix=
        private D19KeyRecord.D19SplitKey07 _D19SplitKey07 = new D19KeyRecord.D19SplitKey07();
        
        
        
        
        // [DEBUG] Field: D19SplitKey03, is_external=, is_static_class=False, static_prefix=
        private D19KeyRecord.D19SplitKey03 _D19SplitKey03 = new D19KeyRecord.D19SplitKey03();
        
        
        
        
        // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
        private string _Filler6 ="";
        
        
        
        
        // [DEBUG] Field: D19TrancheFlag, is_external=, is_static_class=False, static_prefix=
        private string _D19TrancheFlag ="";
        
        
        
        
        // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
        private string _Filler7 ="";
        
        
        
        
        // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
        private string _Filler8 ="";
        
        
        
        
        // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
        private string _Filler9 ="";
        
        
        
        
        // [DEBUG] Field: D19SplitKey05, is_external=, is_static_class=False, static_prefix=
        private D19KeyRecord.D19SplitKey05 _D19SplitKey05 = new D19KeyRecord.D19SplitKey05();
        
        
        
        
        // [DEBUG] Field: Filler10, is_external=, is_static_class=False, static_prefix=
        private string _Filler10 ="";
        
        
        
        
        // [DEBUG] Field: Filler11, is_external=, is_static_class=False, static_prefix=
        private string _Filler11 ="";
        
        
        
        
        // [DEBUG] Field: Filler12, is_external=, is_static_class=False, static_prefix=
        private string _Filler12 ="";
        
        
        
        
        // [DEBUG] Field: Filler13, is_external=, is_static_class=False, static_prefix=
        private string _Filler13 ="";
        
        
        
        
        // [DEBUG] Field: D19TransactionCategory, is_external=, is_static_class=False, static_prefix=
        private string _D19TransactionCategory ="";
        
        
        
        
    public D19KeyRecord() {}
    
    public D19KeyRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D19SplitKey01.SetD19SplitKey01AsString(data.Substring(offset, D19SplitKey01.GetSize()));
        offset += 1;
        SetFiller4(data.Substring(offset, 0).Trim());
        offset += 0;
        _D19SplitKey02.SetD19SplitKey02AsString(data.Substring(offset, D19SplitKey02.GetSize()));
        offset += 0;
        _D19SplitKey04.SetD19SplitKey04AsString(data.Substring(offset, D19SplitKey04.GetSize()));
        offset += 0;
        _D19SplitKey06.SetD19SplitKey06AsString(data.Substring(offset, D19SplitKey06.GetSize()));
        offset += 0;
        SetFiller5(data.Substring(offset, 110).Trim());
        offset += 110;
        _D19SplitKey07.SetD19SplitKey07AsString(data.Substring(offset, D19SplitKey07.GetSize()));
        offset += 9;
        _D19SplitKey03.SetD19SplitKey03AsString(data.Substring(offset, D19SplitKey03.GetSize()));
        offset += 0;
        SetFiller6(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD19TrancheFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller7(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller8(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller9(data.Substring(offset, 0).Trim());
        offset += 0;
        _D19SplitKey05.SetD19SplitKey05AsString(data.Substring(offset, D19SplitKey05.GetSize()));
        offset += 0;
        SetFiller10(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller11(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller12(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller13(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD19TransactionCategory(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD19KeyRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D19SplitKey01.GetD19SplitKey01AsString());
        result.Append(_Filler4.PadRight(0));
        result.Append(_D19SplitKey02.GetD19SplitKey02AsString());
        result.Append(_D19SplitKey04.GetD19SplitKey04AsString());
        result.Append(_D19SplitKey06.GetD19SplitKey06AsString());
        result.Append(_Filler5.PadRight(110));
        result.Append(_D19SplitKey07.GetD19SplitKey07AsString());
        result.Append(_D19SplitKey03.GetD19SplitKey03AsString());
        result.Append(_Filler6.PadRight(0));
        result.Append(_D19TrancheFlag.PadRight(0));
        result.Append(_Filler7.PadRight(0));
        result.Append(_Filler8.PadRight(0));
        result.Append(_Filler9.PadRight(0));
        result.Append(_D19SplitKey05.GetD19SplitKey05AsString());
        result.Append(_Filler10.PadRight(0));
        result.Append(_Filler11.PadRight(0));
        result.Append(_Filler12.PadRight(0));
        result.Append(_Filler13.PadRight(0));
        result.Append(_D19TransactionCategory.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD19KeyRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            _D19SplitKey01.SetD19SplitKey01AsString(data.Substring(offset, 1));
        }
        else
        {
            _D19SplitKey01.SetD19SplitKey01AsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller4(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D19SplitKey02.SetD19SplitKey02AsString(data.Substring(offset, 0));
        }
        else
        {
            _D19SplitKey02.SetD19SplitKey02AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D19SplitKey04.SetD19SplitKey04AsString(data.Substring(offset, 0));
        }
        else
        {
            _D19SplitKey04.SetD19SplitKey04AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D19SplitKey06.SetD19SplitKey06AsString(data.Substring(offset, 0));
        }
        else
        {
            _D19SplitKey06.SetD19SplitKey06AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 110 <= data.Length)
        {
            string extracted = data.Substring(offset, 110).Trim();
            SetFiller5(extracted);
        }
        offset += 110;
        if (offset + 9 <= data.Length)
        {
            _D19SplitKey07.SetD19SplitKey07AsString(data.Substring(offset, 9));
        }
        else
        {
            _D19SplitKey07.SetD19SplitKey07AsString(data.Substring(offset));
        }
        offset += 9;
        if (offset + 0 <= data.Length)
        {
            _D19SplitKey03.SetD19SplitKey03AsString(data.Substring(offset, 0));
        }
        else
        {
            _D19SplitKey03.SetD19SplitKey03AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller6(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD19TrancheFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller7(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller8(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller9(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D19SplitKey05.SetD19SplitKey05AsString(data.Substring(offset, 0));
        }
        else
        {
            _D19SplitKey05.SetD19SplitKey05AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller10(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller11(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller12(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller13(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD19TransactionCategory(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D19SplitKey01 GetD19SplitKey01()
    {
        return _D19SplitKey01;
    }
    
    // Standard Setter
    public void SetD19SplitKey01(D19SplitKey01 value)
    {
        _D19SplitKey01 = value;
    }
    
    // Get<>AsString()
    public string GetD19SplitKey01AsString()
    {
        return _D19SplitKey01 != null ? _D19SplitKey01.GetD19SplitKey01AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19SplitKey01AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19SplitKey01 == null)
        {
            _D19SplitKey01 = new D19SplitKey01();
        }
        _D19SplitKey01.SetD19SplitKey01AsString(value);
    }
    
    // Standard Getter
    public string GetFiller4()
    {
        return _Filler4;
    }
    
    // Standard Setter
    public void SetFiller4(string value)
    {
        _Filler4 = value;
    }
    
    // Get<>AsString()
    public string GetFiller4AsString()
    {
        return _Filler4.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler4 = value;
    }
    
    // Standard Getter
    public D19SplitKey02 GetD19SplitKey02()
    {
        return _D19SplitKey02;
    }
    
    // Standard Setter
    public void SetD19SplitKey02(D19SplitKey02 value)
    {
        _D19SplitKey02 = value;
    }
    
    // Get<>AsString()
    public string GetD19SplitKey02AsString()
    {
        return _D19SplitKey02 != null ? _D19SplitKey02.GetD19SplitKey02AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19SplitKey02AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19SplitKey02 == null)
        {
            _D19SplitKey02 = new D19SplitKey02();
        }
        _D19SplitKey02.SetD19SplitKey02AsString(value);
    }
    
    // Standard Getter
    public D19SplitKey04 GetD19SplitKey04()
    {
        return _D19SplitKey04;
    }
    
    // Standard Setter
    public void SetD19SplitKey04(D19SplitKey04 value)
    {
        _D19SplitKey04 = value;
    }
    
    // Get<>AsString()
    public string GetD19SplitKey04AsString()
    {
        return _D19SplitKey04 != null ? _D19SplitKey04.GetD19SplitKey04AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19SplitKey04AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19SplitKey04 == null)
        {
            _D19SplitKey04 = new D19SplitKey04();
        }
        _D19SplitKey04.SetD19SplitKey04AsString(value);
    }
    
    // Standard Getter
    public D19SplitKey06 GetD19SplitKey06()
    {
        return _D19SplitKey06;
    }
    
    // Standard Setter
    public void SetD19SplitKey06(D19SplitKey06 value)
    {
        _D19SplitKey06 = value;
    }
    
    // Get<>AsString()
    public string GetD19SplitKey06AsString()
    {
        return _D19SplitKey06 != null ? _D19SplitKey06.GetD19SplitKey06AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19SplitKey06AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19SplitKey06 == null)
        {
            _D19SplitKey06 = new D19SplitKey06();
        }
        _D19SplitKey06.SetD19SplitKey06AsString(value);
    }
    
    // Standard Getter
    public string GetFiller5()
    {
        return _Filler5;
    }
    
    // Standard Setter
    public void SetFiller5(string value)
    {
        _Filler5 = value;
    }
    
    // Get<>AsString()
    public string GetFiller5AsString()
    {
        return _Filler5.PadRight(110);
    }
    
    // Set<>AsString()
    public void SetFiller5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler5 = value;
    }
    
    // Standard Getter
    public D19SplitKey07 GetD19SplitKey07()
    {
        return _D19SplitKey07;
    }
    
    // Standard Setter
    public void SetD19SplitKey07(D19SplitKey07 value)
    {
        _D19SplitKey07 = value;
    }
    
    // Get<>AsString()
    public string GetD19SplitKey07AsString()
    {
        return _D19SplitKey07 != null ? _D19SplitKey07.GetD19SplitKey07AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19SplitKey07AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19SplitKey07 == null)
        {
            _D19SplitKey07 = new D19SplitKey07();
        }
        _D19SplitKey07.SetD19SplitKey07AsString(value);
    }
    
    // Standard Getter
    public D19SplitKey03 GetD19SplitKey03()
    {
        return _D19SplitKey03;
    }
    
    // Standard Setter
    public void SetD19SplitKey03(D19SplitKey03 value)
    {
        _D19SplitKey03 = value;
    }
    
    // Get<>AsString()
    public string GetD19SplitKey03AsString()
    {
        return _D19SplitKey03 != null ? _D19SplitKey03.GetD19SplitKey03AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19SplitKey03AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19SplitKey03 == null)
        {
            _D19SplitKey03 = new D19SplitKey03();
        }
        _D19SplitKey03.SetD19SplitKey03AsString(value);
    }
    
    // Standard Getter
    public string GetFiller6()
    {
        return _Filler6;
    }
    
    // Standard Setter
    public void SetFiller6(string value)
    {
        _Filler6 = value;
    }
    
    // Get<>AsString()
    public string GetFiller6AsString()
    {
        return _Filler6.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler6 = value;
    }
    
    // Standard Getter
    public string GetD19TrancheFlag()
    {
        return _D19TrancheFlag;
    }
    
    // Standard Setter
    public void SetD19TrancheFlag(string value)
    {
        _D19TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD19TrancheFlagAsString()
    {
        return _D19TrancheFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD19TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D19TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetFiller7()
    {
        return _Filler7;
    }
    
    // Standard Setter
    public void SetFiller7(string value)
    {
        _Filler7 = value;
    }
    
    // Get<>AsString()
    public string GetFiller7AsString()
    {
        return _Filler7.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler7 = value;
    }
    
    // Standard Getter
    public string GetFiller8()
    {
        return _Filler8;
    }
    
    // Standard Setter
    public void SetFiller8(string value)
    {
        _Filler8 = value;
    }
    
    // Get<>AsString()
    public string GetFiller8AsString()
    {
        return _Filler8.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler8 = value;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    // Standard Getter
    public D19SplitKey05 GetD19SplitKey05()
    {
        return _D19SplitKey05;
    }
    
    // Standard Setter
    public void SetD19SplitKey05(D19SplitKey05 value)
    {
        _D19SplitKey05 = value;
    }
    
    // Get<>AsString()
    public string GetD19SplitKey05AsString()
    {
        return _D19SplitKey05 != null ? _D19SplitKey05.GetD19SplitKey05AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD19SplitKey05AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D19SplitKey05 == null)
        {
            _D19SplitKey05 = new D19SplitKey05();
        }
        _D19SplitKey05.SetD19SplitKey05AsString(value);
    }
    
    // Standard Getter
    public string GetFiller10()
    {
        return _Filler10;
    }
    
    // Standard Setter
    public void SetFiller10(string value)
    {
        _Filler10 = value;
    }
    
    // Get<>AsString()
    public string GetFiller10AsString()
    {
        return _Filler10.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler10 = value;
    }
    
    // Standard Getter
    public string GetFiller11()
    {
        return _Filler11;
    }
    
    // Standard Setter
    public void SetFiller11(string value)
    {
        _Filler11 = value;
    }
    
    // Get<>AsString()
    public string GetFiller11AsString()
    {
        return _Filler11.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler11 = value;
    }
    
    // Standard Getter
    public string GetFiller12()
    {
        return _Filler12;
    }
    
    // Standard Setter
    public void SetFiller12(string value)
    {
        _Filler12 = value;
    }
    
    // Get<>AsString()
    public string GetFiller12AsString()
    {
        return _Filler12.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler12 = value;
    }
    
    // Standard Getter
    public string GetFiller13()
    {
        return _Filler13;
    }
    
    // Standard Setter
    public void SetFiller13(string value)
    {
        _Filler13 = value;
    }
    
    // Get<>AsString()
    public string GetFiller13AsString()
    {
        return _Filler13.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller13AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler13 = value;
    }
    
    // Standard Getter
    public string GetD19TransactionCategory()
    {
        return _D19TransactionCategory;
    }
    
    // Standard Setter
    public void SetD19TransactionCategory(string value)
    {
        _D19TransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetD19TransactionCategoryAsString()
    {
        return _D19TransactionCategory.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD19TransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D19TransactionCategory = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D19SplitKey01
    public class D19SplitKey01
    {
        private static int _size = 1;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D19CurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _D19CurrencySort =0;
        
        
        // 88-level condition checks for D19CurrencySort
        public bool IsD19CurrencySterling()
        {
            if (this._D19CurrencySort == 0) return true;
            return false;
        }
        public bool IsD19CurrencyEuro()
        {
            if (this._D19CurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: D19CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D19CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D19CountryCode, is_external=, is_static_class=False, static_prefix=
        private string _D19CountryCode ="";
        
        
        
        
    public D19SplitKey01() {}
    
    public D19SplitKey01(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD19CurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetD19CoAcLk(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD19CountryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD19SplitKey01AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D19CurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_D19CoAcLk.PadRight(0));
        result.Append(_D19CountryCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD19SplitKey01AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD19CurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD19CoAcLk(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD19CountryCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD19CurrencySort()
    {
        return _D19CurrencySort;
    }
    
    // Standard Setter
    public void SetD19CurrencySort(int value)
    {
        _D19CurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetD19CurrencySortAsString()
    {
        return _D19CurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD19CurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D19CurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetD19CoAcLk()
    {
        return _D19CoAcLk;
    }
    
    // Standard Setter
    public void SetD19CoAcLk(string value)
    {
        _D19CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD19CoAcLkAsString()
    {
        return _D19CoAcLk.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD19CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D19CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD19CountryCode()
    {
        return _D19CountryCode;
    }
    
    // Standard Setter
    public void SetD19CountryCode(string value)
    {
        _D19CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD19CountryCodeAsString()
    {
        return _D19CountryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD19CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D19CountryCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D19SplitKey02
public class D19SplitKey02
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19MainGroup23, is_external=, is_static_class=False, static_prefix=
    private string _D19MainGroup23 ="";
    
    
    
    
    // [DEBUG] Field: D19SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D19SecurityType ="";
    
    
    
    
    // [DEBUG] Field: D19SecuritySortCode, is_external=, is_static_class=False, static_prefix=
    private string _D19SecuritySortCode ="";
    
    
    
    
    // [DEBUG] Field: D19SedolSort, is_external=, is_static_class=False, static_prefix=
    private string _D19SedolSort ="";
    
    
    
    
    // [DEBUG] Field: D19RecordType, is_external=, is_static_class=False, static_prefix=
    private string _D19RecordType ="";
    
    
    
    
public D19SplitKey02() {}

public D19SplitKey02(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD19MainGroup23(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19SecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19SecuritySortCode(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19SedolSort(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19RecordType(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD19SplitKey02AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19MainGroup23.PadRight(0));
    result.Append(_D19SecurityType.PadRight(0));
    result.Append(_D19SecuritySortCode.PadRight(0));
    result.Append(_D19SedolSort.PadRight(0));
    result.Append(_D19RecordType.PadRight(0));
    
    return result.ToString();
}

public void SetD19SplitKey02AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19MainGroup23(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19SecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19SecuritySortCode(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19SedolSort(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19RecordType(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD19MainGroup23()
{
    return _D19MainGroup23;
}

// Standard Setter
public void SetD19MainGroup23(string value)
{
    _D19MainGroup23 = value;
}

// Get<>AsString()
public string GetD19MainGroup23AsString()
{
    return _D19MainGroup23.PadRight(0);
}

// Set<>AsString()
public void SetD19MainGroup23AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19MainGroup23 = value;
}

// Standard Getter
public string GetD19SecurityType()
{
    return _D19SecurityType;
}

// Standard Setter
public void SetD19SecurityType(string value)
{
    _D19SecurityType = value;
}

// Get<>AsString()
public string GetD19SecurityTypeAsString()
{
    return _D19SecurityType.PadRight(0);
}

// Set<>AsString()
public void SetD19SecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19SecurityType = value;
}

// Standard Getter
public string GetD19SecuritySortCode()
{
    return _D19SecuritySortCode;
}

// Standard Setter
public void SetD19SecuritySortCode(string value)
{
    _D19SecuritySortCode = value;
}

// Get<>AsString()
public string GetD19SecuritySortCodeAsString()
{
    return _D19SecuritySortCode.PadRight(0);
}

// Set<>AsString()
public void SetD19SecuritySortCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19SecuritySortCode = value;
}

// Standard Getter
public string GetD19SedolSort()
{
    return _D19SedolSort;
}

// Standard Setter
public void SetD19SedolSort(string value)
{
    _D19SedolSort = value;
}

// Get<>AsString()
public string GetD19SedolSortAsString()
{
    return _D19SedolSort.PadRight(0);
}

// Set<>AsString()
public void SetD19SedolSortAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19SedolSort = value;
}

// Standard Getter
public string GetD19RecordType()
{
    return _D19RecordType;
}

// Standard Setter
public void SetD19RecordType(string value)
{
    _D19RecordType = value;
}

// Get<>AsString()
public string GetD19RecordTypeAsString()
{
    return _D19RecordType.PadRight(0);
}

// Set<>AsString()
public void SetD19RecordTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19RecordType = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D19SplitKey04
public class D19SplitKey04
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19AcquisitionDate, is_external=, is_static_class=False, static_prefix=
    private D19SplitKey04.D19AcquisitionDate _D19AcquisitionDate = new D19SplitKey04.D19AcquisitionDate();
    
    
    
    
public D19SplitKey04() {}

public D19SplitKey04(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D19AcquisitionDate.SetD19AcquisitionDateAsString(data.Substring(offset, D19AcquisitionDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD19SplitKey04AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19AcquisitionDate.GetD19AcquisitionDateAsString());
    
    return result.ToString();
}

public void SetD19SplitKey04AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        _D19AcquisitionDate.SetD19AcquisitionDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D19AcquisitionDate.SetD19AcquisitionDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public D19AcquisitionDate GetD19AcquisitionDate()
{
    return _D19AcquisitionDate;
}

// Standard Setter
public void SetD19AcquisitionDate(D19AcquisitionDate value)
{
    _D19AcquisitionDate = value;
}

// Get<>AsString()
public string GetD19AcquisitionDateAsString()
{
    return _D19AcquisitionDate != null ? _D19AcquisitionDate.GetD19AcquisitionDateAsString() : "";
}

// Set<>AsString()
public void SetD19AcquisitionDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D19AcquisitionDate == null)
    {
        _D19AcquisitionDate = new D19AcquisitionDate();
    }
    _D19AcquisitionDate.SetD19AcquisitionDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D19AcquisitionDate
public class D19AcquisitionDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19AcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D19AcquisitionDateYy ="";
    
    
    
    
    // [DEBUG] Field: D19AcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D19AcquisitionDateMm ="";
    
    
    
    
    // [DEBUG] Field: D19AcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D19AcquisitionDateDd ="";
    
    
    
    
public D19AcquisitionDate() {}

public D19AcquisitionDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD19AcquisitionDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19AcquisitionDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19AcquisitionDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD19AcquisitionDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19AcquisitionDateYy.PadRight(0));
    result.Append(_D19AcquisitionDateMm.PadRight(0));
    result.Append(_D19AcquisitionDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD19AcquisitionDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19AcquisitionDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19AcquisitionDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19AcquisitionDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD19AcquisitionDateYy()
{
    return _D19AcquisitionDateYy;
}

// Standard Setter
public void SetD19AcquisitionDateYy(string value)
{
    _D19AcquisitionDateYy = value;
}

// Get<>AsString()
public string GetD19AcquisitionDateYyAsString()
{
    return _D19AcquisitionDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD19AcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19AcquisitionDateYy = value;
}

// Standard Getter
public string GetD19AcquisitionDateMm()
{
    return _D19AcquisitionDateMm;
}

// Standard Setter
public void SetD19AcquisitionDateMm(string value)
{
    _D19AcquisitionDateMm = value;
}

// Get<>AsString()
public string GetD19AcquisitionDateMmAsString()
{
    return _D19AcquisitionDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD19AcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19AcquisitionDateMm = value;
}

// Standard Getter
public string GetD19AcquisitionDateDd()
{
    return _D19AcquisitionDateDd;
}

// Standard Setter
public void SetD19AcquisitionDateDd(string value)
{
    _D19AcquisitionDateDd = value;
}

// Get<>AsString()
public string GetD19AcquisitionDateDdAsString()
{
    return _D19AcquisitionDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD19AcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19AcquisitionDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D19SplitKey06
public class D19SplitKey06
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19TrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _D19TrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: D19LineNumber, is_external=, is_static_class=False, static_prefix=
    private string _D19LineNumber ="";
    
    
    
    
    // [DEBUG] Field: D19BdvIndicator, is_external=, is_static_class=False, static_prefix=
    private string _D19BdvIndicator ="";
    
    
    
    
public D19SplitKey06() {}

public D19SplitKey06(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD19TrancheContractNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19LineNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19BdvIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD19SplitKey06AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19TrancheContractNumber.PadRight(0));
    result.Append(_D19LineNumber.PadRight(0));
    result.Append(_D19BdvIndicator.PadRight(0));
    
    return result.ToString();
}

public void SetD19SplitKey06AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19TrancheContractNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19LineNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19BdvIndicator(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD19TrancheContractNumber()
{
    return _D19TrancheContractNumber;
}

// Standard Setter
public void SetD19TrancheContractNumber(string value)
{
    _D19TrancheContractNumber = value;
}

// Get<>AsString()
public string GetD19TrancheContractNumberAsString()
{
    return _D19TrancheContractNumber.PadRight(0);
}

// Set<>AsString()
public void SetD19TrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19TrancheContractNumber = value;
}

// Standard Getter
public string GetD19LineNumber()
{
    return _D19LineNumber;
}

// Standard Setter
public void SetD19LineNumber(string value)
{
    _D19LineNumber = value;
}

// Get<>AsString()
public string GetD19LineNumberAsString()
{
    return _D19LineNumber.PadRight(0);
}

// Set<>AsString()
public void SetD19LineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19LineNumber = value;
}

// Standard Getter
public string GetD19BdvIndicator()
{
    return _D19BdvIndicator;
}

// Standard Setter
public void SetD19BdvIndicator(string value)
{
    _D19BdvIndicator = value;
}

// Get<>AsString()
public string GetD19BdvIndicatorAsString()
{
    return _D19BdvIndicator.PadRight(0);
}

// Set<>AsString()
public void SetD19BdvIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19BdvIndicator = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D19SplitKey07
public class D19SplitKey07
{
    private static int _size = 9;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19SequenceNumber, is_external=, is_static_class=False, static_prefix=
    private int _D19SequenceNumber =0;
    
    
    
    
public D19SplitKey07() {}

public D19SplitKey07(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD19SequenceNumber(int.Parse(data.Substring(offset, 9).Trim()));
    offset += 9;
    
}

// Serialization methods
public string GetD19SplitKey07AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19SequenceNumber.ToString().PadLeft(9, '0'));
    
    return result.ToString();
}

public void SetD19SplitKey07AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD19SequenceNumber(parsedInt);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public int GetD19SequenceNumber()
{
    return _D19SequenceNumber;
}

// Standard Setter
public void SetD19SequenceNumber(int value)
{
    _D19SequenceNumber = value;
}

// Get<>AsString()
public string GetD19SequenceNumberAsString()
{
    return _D19SequenceNumber.ToString().PadLeft(9, '0');
}

// Set<>AsString()
public void SetD19SequenceNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D19SequenceNumber = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D19SplitKey03
public class D19SplitKey03
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19AcquisitionDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D19AcquisitionDateCc ="";
    
    
    
    
public D19SplitKey03() {}

public D19SplitKey03(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD19AcquisitionDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD19SplitKey03AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19AcquisitionDateCc.PadRight(0));
    
    return result.ToString();
}

public void SetD19SplitKey03AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19AcquisitionDateCc(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD19AcquisitionDateCc()
{
    return _D19AcquisitionDateCc;
}

// Standard Setter
public void SetD19AcquisitionDateCc(string value)
{
    _D19AcquisitionDateCc = value;
}

// Get<>AsString()
public string GetD19AcquisitionDateCcAsString()
{
    return _D19AcquisitionDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD19AcquisitionDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19AcquisitionDateCc = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D19SplitKey05
public class D19SplitKey05
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19TaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D19TaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: D19TaperDate, is_external=, is_static_class=False, static_prefix=
    private D19SplitKey05.D19TaperDate _D19TaperDate = new D19SplitKey05.D19TaperDate();
    
    
    
    
public D19SplitKey05() {}

public D19SplitKey05(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD19TaperDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    _D19TaperDate.SetD19TaperDateAsString(data.Substring(offset, D19TaperDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD19SplitKey05AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19TaperDateCc.PadRight(0));
    result.Append(_D19TaperDate.GetD19TaperDateAsString());
    
    return result.ToString();
}

public void SetD19SplitKey05AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19TaperDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _D19TaperDate.SetD19TaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D19TaperDate.SetD19TaperDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD19TaperDateCc()
{
    return _D19TaperDateCc;
}

// Standard Setter
public void SetD19TaperDateCc(string value)
{
    _D19TaperDateCc = value;
}

// Get<>AsString()
public string GetD19TaperDateCcAsString()
{
    return _D19TaperDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD19TaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19TaperDateCc = value;
}

// Standard Getter
public D19TaperDate GetD19TaperDate()
{
    return _D19TaperDate;
}

// Standard Setter
public void SetD19TaperDate(D19TaperDate value)
{
    _D19TaperDate = value;
}

// Get<>AsString()
public string GetD19TaperDateAsString()
{
    return _D19TaperDate != null ? _D19TaperDate.GetD19TaperDateAsString() : "";
}

// Set<>AsString()
public void SetD19TaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D19TaperDate == null)
    {
        _D19TaperDate = new D19TaperDate();
    }
    _D19TaperDate.SetD19TaperDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D19TaperDate
public class D19TaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D19TaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D19TaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: D19TaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D19TaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: D19TaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D19TaperDateDd ="";
    
    
    
    
public D19TaperDate() {}

public D19TaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD19TaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19TaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD19TaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD19TaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D19TaperDateYy.PadRight(0));
    result.Append(_D19TaperDateMm.PadRight(0));
    result.Append(_D19TaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD19TaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19TaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19TaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD19TaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD19TaperDateYy()
{
    return _D19TaperDateYy;
}

// Standard Setter
public void SetD19TaperDateYy(string value)
{
    _D19TaperDateYy = value;
}

// Get<>AsString()
public string GetD19TaperDateYyAsString()
{
    return _D19TaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD19TaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19TaperDateYy = value;
}

// Standard Getter
public string GetD19TaperDateMm()
{
    return _D19TaperDateMm;
}

// Standard Setter
public void SetD19TaperDateMm(string value)
{
    _D19TaperDateMm = value;
}

// Get<>AsString()
public string GetD19TaperDateMmAsString()
{
    return _D19TaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD19TaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19TaperDateMm = value;
}

// Standard Getter
public string GetD19TaperDateDd()
{
    return _D19TaperDateDd;
}

// Standard Setter
public void SetD19TaperDateDd(string value)
{
    _D19TaperDateDd = value;
}

// Get<>AsString()
public string GetD19TaperDateDdAsString()
{
    return _D19TaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD19TaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19TaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}