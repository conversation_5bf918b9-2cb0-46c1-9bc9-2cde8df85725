using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D17Record Data Structure

public class D17Record
{
    private static int _size = 43;
    // [DEBUG] Class: D17Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D17Key, is_external=, is_static_class=False, static_prefix=
    private D17Key _D17Key = new D17Key();
    
    
    
    
    // [DEBUG] Field: D17Action, is_external=, is_static_class=False, static_prefix=
    private string _D17Action ="";
    
    
    
    
    // [DEBUG] Field: D17DateOfAction, is_external=, is_static_class=False, static_prefix=
    private int _D17DateOfAction =0;
    
    
    
    
    // [DEBUG] Field: D17TimeOfAction, is_external=, is_static_class=False, static_prefix=
    private int _D17TimeOfAction =0;
    
    
    
    
    // [DEBUG] Field: D17Userid, is_external=, is_static_class=False, static_prefix=
    private string _D17Userid ="";
    
    
    
    
    
    // Serialization methods
    public string GetD17RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D17Key.GetD17KeyAsString());
        result.Append(_D17Action.PadRight(1));
        result.Append(_D17DateOfAction.ToString().PadLeft(6, '0'));
        result.Append(_D17TimeOfAction.ToString().PadLeft(8, '0'));
        result.Append(_D17Userid.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetD17RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 20 <= data.Length)
        {
            _D17Key.SetD17KeyAsString(data.Substring(offset, 20));
        }
        else
        {
            _D17Key.SetD17KeyAsString(data.Substring(offset));
        }
        offset += 20;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD17Action(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD17DateOfAction(parsedInt);
        }
        offset += 6;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD17TimeOfAction(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD17Userid(extracted);
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD17RecordAsString();
    }
    // Set<>String Override function
    public void SetD17Record(string value)
    {
        SetD17RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D17Key GetD17Key()
    {
        return _D17Key;
    }
    
    // Standard Setter
    public void SetD17Key(D17Key value)
    {
        _D17Key = value;
    }
    
    // Get<>AsString()
    public string GetD17KeyAsString()
    {
        return _D17Key != null ? _D17Key.GetD17KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD17KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D17Key == null)
        {
            _D17Key = new D17Key();
        }
        _D17Key.SetD17KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD17Action()
    {
        return _D17Action;
    }
    
    // Standard Setter
    public void SetD17Action(string value)
    {
        _D17Action = value;
    }
    
    // Get<>AsString()
    public string GetD17ActionAsString()
    {
        return _D17Action.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD17ActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D17Action = value;
    }
    
    // Standard Getter
    public int GetD17DateOfAction()
    {
        return _D17DateOfAction;
    }
    
    // Standard Setter
    public void SetD17DateOfAction(int value)
    {
        _D17DateOfAction = value;
    }
    
    // Get<>AsString()
    public string GetD17DateOfActionAsString()
    {
        return _D17DateOfAction.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD17DateOfActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D17DateOfAction = parsed;
    }
    
    // Standard Getter
    public int GetD17TimeOfAction()
    {
        return _D17TimeOfAction;
    }
    
    // Standard Setter
    public void SetD17TimeOfAction(int value)
    {
        _D17TimeOfAction = value;
    }
    
    // Get<>AsString()
    public string GetD17TimeOfActionAsString()
    {
        return _D17TimeOfAction.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetD17TimeOfActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D17TimeOfAction = parsed;
    }
    
    // Standard Getter
    public string GetD17Userid()
    {
        return _D17Userid;
    }
    
    // Standard Setter
    public void SetD17Userid(string value)
    {
        _D17Userid = value;
    }
    
    // Get<>AsString()
    public string GetD17UseridAsString()
    {
        return _D17Userid.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD17UseridAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D17Userid = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD17Key(string value)
    {
        _D17Key.SetD17KeyAsString(value);
    }
    // Nested Class: D17Key
    public class D17Key
    {
        private static int _size = 20;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D17Year, is_external=, is_static_class=False, static_prefix=
        private int _D17Year =0;
        
        
        
        
        // [DEBUG] Field: D17InvertedDate, is_external=, is_static_class=False, static_prefix=
        private int _D17InvertedDate =0;
        
        
        
        
        // [DEBUG] Field: D17InvertedTime, is_external=, is_static_class=False, static_prefix=
        private int _D17InvertedTime =0;
        
        
        
        
        // [DEBUG] Field: D17SubFundCode, is_external=, is_static_class=False, static_prefix=
        private string _D17SubFundCode ="";
        
        
        
        
    public D17Key() {}
    
    public D17Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD17Year(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD17InvertedDate(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        SetD17InvertedTime(int.Parse(data.Substring(offset, 8).Trim()));
        offset += 8;
        SetD17SubFundCode(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetD17KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D17Year.ToString().PadLeft(2, '0'));
        result.Append(_D17InvertedDate.ToString().PadLeft(6, '0'));
        result.Append(_D17InvertedTime.ToString().PadLeft(8, '0'));
        result.Append(_D17SubFundCode.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD17KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD17Year(parsedInt);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD17InvertedDate(parsedInt);
        }
        offset += 6;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD17InvertedTime(parsedInt);
        }
        offset += 8;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD17SubFundCode(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD17Year()
    {
        return _D17Year;
    }
    
    // Standard Setter
    public void SetD17Year(int value)
    {
        _D17Year = value;
    }
    
    // Get<>AsString()
    public string GetD17YearAsString()
    {
        return _D17Year.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD17YearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D17Year = parsed;
    }
    
    // Standard Getter
    public int GetD17InvertedDate()
    {
        return _D17InvertedDate;
    }
    
    // Standard Setter
    public void SetD17InvertedDate(int value)
    {
        _D17InvertedDate = value;
    }
    
    // Get<>AsString()
    public string GetD17InvertedDateAsString()
    {
        return _D17InvertedDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD17InvertedDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D17InvertedDate = parsed;
    }
    
    // Standard Getter
    public int GetD17InvertedTime()
    {
        return _D17InvertedTime;
    }
    
    // Standard Setter
    public void SetD17InvertedTime(int value)
    {
        _D17InvertedTime = value;
    }
    
    // Get<>AsString()
    public string GetD17InvertedTimeAsString()
    {
        return _D17InvertedTime.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetD17InvertedTimeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D17InvertedTime = parsed;
    }
    
    // Standard Getter
    public string GetD17SubFundCode()
    {
        return _D17SubFundCode;
    }
    
    // Standard Setter
    public void SetD17SubFundCode(string value)
    {
        _D17SubFundCode = value;
    }
    
    // Get<>AsString()
    public string GetD17SubFundCodeAsString()
    {
        return _D17SubFundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD17SubFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D17SubFundCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}