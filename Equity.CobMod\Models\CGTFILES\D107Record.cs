using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D107Record Data Structure

public class D107Record
{
    private static int _size = 200;
    // [DEBUG] Class: D107Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler154, is_external=, is_static_class=False, static_prefix=
    private string _Filler154 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD107RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler154.PadRight(200));
        
        return result.ToString();
    }
    
    public void SetD107RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 200 <= data.Length)
        {
            string extracted = data.Substring(offset, 200).Trim();
            SetFiller154(extracted);
        }
        offset += 200;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD107RecordAsString();
    }
    // Set<>String Override function
    public void SetD107Record(string value)
    {
        SetD107RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller154()
    {
        return _Filler154;
    }
    
    // Standard Setter
    public void SetFiller154(string value)
    {
        _Filler154 = value;
    }
    
    // Get<>AsString()
    public string GetFiller154AsString()
    {
        return _Filler154.PadRight(200);
    }
    
    // Set<>AsString()
    public void SetFiller154AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler154 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}