using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D108Record Data Structure

public class D108Record
{
    private static int _size = 80;
    // [DEBUG] Class: D108Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler155, is_external=, is_static_class=False, static_prefix=
    private string _Filler155 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD108RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler155.PadRight(80));
        
        return result.ToString();
    }
    
    public void SetD108RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 80 <= data.Length)
        {
            string extracted = data.Substring(offset, 80).Trim();
            SetFiller155(extracted);
        }
        offset += 80;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD108RecordAsString();
    }
    // Set<>String Override function
    public void SetD108Record(string value)
    {
        SetD108RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller155()
    {
        return _Filler155;
    }
    
    // Standard Setter
    public void SetFiller155(string value)
    {
        _Filler155 = value;
    }
    
    // Get<>AsString()
    public string GetFiller155AsString()
    {
        return _Filler155.PadRight(80);
    }
    
    // Set<>AsString()
    public void SetFiller155AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler155 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}