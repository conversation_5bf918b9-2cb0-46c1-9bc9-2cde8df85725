using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing Cgtdate2LinkageDate7 Data Structure

public class Cgtdate2LinkageDate7
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate7, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd7 =0;
    
    
    
    
    // [DEBUG] Field: Filler388, is_external=, is_static_class=False, static_prefix=
    private Filler388 _Filler388 = new Filler388();
    
    
    
    
    // [DEBUG] Field: Filler389, is_external=, is_static_class=False, static_prefix=
    private Filler389 _Filler389 = new Filler389();
    
    
    
    
    // [DEBUG] Field: Filler390, is_external=, is_static_class=False, static_prefix=
    private Filler390 _Filler390 = new Filler390();
    
    
    
    
    // [DEBUG] Field: Filler391, is_external=, is_static_class=False, static_prefix=
    private Filler391 _Filler391 = new Filler391();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0'));
        result.Append(_Filler388.GetFiller388AsString());
        result.Append(_Filler389.GetFiller389AsString());
        result.Append(_Filler390.GetFiller390AsString());
        result.Append(_Filler391.GetFiller391AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd7(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler388.SetFiller388AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler388.SetFiller388AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler389.SetFiller389AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler389.SetFiller389AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler390.SetFiller390AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler390.SetFiller390AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler391.SetFiller391AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler391.SetFiller391AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate7AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate7(string value)
    {
        SetCgtdate2LinkageDate7AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd7()
    {
        return _Cgtdate2Ccyymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd7(int value)
    {
        _Cgtdate2Ccyymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd7AsString()
    {
        return _Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd7 = parsed;
    }
    
    // Standard Getter
    public Filler388 GetFiller388()
    {
        return _Filler388;
    }
    
    // Standard Setter
    public void SetFiller388(Filler388 value)
    {
        _Filler388 = value;
    }
    
    // Get<>AsString()
    public string GetFiller388AsString()
    {
        return _Filler388 != null ? _Filler388.GetFiller388AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller388AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler388 == null)
        {
            _Filler388 = new Filler388();
        }
        _Filler388.SetFiller388AsString(value);
    }
    
    // Standard Getter
    public Filler389 GetFiller389()
    {
        return _Filler389;
    }
    
    // Standard Setter
    public void SetFiller389(Filler389 value)
    {
        _Filler389 = value;
    }
    
    // Get<>AsString()
    public string GetFiller389AsString()
    {
        return _Filler389 != null ? _Filler389.GetFiller389AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller389AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler389 == null)
        {
            _Filler389 = new Filler389();
        }
        _Filler389.SetFiller389AsString(value);
    }
    
    // Standard Getter
    public Filler390 GetFiller390()
    {
        return _Filler390;
    }
    
    // Standard Setter
    public void SetFiller390(Filler390 value)
    {
        _Filler390 = value;
    }
    
    // Get<>AsString()
    public string GetFiller390AsString()
    {
        return _Filler390 != null ? _Filler390.GetFiller390AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller390AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler390 == null)
        {
            _Filler390 = new Filler390();
        }
        _Filler390.SetFiller390AsString(value);
    }
    
    // Standard Getter
    public Filler391 GetFiller391()
    {
        return _Filler391;
    }
    
    // Standard Setter
    public void SetFiller391(Filler391 value)
    {
        _Filler391 = value;
    }
    
    // Get<>AsString()
    public string GetFiller391AsString()
    {
        return _Filler391 != null ? _Filler391.GetFiller391AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller391AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler391 == null)
        {
            _Filler391 = new Filler391();
        }
        _Filler391.SetFiller391AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller388(string value)
    {
        _Filler388.SetFiller388AsString(value);
    }
    // Nested Class: Filler388
    public class Filler388
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd7, is_external=, is_static_class=False, static_prefix=
        private Filler388.Cgtdate2Yymmdd7 _Cgtdate2Yymmdd7 = new Filler388.Cgtdate2Yymmdd7();
        
        
        
        
    public Filler388() {}
    
    public Filler388(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, Cgtdate2Yymmdd7.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller388AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc7.PadRight(2));
        result.Append(_Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetFiller388AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc7(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc7()
    {
        return _Cgtdate2Cc7;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc7(string value)
    {
        _Cgtdate2Cc7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc7AsString()
    {
        return _Cgtdate2Cc7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd7 GetCgtdate2Yymmdd7()
    {
        return _Cgtdate2Yymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd7(Cgtdate2Yymmdd7 value)
    {
        _Cgtdate2Yymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd7AsString()
    {
        return _Cgtdate2Yymmdd7 != null ? _Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd7 == null)
        {
            _Cgtdate2Yymmdd7 = new Cgtdate2Yymmdd7();
        }
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd7
    public class Cgtdate2Yymmdd7
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd7, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd7.Cgtdate2Mmdd7 _Cgtdate2Mmdd7 = new Cgtdate2Yymmdd7.Cgtdate2Mmdd7();
        
        
        
        
    public Cgtdate2Yymmdd7() {}
    
    public Cgtdate2Yymmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, Cgtdate2Mmdd7.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy7.PadRight(2));
        result.Append(_Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy7(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy7()
    {
        return _Cgtdate2Yy7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy7(string value)
    {
        _Cgtdate2Yy7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy7AsString()
    {
        return _Cgtdate2Yy7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd7 GetCgtdate2Mmdd7()
    {
        return _Cgtdate2Mmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd7(Cgtdate2Mmdd7 value)
    {
        _Cgtdate2Mmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd7AsString()
    {
        return _Cgtdate2Mmdd7 != null ? _Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd7 == null)
        {
            _Cgtdate2Mmdd7 = new Cgtdate2Mmdd7();
        }
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd7
    public class Cgtdate2Mmdd7
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd7 ="";
        
        
        
        
    public Cgtdate2Mmdd7() {}
    
    public Cgtdate2Mmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm7(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd7(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm7.PadRight(2));
        result.Append(_Cgtdate2Dd7.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm7(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd7(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm7()
    {
        return _Cgtdate2Mm7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm7(string value)
    {
        _Cgtdate2Mm7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm7AsString()
    {
        return _Cgtdate2Mm7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm7 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd7()
    {
        return _Cgtdate2Dd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd7(string value)
    {
        _Cgtdate2Dd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd7AsString()
    {
        return _Cgtdate2Dd7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd7 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller389(string value)
{
    _Filler389.SetFiller389AsString(value);
}
// Nested Class: Filler389
public class Filler389
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd7 =0;
    
    
    
    
public Filler389() {}

public Filler389(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller389AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy7.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd7.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller389AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy7(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd7(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy7()
{
    return _Cgtdate2CCcyy7;
}

// Standard Setter
public void SetCgtdate2CCcyy7(int value)
{
    _Cgtdate2CCcyy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy7AsString()
{
    return _Cgtdate2CCcyy7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd7()
{
    return _Cgtdate2CMmdd7;
}

// Standard Setter
public void SetCgtdate2CMmdd7(int value)
{
    _Cgtdate2CMmdd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd7AsString()
{
    return _Cgtdate2CMmdd7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller390(string value)
{
    _Filler390.SetFiller390AsString(value);
}
// Nested Class: Filler390
public class Filler390
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd7 =0;
    
    
    
    
public Filler390() {}

public Filler390(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller390AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd7.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller390AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd7(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc7()
{
    return _Cgtdate2CCc7;
}

// Standard Setter
public void SetCgtdate2CCc7(int value)
{
    _Cgtdate2CCc7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc7AsString()
{
    return _Cgtdate2CCc7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc7 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy7()
{
    return _Cgtdate2CYy7;
}

// Standard Setter
public void SetCgtdate2CYy7(int value)
{
    _Cgtdate2CYy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy7AsString()
{
    return _Cgtdate2CYy7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm7()
{
    return _Cgtdate2CMm7;
}

// Standard Setter
public void SetCgtdate2CMm7(int value)
{
    _Cgtdate2CMm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm7AsString()
{
    return _Cgtdate2CMm7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm7 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd7()
{
    return _Cgtdate2CDd7;
}

// Standard Setter
public void SetCgtdate2CDd7(int value)
{
    _Cgtdate2CDd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd7AsString()
{
    return _Cgtdate2CDd7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller391(string value)
{
    _Filler391.SetFiller391AsString(value);
}
// Nested Class: Filler391
public class Filler391
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm7 =0;
    
    
    
    
    // [DEBUG] Field: Filler392, is_external=, is_static_class=False, static_prefix=
    private string _Filler392 ="";
    
    
    
    
public Filler391() {}

public Filler391(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm7(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller392(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller391AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm7.ToString().PadLeft(6, '0'));
    result.Append(_Filler392.PadRight(2));
    
    return result.ToString();
}

public void SetFiller391AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm7(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller392(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm7()
{
    return _Cgtdate2CCcyymm7;
}

// Standard Setter
public void SetCgtdate2CCcyymm7(int value)
{
    _Cgtdate2CCcyymm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm7AsString()
{
    return _Cgtdate2CCcyymm7.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm7 = parsed;
}

// Standard Getter
public string GetFiller392()
{
    return _Filler392;
}

// Standard Setter
public void SetFiller392(string value)
{
    _Filler392 = value;
}

// Get<>AsString()
public string GetFiller392AsString()
{
    return _Filler392.PadRight(2);
}

// Set<>AsString()
public void SetFiller392AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler392 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}