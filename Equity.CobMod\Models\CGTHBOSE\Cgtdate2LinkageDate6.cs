using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgthboseDTO
{// DTO class representing Cgtdate2LinkageDate6 Data Structure

public class Cgtdate2LinkageDate6
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate6, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd6 =0;
    
    
    
    
    // [DEBUG] Field: Filler51, is_external=, is_static_class=False, static_prefix=
    private Filler51 _Filler51 = new Filler51();
    
    
    
    
    // [DEBUG] Field: Filler52, is_external=, is_static_class=False, static_prefix=
    private Filler52 _Filler52 = new Filler52();
    
    
    
    
    // [DEBUG] Field: Filler53, is_external=, is_static_class=False, static_prefix=
    private Filler53 _Filler53 = new Filler53();
    
    
    
    
    // [DEBUG] Field: Filler54, is_external=, is_static_class=False, static_prefix=
    private Filler54 _Filler54 = new Filler54();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0'));
        result.Append(_Filler51.GetFiller51AsString());
        result.Append(_Filler52.GetFiller52AsString());
        result.Append(_Filler53.GetFiller53AsString());
        result.Append(_Filler54.GetFiller54AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd6(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler51.SetFiller51AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler51.SetFiller51AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler52.SetFiller52AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler52.SetFiller52AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler53.SetFiller53AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler53.SetFiller53AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler54.SetFiller54AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler54.SetFiller54AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate6AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate6(string value)
    {
        SetCgtdate2LinkageDate6AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd6()
    {
        return _Cgtdate2Ccyymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd6(int value)
    {
        _Cgtdate2Ccyymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd6AsString()
    {
        return _Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd6 = parsed;
    }
    
    // Standard Getter
    public Filler51 GetFiller51()
    {
        return _Filler51;
    }
    
    // Standard Setter
    public void SetFiller51(Filler51 value)
    {
        _Filler51 = value;
    }
    
    // Get<>AsString()
    public string GetFiller51AsString()
    {
        return _Filler51 != null ? _Filler51.GetFiller51AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller51AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler51 == null)
        {
            _Filler51 = new Filler51();
        }
        _Filler51.SetFiller51AsString(value);
    }
    
    // Standard Getter
    public Filler52 GetFiller52()
    {
        return _Filler52;
    }
    
    // Standard Setter
    public void SetFiller52(Filler52 value)
    {
        _Filler52 = value;
    }
    
    // Get<>AsString()
    public string GetFiller52AsString()
    {
        return _Filler52 != null ? _Filler52.GetFiller52AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller52AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler52 == null)
        {
            _Filler52 = new Filler52();
        }
        _Filler52.SetFiller52AsString(value);
    }
    
    // Standard Getter
    public Filler53 GetFiller53()
    {
        return _Filler53;
    }
    
    // Standard Setter
    public void SetFiller53(Filler53 value)
    {
        _Filler53 = value;
    }
    
    // Get<>AsString()
    public string GetFiller53AsString()
    {
        return _Filler53 != null ? _Filler53.GetFiller53AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller53AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler53 == null)
        {
            _Filler53 = new Filler53();
        }
        _Filler53.SetFiller53AsString(value);
    }
    
    // Standard Getter
    public Filler54 GetFiller54()
    {
        return _Filler54;
    }
    
    // Standard Setter
    public void SetFiller54(Filler54 value)
    {
        _Filler54 = value;
    }
    
    // Get<>AsString()
    public string GetFiller54AsString()
    {
        return _Filler54 != null ? _Filler54.GetFiller54AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller54AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler54 == null)
        {
            _Filler54 = new Filler54();
        }
        _Filler54.SetFiller54AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller51(string value)
    {
        _Filler51.SetFiller51AsString(value);
    }
    // Nested Class: Filler51
    public class Filler51
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd6, is_external=, is_static_class=False, static_prefix=
        private Filler51.Cgtdate2Yymmdd6 _Cgtdate2Yymmdd6 = new Filler51.Cgtdate2Yymmdd6();
        
        
        
        
    public Filler51() {}
    
    public Filler51(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, Cgtdate2Yymmdd6.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller51AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc6.PadRight(2));
        result.Append(_Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetFiller51AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc6(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc6()
    {
        return _Cgtdate2Cc6;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc6(string value)
    {
        _Cgtdate2Cc6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc6AsString()
    {
        return _Cgtdate2Cc6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd6 GetCgtdate2Yymmdd6()
    {
        return _Cgtdate2Yymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd6(Cgtdate2Yymmdd6 value)
    {
        _Cgtdate2Yymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd6AsString()
    {
        return _Cgtdate2Yymmdd6 != null ? _Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd6 == null)
        {
            _Cgtdate2Yymmdd6 = new Cgtdate2Yymmdd6();
        }
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd6
    public class Cgtdate2Yymmdd6
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd6, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd6.Cgtdate2Mmdd6 _Cgtdate2Mmdd6 = new Cgtdate2Yymmdd6.Cgtdate2Mmdd6();
        
        
        
        
    public Cgtdate2Yymmdd6() {}
    
    public Cgtdate2Yymmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, Cgtdate2Mmdd6.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy6.PadRight(2));
        result.Append(_Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy6(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy6()
    {
        return _Cgtdate2Yy6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy6(string value)
    {
        _Cgtdate2Yy6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy6AsString()
    {
        return _Cgtdate2Yy6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd6 GetCgtdate2Mmdd6()
    {
        return _Cgtdate2Mmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd6(Cgtdate2Mmdd6 value)
    {
        _Cgtdate2Mmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd6AsString()
    {
        return _Cgtdate2Mmdd6 != null ? _Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd6 == null)
        {
            _Cgtdate2Mmdd6 = new Cgtdate2Mmdd6();
        }
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd6
    public class Cgtdate2Mmdd6
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd6 ="";
        
        
        
        
    public Cgtdate2Mmdd6() {}
    
    public Cgtdate2Mmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm6(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd6(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm6.PadRight(2));
        result.Append(_Cgtdate2Dd6.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm6(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd6(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm6()
    {
        return _Cgtdate2Mm6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm6(string value)
    {
        _Cgtdate2Mm6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm6AsString()
    {
        return _Cgtdate2Mm6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm6 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd6()
    {
        return _Cgtdate2Dd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd6(string value)
    {
        _Cgtdate2Dd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd6AsString()
    {
        return _Cgtdate2Dd6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd6 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller52(string value)
{
    _Filler52.SetFiller52AsString(value);
}
// Nested Class: Filler52
public class Filler52
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd6 =0;
    
    
    
    
public Filler52() {}

public Filler52(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller52AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy6.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd6.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller52AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy6(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd6(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy6()
{
    return _Cgtdate2CCcyy6;
}

// Standard Setter
public void SetCgtdate2CCcyy6(int value)
{
    _Cgtdate2CCcyy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy6AsString()
{
    return _Cgtdate2CCcyy6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd6()
{
    return _Cgtdate2CMmdd6;
}

// Standard Setter
public void SetCgtdate2CMmdd6(int value)
{
    _Cgtdate2CMmdd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd6AsString()
{
    return _Cgtdate2CMmdd6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller53(string value)
{
    _Filler53.SetFiller53AsString(value);
}
// Nested Class: Filler53
public class Filler53
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd6 =0;
    
    
    
    
public Filler53() {}

public Filler53(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller53AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd6.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller53AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd6(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc6()
{
    return _Cgtdate2CCc6;
}

// Standard Setter
public void SetCgtdate2CCc6(int value)
{
    _Cgtdate2CCc6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc6AsString()
{
    return _Cgtdate2CCc6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc6 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy6()
{
    return _Cgtdate2CYy6;
}

// Standard Setter
public void SetCgtdate2CYy6(int value)
{
    _Cgtdate2CYy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy6AsString()
{
    return _Cgtdate2CYy6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm6()
{
    return _Cgtdate2CMm6;
}

// Standard Setter
public void SetCgtdate2CMm6(int value)
{
    _Cgtdate2CMm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm6AsString()
{
    return _Cgtdate2CMm6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm6 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd6()
{
    return _Cgtdate2CDd6;
}

// Standard Setter
public void SetCgtdate2CDd6(int value)
{
    _Cgtdate2CDd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd6AsString()
{
    return _Cgtdate2CDd6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller54(string value)
{
    _Filler54.SetFiller54AsString(value);
}
// Nested Class: Filler54
public class Filler54
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm6 =0;
    
    
    
    
    // [DEBUG] Field: Filler55, is_external=, is_static_class=False, static_prefix=
    private string _Filler55 ="";
    
    
    
    
public Filler54() {}

public Filler54(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm6(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller55(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller54AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm6.ToString().PadLeft(6, '0'));
    result.Append(_Filler55.PadRight(2));
    
    return result.ToString();
}

public void SetFiller54AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm6(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller55(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm6()
{
    return _Cgtdate2CCcyymm6;
}

// Standard Setter
public void SetCgtdate2CCcyymm6(int value)
{
    _Cgtdate2CCcyymm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm6AsString()
{
    return _Cgtdate2CCcyymm6.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm6 = parsed;
}

// Standard Getter
public string GetFiller55()
{
    return _Filler55;
}

// Standard Setter
public void SetFiller55(string value)
{
    _Filler55 = value;
}

// Get<>AsString()
public string GetFiller55AsString()
{
    return _Filler55.PadRight(2);
}

// Set<>AsString()
public void SetFiller55AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler55 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}