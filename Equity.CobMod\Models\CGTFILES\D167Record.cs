using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D167Record Data Structure

public class D167Record
{
    private static int _size = 33;
    // [DEBUG] Class: D167Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D167Key, is_external=, is_static_class=False, static_prefix=
    private D167Key _D167Key = new D167Key();
    
    
    
    
    // [DEBUG] Field: D167Description, is_external=, is_static_class=False, static_prefix=
    private string _D167Description ="";
    
    
    
    
    // [DEBUG] Field: D167InverseKey, is_external=, is_static_class=False, static_prefix=
    private string _D167InverseKey ="";
    
    
    
    
    
    // Serialization methods
    public string GetD167RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D167Key.GetD167KeyAsString());
        result.Append(_D167Description.PadRight(30));
        result.Append(_D167InverseKey.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD167RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            _D167Key.SetD167KeyAsString(data.Substring(offset, 3));
        }
        else
        {
            _D167Key.SetD167KeyAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD167Description(extracted);
        }
        offset += 30;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD167InverseKey(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD167RecordAsString();
    }
    // Set<>String Override function
    public void SetD167Record(string value)
    {
        SetD167RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D167Key GetD167Key()
    {
        return _D167Key;
    }
    
    // Standard Setter
    public void SetD167Key(D167Key value)
    {
        _D167Key = value;
    }
    
    // Get<>AsString()
    public string GetD167KeyAsString()
    {
        return _D167Key != null ? _D167Key.GetD167KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD167KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D167Key == null)
        {
            _D167Key = new D167Key();
        }
        _D167Key.SetD167KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD167Description()
    {
        return _D167Description;
    }
    
    // Standard Setter
    public void SetD167Description(string value)
    {
        _D167Description = value;
    }
    
    // Get<>AsString()
    public string GetD167DescriptionAsString()
    {
        return _D167Description.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD167DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D167Description = value;
    }
    
    // Standard Getter
    public string GetD167InverseKey()
    {
        return _D167InverseKey;
    }
    
    // Standard Setter
    public void SetD167InverseKey(string value)
    {
        _D167InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD167InverseKeyAsString()
    {
        return _D167InverseKey.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD167InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D167InverseKey = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD167Key(string value)
    {
        _D167Key.SetD167KeyAsString(value);
    }
    // Nested Class: D167Key
    public class D167Key
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D167PriceTypeCode, is_external=, is_static_class=False, static_prefix=
        private string _D167PriceTypeCode ="";
        
        
        
        
        // [DEBUG] Field: D167SequenceCode, is_external=, is_static_class=False, static_prefix=
        private int _D167SequenceCode =0;
        
        
        
        
    public D167Key() {}
    
    public D167Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD167PriceTypeCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD167SequenceCode(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetD167KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D167PriceTypeCode.PadRight(0));
        result.Append(_D167SequenceCode.ToString().PadLeft(3, '0'));
        
        return result.ToString();
    }
    
    public void SetD167KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD167PriceTypeCode(extracted);
        }
        offset += 0;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD167SequenceCode(parsedInt);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD167PriceTypeCode()
    {
        return _D167PriceTypeCode;
    }
    
    // Standard Setter
    public void SetD167PriceTypeCode(string value)
    {
        _D167PriceTypeCode = value;
    }
    
    // Get<>AsString()
    public string GetD167PriceTypeCodeAsString()
    {
        return _D167PriceTypeCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD167PriceTypeCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D167PriceTypeCode = value;
    }
    
    // Standard Getter
    public int GetD167SequenceCode()
    {
        return _D167SequenceCode;
    }
    
    // Standard Setter
    public void SetD167SequenceCode(int value)
    {
        _D167SequenceCode = value;
    }
    
    // Get<>AsString()
    public string GetD167SequenceCodeAsString()
    {
        return _D167SequenceCode.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetD167SequenceCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D167SequenceCode = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}