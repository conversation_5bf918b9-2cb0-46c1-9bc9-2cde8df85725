using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D26File Data Structure

public class D26File
{
    private static int _size = 12;
    // [DEBUG] Class: D26File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler193, is_external=, is_static_class=False, static_prefix=
    private string _Filler193 ="$";
    
    
    
    
    // [DEBUG] Field: D26UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D26UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler194, is_external=, is_static_class=False, static_prefix=
    private string _Filler194 ="R7";
    
    
    
    
    // [DEBUG] Field: D26ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D26ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler195, is_external=, is_static_class=False, static_prefix=
    private string _Filler195 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD26FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler193.PadRight(1));
        result.Append(_D26UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler194.PadRight(2));
        result.Append(_D26ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler195.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD26FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller193(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD26UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller194(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD26ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller195(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD26FileAsString();
    }
    // Set<>String Override function
    public void SetD26File(string value)
    {
        SetD26FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller193()
    {
        return _Filler193;
    }
    
    // Standard Setter
    public void SetFiller193(string value)
    {
        _Filler193 = value;
    }
    
    // Get<>AsString()
    public string GetFiller193AsString()
    {
        return _Filler193.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller193AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler193 = value;
    }
    
    // Standard Getter
    public int GetD26UserNo()
    {
        return _D26UserNo;
    }
    
    // Standard Setter
    public void SetD26UserNo(int value)
    {
        _D26UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD26UserNoAsString()
    {
        return _D26UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD26UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D26UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller194()
    {
        return _Filler194;
    }
    
    // Standard Setter
    public void SetFiller194(string value)
    {
        _Filler194 = value;
    }
    
    // Get<>AsString()
    public string GetFiller194AsString()
    {
        return _Filler194.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller194AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler194 = value;
    }
    
    // Standard Getter
    public int GetD26ReportNo()
    {
        return _D26ReportNo;
    }
    
    // Standard Setter
    public void SetD26ReportNo(int value)
    {
        _D26ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD26ReportNoAsString()
    {
        return _D26ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD26ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D26ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller195()
    {
        return _Filler195;
    }
    
    // Standard Setter
    public void SetFiller195(string value)
    {
        _Filler195 = value;
    }
    
    // Get<>AsString()
    public string GetFiller195AsString()
    {
        return _Filler195.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller195AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler195 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}