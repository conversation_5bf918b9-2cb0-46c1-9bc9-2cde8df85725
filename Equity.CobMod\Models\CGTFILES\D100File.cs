using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D100File Data Structure

public class D100File
{
    private static int _size = 12;
    // [DEBUG] Class: D100File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler288, is_external=, is_static_class=False, static_prefix=
    private string _Filler288 ="$";
    
    
    
    
    // [DEBUG] Field: D100UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D100UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler289, is_external=, is_static_class=False, static_prefix=
    private string _Filler289 ="RP";
    
    
    
    
    // [DEBUG] Field: D100ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D100ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler290, is_external=, is_static_class=False, static_prefix=
    private string _Filler290 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD100FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler288.PadRight(1));
        result.Append(_D100UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler289.PadRight(2));
        result.Append(_D100ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler290.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD100FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller288(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD100UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller289(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD100ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller290(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD100FileAsString();
    }
    // Set<>String Override function
    public void SetD100File(string value)
    {
        SetD100FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller288()
    {
        return _Filler288;
    }
    
    // Standard Setter
    public void SetFiller288(string value)
    {
        _Filler288 = value;
    }
    
    // Get<>AsString()
    public string GetFiller288AsString()
    {
        return _Filler288.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller288AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler288 = value;
    }
    
    // Standard Getter
    public int GetD100UserNo()
    {
        return _D100UserNo;
    }
    
    // Standard Setter
    public void SetD100UserNo(int value)
    {
        _D100UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD100UserNoAsString()
    {
        return _D100UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD100UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D100UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller289()
    {
        return _Filler289;
    }
    
    // Standard Setter
    public void SetFiller289(string value)
    {
        _Filler289 = value;
    }
    
    // Get<>AsString()
    public string GetFiller289AsString()
    {
        return _Filler289.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller289AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler289 = value;
    }
    
    // Standard Getter
    public int GetD100ReportNo()
    {
        return _D100ReportNo;
    }
    
    // Standard Setter
    public void SetD100ReportNo(int value)
    {
        _D100ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD100ReportNoAsString()
    {
        return _D100ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD100ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D100ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller290()
    {
        return _Filler290;
    }
    
    // Standard Setter
    public void SetFiller290(string value)
    {
        _Filler290 = value;
    }
    
    // Get<>AsString()
    public string GetFiller290AsString()
    {
        return _Filler290.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller290AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler290 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}