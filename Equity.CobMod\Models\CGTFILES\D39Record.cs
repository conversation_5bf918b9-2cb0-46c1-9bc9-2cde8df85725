using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D39Record Data Structure

public class D39Record
{
    private static int _size = 8;
    // [DEBUG] Class: D39Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D39Key, is_external=, is_static_class=False, static_prefix=
    private D39Key _D39Key = new D39Key();
    
    
    
    
    
    // Serialization methods
    public string GetD39RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D39Key.GetD39KeyAsString());
        
        return result.ToString();
    }
    
    public void SetD39RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            _D39Key.SetD39KeyAsString(data.Substring(offset, 8));
        }
        else
        {
            _D39Key.SetD39KeyAsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD39RecordAsString();
    }
    // Set<>String Override function
    public void SetD39Record(string value)
    {
        SetD39RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D39Key GetD39Key()
    {
        return _D39Key;
    }
    
    // Standard Setter
    public void SetD39Key(D39Key value)
    {
        _D39Key = value;
    }
    
    // Get<>AsString()
    public string GetD39KeyAsString()
    {
        return _D39Key != null ? _D39Key.GetD39KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD39KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D39Key == null)
        {
            _D39Key = new D39Key();
        }
        _D39Key.SetD39KeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD39Key(string value)
    {
        _D39Key.SetD39KeyAsString(value);
    }
    // Nested Class: D39Key
    public class D39Key
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D39Program, is_external=, is_static_class=False, static_prefix=
        private string _D39Program ="";
        
        
        
        
    public D39Key() {}
    
    public D39Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD39Program(data.Substring(offset, 8).Trim());
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetD39KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D39Program.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetD39KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD39Program(extracted);
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD39Program()
    {
        return _D39Program;
    }
    
    // Standard Setter
    public void SetD39Program(string value)
    {
        _D39Program = value;
    }
    
    // Get<>AsString()
    public string GetD39ProgramAsString()
    {
        return _D39Program.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD39ProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D39Program = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}