using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WttcCosts Data Structure

public class WttcCosts
{
    private static int _size = 1188026;
    // [DEBUG] Class: WttcCosts, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler473, is_external=, is_static_class=False, static_prefix=
    private string _Filler473 ="COSTS===========";
    
    
    
    
    // [DEBUG] Field: WttcMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WttcMaxTableSize =12000;
    
    
    
    
    // [DEBUG] Field: WttcOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WttcOccurs =0;
    
    
    
    
    // [DEBUG] Field: WttcElement, is_external=, is_static_class=False, static_prefix=
    private WttcElement[] _WttcElement = new WttcElement[12000];
    
    public void InitializeWttcElementArray()
    {
        for (int i = 0; i < 12000; i++)
        {
            _WttcElement[i] = new WttcElement();
        }
    }
    
    
    
    
    // Serialization methods
    public string GetWttcCostsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler473.PadRight(16));
        result.Append(_WttcMaxTableSize.ToString().PadLeft(5, '0'));
        result.Append(_WttcOccurs.ToString().PadLeft(5, '0'));
        for (int i = 0; i < 12000; i++)
        {
            result.Append(_WttcElement[i].GetWttcElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWttcCostsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller473(extracted);
        }
        offset += 16;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttcMaxTableSize(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttcOccurs(parsedInt);
        }
        offset += 5;
        for (int i = 0; i < 12000; i++)
        {
            if (offset + 99 > data.Length) break;
            string val = data.Substring(offset, 99);
            
            _WttcElement[i].SetWttcElementAsString(val);
            offset += 99;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWttcCostsAsString();
    }
    // Set<>String Override function
    public void SetWttcCosts(string value)
    {
        SetWttcCostsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller473()
    {
        return _Filler473;
    }
    
    // Standard Setter
    public void SetFiller473(string value)
    {
        _Filler473 = value;
    }
    
    // Get<>AsString()
    public string GetFiller473AsString()
    {
        return _Filler473.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller473AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler473 = value;
    }
    
    // Standard Getter
    public int GetWttcMaxTableSize()
    {
        return _WttcMaxTableSize;
    }
    
    // Standard Setter
    public void SetWttcMaxTableSize(int value)
    {
        _WttcMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWttcMaxTableSizeAsString()
    {
        return _WttcMaxTableSize.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttcMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttcMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWttcOccurs()
    {
        return _WttcOccurs;
    }
    
    // Standard Setter
    public void SetWttcOccurs(int value)
    {
        _WttcOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWttcOccursAsString()
    {
        return _WttcOccurs.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttcOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttcOccurs = parsed;
    }
    
    // Array Accessors for WttcElement
    public WttcElement GetWttcElementAt(int index)
    {
        return _WttcElement[index];
    }
    
    public void SetWttcElementAt(int index, WttcElement value)
    {
        _WttcElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WttcElement GetWttcElement()
    {
        return _WttcElement != null && _WttcElement.Length > 0
        ? _WttcElement[0]
        : new WttcElement();
    }
    
    public void SetWttcElement(WttcElement value)
    {
        if (_WttcElement == null || _WttcElement.Length == 0)
        _WttcElement = new WttcElement[1];
        _WttcElement[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWttcElement(string value)
    {
        if (!string.IsNullOrEmpty(value) && value.Length < WttcElement.GetSize() * _WttcElement.Length)
        {
            value = value.PadRight(WttcElement.GetSize() * _WttcElement.Length);
        }
        
        int offset = 0;
        for (int i = 0; i < _WttcElement.Length; i++)
        {
            if (offset + WttcElement.GetSize() > value.Length) break;
            string chunk = value.Substring(offset, WttcElement.GetSize());
            _WttcElement[i].SetWttcElementAsString(chunk);
            offset += WttcElement.GetSize();
        }
    }

        public WttcElement GetWttcElementAt(int index)
        {
            if (index >= 0 && index < _WttcElement.Length)
            {
                return _WttcElement[index];
            }
            return new WttcElement(); // Return empty element if index is out of bounds
        }

        public void SetWttcPointer(string value)
        {
            // This method sets the pointer for the WttcCosts structure
            // Implementation depends on business logic
        }

        // Nested Class: WttcElement
        public class WttcElement
    {
        private static int _size = 99;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttcPointer, is_external=, is_static_class=False, static_prefix=
        private string _WttcPointer ="";
        
        
        
        
        // [DEBUG] Field: WttcCostNo, is_external=, is_static_class=False, static_prefix=
        private int _WttcCostNo =0;
        
        
        
        
        // [DEBUG] Field: WttcData, is_external=, is_static_class=False, static_prefix=
        private WttcElement.WttcData _WttcData = new WttcElement.WttcData();
        
        
        
        
    public WttcElement() {}
    
    public WttcElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttcPointer(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWttcCostNo(int.Parse(data.Substring(offset, 5).Trim()));
        offset += 5;
        _WttcData.SetWttcDataAsString(data.Substring(offset, WttcData.GetSize()));
        offset += 90;
        
    }
    
    // Serialization methods
    public string GetWttcElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttcPointer.PadRight(4));
        result.Append(_WttcCostNo.ToString().PadLeft(5, '0'));
        result.Append(_WttcData.GetWttcDataAsString());
        
        return result.ToString();
    }
    
    public void SetWttcElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWttcPointer(extracted);
        }
        offset += 4;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttcCostNo(parsedInt);
        }
        offset += 5;
        if (offset + 90 <= data.Length)
        {
            _WttcData.SetWttcDataAsString(data.Substring(offset, 90));
        }
        else
        {
            _WttcData.SetWttcDataAsString(data.Substring(offset));
        }
        offset += 90;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWttcPointer()
    {
        return _WttcPointer;
    }
    
    // Standard Setter
    public void SetWttcPointer(string value)
    {
        _WttcPointer = value;
    }
    
    // Get<>AsString()
    public string GetWttcPointerAsString()
    {
        return _WttcPointer.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWttcPointerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttcPointer = value;
    }
    
    // Standard Getter
    public int GetWttcCostNo()
    {
        return _WttcCostNo;
    }
    
    // Standard Setter
    public void SetWttcCostNo(int value)
    {
        _WttcCostNo = value;
    }
    
    // Get<>AsString()
    public string GetWttcCostNoAsString()
    {
        return _WttcCostNo.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttcCostNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttcCostNo = parsed;
    }
    
    // Standard Getter
    public WttcData GetWttcData()
    {
        return _WttcData;
    }
    
    // Standard Setter
    public void SetWttcData(WttcData value)
    {
        _WttcData = value;
    }
    
    // Get<>AsString()
    public string GetWttcDataAsString()
    {
        return _WttcData != null ? _WttcData.GetWttcDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttcDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttcData == null)
        {
            _WttcData = new WttcData();
        }
        _WttcData.SetWttcDataAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttcData
    public class WttcData
    {
        private static int _size = 90;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttcAdjPointer, is_external=, is_static_class=False, static_prefix=
        private string _WttcAdjPointer ="";
        
        
        
        
        // [DEBUG] Field: WttcApportioning, is_external=, is_static_class=False, static_prefix=
        private decimal _WttcApportioning =0;
        
        
        
        
        // [DEBUG] Field: WttcMasterfileFields, is_external=, is_static_class=False, static_prefix=
        private WttcData.WttcMasterfileFields _WttcMasterfileFields = new WttcData.WttcMasterfileFields();
        
        
        
        
    public WttcData() {}
    
    public WttcData(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttcAdjPointer(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWttcApportioning(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        _WttcMasterfileFields.SetWttcMasterfileFieldsAsString(data.Substring(offset, WttcMasterfileFields.GetSize()));
        offset += 73;
        
    }
    
    // Serialization methods
    public string GetWttcDataAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttcAdjPointer.PadRight(4));
        result.Append(_WttcApportioning.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttcMasterfileFields.GetWttcMasterfileFieldsAsString());
        
        return result.ToString();
    }
    
    public void SetWttcDataAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWttcAdjPointer(extracted);
        }
        offset += 4;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttcApportioning(parsedDec);
        }
        offset += 13;
        if (offset + 73 <= data.Length)
        {
            _WttcMasterfileFields.SetWttcMasterfileFieldsAsString(data.Substring(offset, 73));
        }
        else
        {
            _WttcMasterfileFields.SetWttcMasterfileFieldsAsString(data.Substring(offset));
        }
        offset += 73;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWttcAdjPointer()
    {
        return _WttcAdjPointer;
    }
    
    // Standard Setter
    public void SetWttcAdjPointer(string value)
    {
        _WttcAdjPointer = value;
    }
    
    // Get<>AsString()
    public string GetWttcAdjPointerAsString()
    {
        return _WttcAdjPointer.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWttcAdjPointerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttcAdjPointer = value;
    }
    
    // Standard Getter
    public decimal GetWttcApportioning()
    {
        return _WttcApportioning;
    }
    
    // Standard Setter
    public void SetWttcApportioning(decimal value)
    {
        _WttcApportioning = value;
    }
    
    // Get<>AsString()
    public string GetWttcApportioningAsString()
    {
        return _WttcApportioning.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttcApportioningAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttcApportioning = parsed;
    }
    
    // Standard Getter
    public WttcMasterfileFields GetWttcMasterfileFields()
    {
        return _WttcMasterfileFields;
    }
    
    // Standard Setter
    public void SetWttcMasterfileFields(WttcMasterfileFields value)
    {
        _WttcMasterfileFields = value;
    }
    
    // Get<>AsString()
    public string GetWttcMasterfileFieldsAsString()
    {
        return _WttcMasterfileFields != null ? _WttcMasterfileFields.GetWttcMasterfileFieldsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttcMasterfileFieldsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttcMasterfileFields == null)
        {
            _WttcMasterfileFields = new WttcMasterfileFields();
        }
        _WttcMasterfileFields.SetWttcMasterfileFieldsAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttcMasterfileFields
    public class WttcMasterfileFields
    {
        private static int _size = 73;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttcBfBalanceUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttcBfBalanceUnits =0;
        
        
        
        
        // [DEBUG] Field: WttcBalanceUnitsYtd, is_external=, is_static_class=False, static_prefix=
        private decimal _WttcBalanceUnitsYtd =0;
        
        
        
        
        // [DEBUG] Field: WttcUnitsPresent, is_external=, is_static_class=False, static_prefix=
        private string _WttcUnitsPresent ="";
        
        
        // 88-level condition checks for WttcUnitsPresent
        public bool IsWttcRealCost()
        {
            if (this._WttcUnitsPresent == "'0'") return true;
            if (this._WttcUnitsPresent == "'1'") return true;
            if (this._WttcUnitsPresent == "'I'") return true;
            if (this._WttcUnitsPresent == "'4'") return true;
            return false;
        }
        public bool IsWttcCostWithUnits()
        {
            if (this._WttcUnitsPresent == "'1'") return true;
            if (this._WttcUnitsPresent == "'I'") return true;
            return false;
        }
        public bool IsWttcIndexed1982Cost()
        {
            if (this._WttcUnitsPresent == "'I'") return true;
            return false;
        }
        public bool IsWttcGiltLoss()
        {
            if (this._WttcUnitsPresent == "'2'") return true;
            return false;
        }
        public bool IsWttcBfGain()
        {
            if (this._WttcUnitsPresent == "'3'") return true;
            return false;
        }
        public bool IsWttcSmallDistribution()
        {
            if (this._WttcUnitsPresent == "'4'") return true;
            return false;
        }
        public bool IsWttcBfIndxn()
        {
            if (this._WttcUnitsPresent == "'5'") return true;
            return false;
        }
        public bool IsWttcOsLiability()
        {
            if (this._WttcUnitsPresent == "'6'") return true;
            return false;
        }
        public bool IsWttc1982Bdv()
        {
            if (this._WttcUnitsPresent == "'7'") return true;
            return false;
        }
        public bool IsWttc1965Bdv()
        {
            if (this._WttcUnitsPresent == "'B'") return true;
            return false;
        }
        public bool IsWttcOrigCost()
        {
            if (this._WttcUnitsPresent == "'C'") return true;
            return false;
        }
        public bool IsWttcIrishDdHistCost()
        {
            if (this._WttcUnitsPresent == "'D'") return true;
            return false;
        }
        public bool IsWttcIrishNdlCost()
        {
            if (this._WttcUnitsPresent == "'N'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttcBfCostBalance, is_external=, is_static_class=False, static_prefix=
        private decimal _WttcBfCostBalance =0;
        
        
        
        
        // [DEBUG] Field: WttcCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
        private decimal _WttcCostBalanceYtd =0;
        
        
        
        
        // [DEBUG] Field: WttcDates, is_external=, is_static_class=False, static_prefix=
        private WttcMasterfileFields.WttcDates _WttcDates = new WttcMasterfileFields.WttcDates();
        
        
        
        
        // [DEBUG] Field: WttcNiNpPiCosts, is_external=, is_static_class=False, static_prefix=
        private string _WttcNiNpPiCosts ="";
        
        
        
        
        // [DEBUG] Field: WttcTaperDate, is_external=, is_static_class=False, static_prefix=
        private WttcMasterfileFields.WttcTaperDate _WttcTaperDate = new WttcMasterfileFields.WttcTaperDate();
        
        
        
        
        // [DEBUG] Field: WttcBfTaperUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttcBfTaperUnits =0;
        
        
        
        
        // [DEBUG] Field: WttcTaperUnitsYtd, is_external=, is_static_class=False, static_prefix=
        private decimal _WttcTaperUnitsYtd =0;
        
        
        
        
    public WttcMasterfileFields() {}
    
    public WttcMasterfileFields(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttcBfBalanceUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWttcBalanceUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWttcUnitsPresent(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttcBfCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        SetWttcCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        _WttcDates.SetWttcDatesAsString(data.Substring(offset, WttcDates.GetSize()));
        offset += 12;
        SetWttcNiNpPiCosts(data.Substring(offset, 0).Trim());
        offset += 0;
        _WttcTaperDate.SetWttcTaperDateAsString(data.Substring(offset, WttcTaperDate.GetSize()));
        offset += 0;
        SetWttcBfTaperUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 0)));
        offset += 0;
        SetWttcTaperUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 0)));
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWttcMasterfileFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttcBfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttcBalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttcUnitsPresent.PadRight(1));
        result.Append(_WttcBfCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttcCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttcDates.GetWttcDatesAsString());
        result.Append(_WttcNiNpPiCosts.PadRight(0));
        result.Append(_WttcTaperDate.GetWttcTaperDateAsString());
        result.Append(_WttcBfTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttcTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWttcMasterfileFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttcBfBalanceUnits(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttcBalanceUnitsYtd(parsedDec);
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttcUnitsPresent(extracted);
        }
        offset += 1;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttcBfCostBalance(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttcCostBalanceYtd(parsedDec);
        }
        offset += 17;
        if (offset + 12 <= data.Length)
        {
            _WttcDates.SetWttcDatesAsString(data.Substring(offset, 12));
        }
        else
        {
            _WttcDates.SetWttcDatesAsString(data.Substring(offset));
        }
        offset += 12;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttcNiNpPiCosts(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _WttcTaperDate.SetWttcTaperDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _WttcTaperDate.SetWttcTaperDateAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttcBfTaperUnits(parsedDec);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttcTaperUnitsYtd(parsedDec);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public decimal GetWttcBfBalanceUnits()
    {
        return _WttcBfBalanceUnits;
    }
    
    // Standard Setter
    public void SetWttcBfBalanceUnits(decimal value)
    {
        _WttcBfBalanceUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttcBfBalanceUnitsAsString()
    {
        return _WttcBfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttcBfBalanceUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttcBfBalanceUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWttcBalanceUnitsYtd()
    {
        return _WttcBalanceUnitsYtd;
    }
    
    // Standard Setter
    public void SetWttcBalanceUnitsYtd(decimal value)
    {
        _WttcBalanceUnitsYtd = value;
    }
    
    // Get<>AsString()
    public string GetWttcBalanceUnitsYtdAsString()
    {
        return _WttcBalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttcBalanceUnitsYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttcBalanceUnitsYtd = parsed;
    }
    
    // Standard Getter
    public string GetWttcUnitsPresent()
    {
        return _WttcUnitsPresent;
    }
    
    // Standard Setter
    public void SetWttcUnitsPresent(string value)
    {
        _WttcUnitsPresent = value;
    }
    
    // Get<>AsString()
    public string GetWttcUnitsPresentAsString()
    {
        return _WttcUnitsPresent.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttcUnitsPresentAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttcUnitsPresent = value;
    }
    
    // Standard Getter
    public decimal GetWttcBfCostBalance()
    {
        return _WttcBfCostBalance;
    }
    
    // Standard Setter
    public void SetWttcBfCostBalance(decimal value)
    {
        _WttcBfCostBalance = value;
    }
    
    // Get<>AsString()
    public string GetWttcBfCostBalanceAsString()
    {
        return _WttcBfCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttcBfCostBalanceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttcBfCostBalance = parsed;
    }
    
    // Standard Getter
    public decimal GetWttcCostBalanceYtd()
    {
        return _WttcCostBalanceYtd;
    }
    
    // Standard Setter
    public void SetWttcCostBalanceYtd(decimal value)
    {
        _WttcCostBalanceYtd = value;
    }
    
    // Get<>AsString()
    public string GetWttcCostBalanceYtdAsString()
    {
        return _WttcCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttcCostBalanceYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttcCostBalanceYtd = parsed;
    }
    
    // Standard Getter
    public WttcDates GetWttcDates()
    {
        return _WttcDates;
    }
    
    // Standard Setter
    public void SetWttcDates(WttcDates value)
    {
        _WttcDates = value;
    }
    
    // Get<>AsString()
    public string GetWttcDatesAsString()
    {
        return _WttcDates != null ? _WttcDates.GetWttcDatesAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttcDatesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttcDates == null)
        {
            _WttcDates = new WttcDates();
        }
        _WttcDates.SetWttcDatesAsString(value);
    }
    
    // Standard Getter
    public string GetWttcNiNpPiCosts()
    {
        return _WttcNiNpPiCosts;
    }
    
    // Standard Setter
    public void SetWttcNiNpPiCosts(string value)
    {
        _WttcNiNpPiCosts = value;
    }
    
    // Get<>AsString()
    public string GetWttcNiNpPiCostsAsString()
    {
        return _WttcNiNpPiCosts.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttcNiNpPiCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttcNiNpPiCosts = value;
    }
    
    // Standard Getter
    public WttcTaperDate GetWttcTaperDate()
    {
        return _WttcTaperDate;
    }
    
    // Standard Setter
    public void SetWttcTaperDate(WttcTaperDate value)
    {
        _WttcTaperDate = value;
    }
    
    // Get<>AsString()
    public string GetWttcTaperDateAsString()
    {
        return _WttcTaperDate != null ? _WttcTaperDate.GetWttcTaperDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttcTaperDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttcTaperDate == null)
        {
            _WttcTaperDate = new WttcTaperDate();
        }
        _WttcTaperDate.SetWttcTaperDateAsString(value);
    }
    
    // Standard Getter
    public decimal GetWttcBfTaperUnits()
    {
        return _WttcBfTaperUnits;
    }
    
    // Standard Setter
    public void SetWttcBfTaperUnits(decimal value)
    {
        _WttcBfTaperUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttcBfTaperUnitsAsString()
    {
        return _WttcBfTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttcBfTaperUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttcBfTaperUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWttcTaperUnitsYtd()
    {
        return _WttcTaperUnitsYtd;
    }
    
    // Standard Setter
    public void SetWttcTaperUnitsYtd(decimal value)
    {
        _WttcTaperUnitsYtd = value;
    }
    
    // Get<>AsString()
    public string GetWttcTaperUnitsYtdAsString()
    {
        return _WttcTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttcTaperUnitsYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttcTaperUnitsYtd = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttcDates
    public class WttcDates
    {
        private static int _size = 12;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttcIndexDate, is_external=, is_static_class=False, static_prefix=
        private WttcDates.WttcIndexDate _WttcIndexDate = new WttcDates.WttcIndexDate();
        
        
        
        
        // [DEBUG] Field: WttcCostDate, is_external=, is_static_class=False, static_prefix=
        private WttcDates.WttcCostDate _WttcCostDate = new WttcDates.WttcCostDate();
        
        
        
        
        // [DEBUG] Field: Filler474, is_external=, is_static_class=False, static_prefix=
        private string _Filler474 ="";
        
        
        // 88-level condition checks for Filler474
        public bool IsWttcCostPreApr82()
        {
            int parsed_Filler474;
            if (int.TryParse(this.GetFiller474(), out parsed_Filler474))
            {
                if (parsed_Filler474 >= 450101 && parsed_Filler474 <= 820331) return true;
            }
            return false;
        }
        
        
    public WttcDates() {}
    
    public WttcDates(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WttcIndexDate.SetWttcIndexDateAsString(data.Substring(offset, WttcIndexDate.GetSize()));
        offset += 6;
        _WttcCostDate.SetWttcCostDateAsString(data.Substring(offset, WttcCostDate.GetSize()));
        offset += 6;
        SetFiller474(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWttcDatesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttcIndexDate.GetWttcIndexDateAsString());
        result.Append(_WttcCostDate.GetWttcCostDateAsString());
        result.Append(_Filler474.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWttcDatesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _WttcIndexDate.SetWttcIndexDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WttcIndexDate.SetWttcIndexDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _WttcCostDate.SetWttcCostDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WttcCostDate.SetWttcCostDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller474(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WttcIndexDate GetWttcIndexDate()
    {
        return _WttcIndexDate;
    }
    
    // Standard Setter
    public void SetWttcIndexDate(WttcIndexDate value)
    {
        _WttcIndexDate = value;
    }
    
    // Get<>AsString()
    public string GetWttcIndexDateAsString()
    {
        return _WttcIndexDate != null ? _WttcIndexDate.GetWttcIndexDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttcIndexDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttcIndexDate == null)
        {
            _WttcIndexDate = new WttcIndexDate();
        }
        _WttcIndexDate.SetWttcIndexDateAsString(value);
    }
    
    // Standard Getter
    public WttcCostDate GetWttcCostDate()
    {
        return _WttcCostDate;
    }
    
    // Standard Setter
    public void SetWttcCostDate(WttcCostDate value)
    {
        _WttcCostDate = value;
    }
    
    // Get<>AsString()
    public string GetWttcCostDateAsString()
    {
        return _WttcCostDate != null ? _WttcCostDate.GetWttcCostDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttcCostDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttcCostDate == null)
        {
            _WttcCostDate = new WttcCostDate();
        }
        _WttcCostDate.SetWttcCostDateAsString(value);
    }
    
    // Standard Getter
    public string GetFiller474()
    {
        return _Filler474;
    }
    
    // Standard Setter
    public void SetFiller474(string value)
    {
        _Filler474 = value;
    }
    
    // Get<>AsString()
    public string GetFiller474AsString()
    {
        return _Filler474.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller474AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler474 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttcIndexDate
    public class WttcIndexDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttcIndexDateYymm, is_external=, is_static_class=False, static_prefix=
        private WttcIndexDate.WttcIndexDateYymm _WttcIndexDateYymm = new WttcIndexDate.WttcIndexDateYymm();
        
        
        
        
        // [DEBUG] Field: WttcIndexDateDd, is_external=, is_static_class=False, static_prefix=
        private int _WttcIndexDateDd =0;
        
        
        
        
    public WttcIndexDate() {}
    
    public WttcIndexDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WttcIndexDateYymm.SetWttcIndexDateYymmAsString(data.Substring(offset, WttcIndexDateYymm.GetSize()));
        offset += 4;
        SetWttcIndexDateDd(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWttcIndexDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttcIndexDateYymm.GetWttcIndexDateYymmAsString());
        result.Append(_WttcIndexDateDd.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWttcIndexDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _WttcIndexDateYymm.SetWttcIndexDateYymmAsString(data.Substring(offset, 4));
        }
        else
        {
            _WttcIndexDateYymm.SetWttcIndexDateYymmAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttcIndexDateDd(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WttcIndexDateYymm GetWttcIndexDateYymm()
    {
        return _WttcIndexDateYymm;
    }
    
    // Standard Setter
    public void SetWttcIndexDateYymm(WttcIndexDateYymm value)
    {
        _WttcIndexDateYymm = value;
    }
    
    // Get<>AsString()
    public string GetWttcIndexDateYymmAsString()
    {
        return _WttcIndexDateYymm != null ? _WttcIndexDateYymm.GetWttcIndexDateYymmAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttcIndexDateYymmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttcIndexDateYymm == null)
        {
            _WttcIndexDateYymm = new WttcIndexDateYymm();
        }
        _WttcIndexDateYymm.SetWttcIndexDateYymmAsString(value);
    }
    
    // Standard Getter
    public int GetWttcIndexDateDd()
    {
        return _WttcIndexDateDd;
    }
    
    // Standard Setter
    public void SetWttcIndexDateDd(int value)
    {
        _WttcIndexDateDd = value;
    }
    
    // Get<>AsString()
    public string GetWttcIndexDateDdAsString()
    {
        return _WttcIndexDateDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWttcIndexDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttcIndexDateDd = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttcIndexDateYymm
    public class WttcIndexDateYymm
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttcIndexDateYy, is_external=, is_static_class=False, static_prefix=
        private int _WttcIndexDateYy =0;
        
        
        
        
        // [DEBUG] Field: WttcIndexDateMm, is_external=, is_static_class=False, static_prefix=
        private int _WttcIndexDateMm =0;
        
        
        
        
    public WttcIndexDateYymm() {}
    
    public WttcIndexDateYymm(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttcIndexDateYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetWttcIndexDateMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWttcIndexDateYymmAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttcIndexDateYy.ToString().PadLeft(2, '0'));
        result.Append(_WttcIndexDateMm.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWttcIndexDateYymmAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttcIndexDateYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttcIndexDateMm(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWttcIndexDateYy()
    {
        return _WttcIndexDateYy;
    }
    
    // Standard Setter
    public void SetWttcIndexDateYy(int value)
    {
        _WttcIndexDateYy = value;
    }
    
    // Get<>AsString()
    public string GetWttcIndexDateYyAsString()
    {
        return _WttcIndexDateYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWttcIndexDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttcIndexDateYy = parsed;
    }
    
    // Standard Getter
    public int GetWttcIndexDateMm()
    {
        return _WttcIndexDateMm;
    }
    
    // Standard Setter
    public void SetWttcIndexDateMm(int value)
    {
        _WttcIndexDateMm = value;
    }
    
    // Get<>AsString()
    public string GetWttcIndexDateMmAsString()
    {
        return _WttcIndexDateMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWttcIndexDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttcIndexDateMm = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Nested Class: WttcCostDate
public class WttcCostDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttcCostDateYymm, is_external=, is_static_class=False, static_prefix=
    private WttcCostDate.WttcCostDateYymm _WttcCostDateYymm = new WttcCostDate.WttcCostDateYymm();
    
    
    
    
    // [DEBUG] Field: WttcCostDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WttcCostDateDd =0;
    
    
    
    
public WttcCostDate() {}

public WttcCostDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttcCostDateYymm.SetWttcCostDateYymmAsString(data.Substring(offset, WttcCostDateYymm.GetSize()));
    offset += 4;
    SetWttcCostDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttcCostDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttcCostDateYymm.GetWttcCostDateYymmAsString());
    result.Append(_WttcCostDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttcCostDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _WttcCostDateYymm.SetWttcCostDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _WttcCostDateYymm.SetWttcCostDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttcCostDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WttcCostDateYymm GetWttcCostDateYymm()
{
    return _WttcCostDateYymm;
}

// Standard Setter
public void SetWttcCostDateYymm(WttcCostDateYymm value)
{
    _WttcCostDateYymm = value;
}

// Get<>AsString()
public string GetWttcCostDateYymmAsString()
{
    return _WttcCostDateYymm != null ? _WttcCostDateYymm.GetWttcCostDateYymmAsString() : "";
}

// Set<>AsString()
public void SetWttcCostDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttcCostDateYymm == null)
    {
        _WttcCostDateYymm = new WttcCostDateYymm();
    }
    _WttcCostDateYymm.SetWttcCostDateYymmAsString(value);
}

// Standard Getter
public int GetWttcCostDateDd()
{
    return _WttcCostDateDd;
}

// Standard Setter
public void SetWttcCostDateDd(int value)
{
    _WttcCostDateDd = value;
}

// Get<>AsString()
public string GetWttcCostDateDdAsString()
{
    return _WttcCostDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttcCostDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttcCostDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttcCostDateYymm
public class WttcCostDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttcCostDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WttcCostDateYy =0;
    
    
    
    
    // [DEBUG] Field: WttcCostDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WttcCostDateMm =0;
    
    
    
    
public WttcCostDateYymm() {}

public WttcCostDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttcCostDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttcCostDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttcCostDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttcCostDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WttcCostDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttcCostDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttcCostDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttcCostDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttcCostDateYy()
{
    return _WttcCostDateYy;
}

// Standard Setter
public void SetWttcCostDateYy(int value)
{
    _WttcCostDateYy = value;
}

// Get<>AsString()
public string GetWttcCostDateYyAsString()
{
    return _WttcCostDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttcCostDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttcCostDateYy = parsed;
}

// Standard Getter
public int GetWttcCostDateMm()
{
    return _WttcCostDateMm;
}

// Standard Setter
public void SetWttcCostDateMm(int value)
{
    _WttcCostDateMm = value;
}

// Get<>AsString()
public string GetWttcCostDateMmAsString()
{
    return _WttcCostDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttcCostDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttcCostDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Nested Class: WttcTaperDate
public class WttcTaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttcTaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _WttcTaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: WttcTaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _WttcTaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: WttcTaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _WttcTaperDateDd ="";
    
    
    
    
public WttcTaperDate() {}

public WttcTaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttcTaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttcTaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttcTaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetWttcTaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttcTaperDateYy.PadRight(0));
    result.Append(_WttcTaperDateMm.PadRight(0));
    result.Append(_WttcTaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetWttcTaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttcTaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttcTaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttcTaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetWttcTaperDateYy()
{
    return _WttcTaperDateYy;
}

// Standard Setter
public void SetWttcTaperDateYy(string value)
{
    _WttcTaperDateYy = value;
}

// Get<>AsString()
public string GetWttcTaperDateYyAsString()
{
    return _WttcTaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetWttcTaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttcTaperDateYy = value;
}

// Standard Getter
public string GetWttcTaperDateMm()
{
    return _WttcTaperDateMm;
}

// Standard Setter
public void SetWttcTaperDateMm(string value)
{
    _WttcTaperDateMm = value;
}

// Get<>AsString()
public string GetWttcTaperDateMmAsString()
{
    return _WttcTaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetWttcTaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttcTaperDateMm = value;
}

// Standard Getter
public string GetWttcTaperDateDd()
{
    return _WttcTaperDateDd;
}

// Standard Setter
public void SetWttcTaperDateDd(string value)
{
    _WttcTaperDateDd = value;
}

// Get<>AsString()
public string GetWttcTaperDateDdAsString()
{
    return _WttcTaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetWttcTaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttcTaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}

}}