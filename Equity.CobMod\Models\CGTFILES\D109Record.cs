using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D109Record Data Structure

public class D109Record
{
    private static int _size = 200;
    // [DEBUG] Class: D109Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler156, is_external=, is_static_class=False, static_prefix=
    private string _Filler156 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD109RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler156.PadRight(200));
        
        return result.ToString();
    }
    
    public void SetD109RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 200 <= data.Length)
        {
            string extracted = data.Substring(offset, 200).Trim();
            SetFiller156(extracted);
        }
        offset += 200;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD109RecordAsString();
    }
    // Set<>String Override function
    public void SetD109Record(string value)
    {
        SetD109RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller156()
    {
        return _Filler156;
    }
    
    // Standard Setter
    public void SetFiller156(string value)
    {
        _Filler156 = value;
    }
    
    // Get<>AsString()
    public string GetFiller156AsString()
    {
        return _Filler156.PadRight(200);
    }
    
    // Set<>AsString()
    public void SetFiller156AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler156 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}