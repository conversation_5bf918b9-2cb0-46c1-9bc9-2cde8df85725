using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D171File Data Structure

public class D171File
{
    private static int _size = 12;
    // [DEBUG] Class: D171File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler331, is_external=, is_static_class=False, static_prefix=
    private string _Filler331 ="$";
    
    
    
    
    // [DEBUG] Field: D171UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D171UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler332, is_external=, is_static_class=False, static_prefix=
    private string _Filler332 ="TE";
    
    
    
    
    // [DEBUG] Field: D171ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D171ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler333, is_external=, is_static_class=False, static_prefix=
    private string _Filler333 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD171FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler331.PadRight(1));
        result.Append(_D171UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler332.PadRight(2));
        result.Append(_D171ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler333.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD171FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller331(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD171UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller332(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD171ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller333(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD171FileAsString();
    }
    // Set<>String Override function
    public void SetD171File(string value)
    {
        SetD171FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller331()
    {
        return _Filler331;
    }
    
    // Standard Setter
    public void SetFiller331(string value)
    {
        _Filler331 = value;
    }
    
    // Get<>AsString()
    public string GetFiller331AsString()
    {
        return _Filler331.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller331AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler331 = value;
    }
    
    // Standard Getter
    public int GetD171UserNo()
    {
        return _D171UserNo;
    }
    
    // Standard Setter
    public void SetD171UserNo(int value)
    {
        _D171UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD171UserNoAsString()
    {
        return _D171UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD171UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D171UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller332()
    {
        return _Filler332;
    }
    
    // Standard Setter
    public void SetFiller332(string value)
    {
        _Filler332 = value;
    }
    
    // Get<>AsString()
    public string GetFiller332AsString()
    {
        return _Filler332.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller332AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler332 = value;
    }
    
    // Standard Getter
    public int GetD171ReportNo()
    {
        return _D171ReportNo;
    }
    
    // Standard Setter
    public void SetD171ReportNo(int value)
    {
        _D171ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD171ReportNoAsString()
    {
        return _D171ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD171ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D171ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller333()
    {
        return _Filler333;
    }
    
    // Standard Setter
    public void SetFiller333(string value)
    {
        _Filler333 = value;
    }
    
    // Get<>AsString()
    public string GetFiller333AsString()
    {
        return _Filler333.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller333AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler333 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}