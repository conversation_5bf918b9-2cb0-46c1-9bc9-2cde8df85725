using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D23File Data Structure

public class D23File
{
    private static int _size = 12;
    // [DEBUG] Class: D23File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler184, is_external=, is_static_class=False, static_prefix=
    private string _Filler184 ="$";
    
    
    
    
    // [DEBUG] Field: D23UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D23UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler185, is_external=, is_static_class=False, static_prefix=
    private string _Filler185 ="R1";
    
    
    
    
    // [DEBUG] Field: D23ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D23ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler186, is_external=, is_static_class=False, static_prefix=
    private string _Filler186 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD23FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler184.PadRight(1));
        result.Append(_D23UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler185.PadRight(2));
        result.Append(_D23ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler186.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD23FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller184(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD23UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller185(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD23ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller186(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD23FileAsString();
    }
    // Set<>String Override function
    public void SetD23File(string value)
    {
        SetD23FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller184()
    {
        return _Filler184;
    }
    
    // Standard Setter
    public void SetFiller184(string value)
    {
        _Filler184 = value;
    }
    
    // Get<>AsString()
    public string GetFiller184AsString()
    {
        return _Filler184.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller184AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler184 = value;
    }
    
    // Standard Getter
    public int GetD23UserNo()
    {
        return _D23UserNo;
    }
    
    // Standard Setter
    public void SetD23UserNo(int value)
    {
        _D23UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD23UserNoAsString()
    {
        return _D23UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD23UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D23UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller185()
    {
        return _Filler185;
    }
    
    // Standard Setter
    public void SetFiller185(string value)
    {
        _Filler185 = value;
    }
    
    // Get<>AsString()
    public string GetFiller185AsString()
    {
        return _Filler185.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller185AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler185 = value;
    }
    
    // Standard Getter
    public int GetD23ReportNo()
    {
        return _D23ReportNo;
    }
    
    // Standard Setter
    public void SetD23ReportNo(int value)
    {
        _D23ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD23ReportNoAsString()
    {
        return _D23ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD23ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D23ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller186()
    {
        return _Filler186;
    }
    
    // Standard Setter
    public void SetFiller186(string value)
    {
        _Filler186 = value;
    }
    
    // Get<>AsString()
    public string GetFiller186AsString()
    {
        return _Filler186.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller186AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler186 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}