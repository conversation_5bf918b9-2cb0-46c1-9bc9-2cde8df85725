using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D31Record Data Structure

public class D31Record
{
    private static int _size = 331;
    // [DEBUG] Class: D31Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D31Key, is_external=, is_static_class=False, static_prefix=
    private D31Key _D31Key = new D31Key();
    
    
    
    
    // [DEBUG] Field: D31PrinterName, is_external=, is_static_class=False, static_prefix=
    private string _D31PrinterName ="";
    
    
    
    
    // [DEBUG] Field: D31PaperSize, is_external=, is_static_class=False, static_prefix=
    private string _D31PaperSize ="";
    
    
    
    
    // [DEBUG] Field: D31PoundsSign, is_external=, is_static_class=False, static_prefix=
    private string _D31PoundsSign ="";
    
    
    
    
    // [DEBUG] Field: D31Light, is_external=, is_static_class=False, static_prefix=
    private string _D31Light ="";
    
    
    
    
    // [DEBUG] Field: D31Bold, is_external=, is_static_class=False, static_prefix=
    private string _D31Bold ="";
    
    
    
    
    // [DEBUG] Field: D31GroupAPrintCommands, is_external=, is_static_class=False, static_prefix=
    private D31GroupAPrintCommands _D31GroupAPrintCommands = new D31GroupAPrintCommands();
    
    
    
    
    // [DEBUG] Field: D31GroupBPrintCommands, is_external=, is_static_class=False, static_prefix=
    private D31GroupBPrintCommands _D31GroupBPrintCommands = new D31GroupBPrintCommands();
    
    
    
    
    // [DEBUG] Field: D31UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D31UpdateCount =0;
    
    
    
    
    // [DEBUG] Field: D31InverseKey, is_external=, is_static_class=False, static_prefix=
    private D31InverseKey _D31InverseKey = new D31InverseKey();
    
    
    
    
    
    // Serialization methods
    public string GetD31RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D31Key.GetD31KeyAsString());
        result.Append(_D31PrinterName.PadRight(30));
        result.Append(_D31PaperSize.PadRight(2));
        result.Append(_D31PoundsSign.PadRight(1));
        result.Append(_D31Light.PadRight(20));
        result.Append(_D31Bold.PadRight(20));
        result.Append(_D31GroupAPrintCommands.GetD31GroupAPrintCommandsAsString());
        result.Append(_D31GroupBPrintCommands.GetD31GroupBPrintCommandsAsString());
        result.Append(_D31UpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_D31InverseKey.GetD31InverseKeyAsString());
        
        return result.ToString();
    }
    
    public void SetD31RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            _D31Key.SetD31KeyAsString(data.Substring(offset, 2));
        }
        else
        {
            _D31Key.SetD31KeyAsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD31PrinterName(extracted);
        }
        offset += 30;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD31PaperSize(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD31PoundsSign(extracted);
        }
        offset += 1;
        if (offset + 20 <= data.Length)
        {
            string extracted = data.Substring(offset, 20).Trim();
            SetD31Light(extracted);
        }
        offset += 20;
        if (offset + 20 <= data.Length)
        {
            string extracted = data.Substring(offset, 20).Trim();
            SetD31Bold(extracted);
        }
        offset += 20;
        if (offset + 135 <= data.Length)
        {
            _D31GroupAPrintCommands.SetD31GroupAPrintCommandsAsString(data.Substring(offset, 135));
        }
        else
        {
            _D31GroupAPrintCommands.SetD31GroupAPrintCommandsAsString(data.Substring(offset));
        }
        offset += 135;
        if (offset + 115 <= data.Length)
        {
            _D31GroupBPrintCommands.SetD31GroupBPrintCommandsAsString(data.Substring(offset, 115));
        }
        else
        {
            _D31GroupBPrintCommands.SetD31GroupBPrintCommandsAsString(data.Substring(offset));
        }
        offset += 115;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD31UpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            _D31InverseKey.SetD31InverseKeyAsString(data.Substring(offset, 2));
        }
        else
        {
            _D31InverseKey.SetD31InverseKeyAsString(data.Substring(offset));
        }
        offset += 2;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD31RecordAsString();
    }
    // Set<>String Override function
    public void SetD31Record(string value)
    {
        SetD31RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D31Key GetD31Key()
    {
        return _D31Key;
    }
    
    // Standard Setter
    public void SetD31Key(D31Key value)
    {
        _D31Key = value;
    }
    
    // Get<>AsString()
    public string GetD31KeyAsString()
    {
        return _D31Key != null ? _D31Key.GetD31KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD31KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D31Key == null)
        {
            _D31Key = new D31Key();
        }
        _D31Key.SetD31KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD31PrinterName()
    {
        return _D31PrinterName;
    }
    
    // Standard Setter
    public void SetD31PrinterName(string value)
    {
        _D31PrinterName = value;
    }
    
    // Get<>AsString()
    public string GetD31PrinterNameAsString()
    {
        return _D31PrinterName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD31PrinterNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D31PrinterName = value;
    }
    
    // Standard Getter
    public string GetD31PaperSize()
    {
        return _D31PaperSize;
    }
    
    // Standard Setter
    public void SetD31PaperSize(string value)
    {
        _D31PaperSize = value;
    }
    
    // Get<>AsString()
    public string GetD31PaperSizeAsString()
    {
        return _D31PaperSize.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD31PaperSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D31PaperSize = value;
    }
    
    // Standard Getter
    public string GetD31PoundsSign()
    {
        return _D31PoundsSign;
    }
    
    // Standard Setter
    public void SetD31PoundsSign(string value)
    {
        _D31PoundsSign = value;
    }
    
    // Get<>AsString()
    public string GetD31PoundsSignAsString()
    {
        return _D31PoundsSign.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD31PoundsSignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D31PoundsSign = value;
    }
    
    // Standard Getter
    public string GetD31Light()
    {
        return _D31Light;
    }
    
    // Standard Setter
    public void SetD31Light(string value)
    {
        _D31Light = value;
    }
    
    // Get<>AsString()
    public string GetD31LightAsString()
    {
        return _D31Light.PadRight(20);
    }
    
    // Set<>AsString()
    public void SetD31LightAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D31Light = value;
    }
    
    // Standard Getter
    public string GetD31Bold()
    {
        return _D31Bold;
    }
    
    // Standard Setter
    public void SetD31Bold(string value)
    {
        _D31Bold = value;
    }
    
    // Get<>AsString()
    public string GetD31BoldAsString()
    {
        return _D31Bold.PadRight(20);
    }
    
    // Set<>AsString()
    public void SetD31BoldAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D31Bold = value;
    }
    
    // Standard Getter
    public D31GroupAPrintCommands GetD31GroupAPrintCommands()
    {
        return _D31GroupAPrintCommands;
    }
    
    // Standard Setter
    public void SetD31GroupAPrintCommands(D31GroupAPrintCommands value)
    {
        _D31GroupAPrintCommands = value;
    }
    
    // Get<>AsString()
    public string GetD31GroupAPrintCommandsAsString()
    {
        return _D31GroupAPrintCommands != null ? _D31GroupAPrintCommands.GetD31GroupAPrintCommandsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD31GroupAPrintCommandsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D31GroupAPrintCommands == null)
        {
            _D31GroupAPrintCommands = new D31GroupAPrintCommands();
        }
        _D31GroupAPrintCommands.SetD31GroupAPrintCommandsAsString(value);
    }
    
    // Standard Getter
    public D31GroupBPrintCommands GetD31GroupBPrintCommands()
    {
        return _D31GroupBPrintCommands;
    }
    
    // Standard Setter
    public void SetD31GroupBPrintCommands(D31GroupBPrintCommands value)
    {
        _D31GroupBPrintCommands = value;
    }
    
    // Get<>AsString()
    public string GetD31GroupBPrintCommandsAsString()
    {
        return _D31GroupBPrintCommands != null ? _D31GroupBPrintCommands.GetD31GroupBPrintCommandsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD31GroupBPrintCommandsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D31GroupBPrintCommands == null)
        {
            _D31GroupBPrintCommands = new D31GroupBPrintCommands();
        }
        _D31GroupBPrintCommands.SetD31GroupBPrintCommandsAsString(value);
    }
    
    // Standard Getter
    public int GetD31UpdateCount()
    {
        return _D31UpdateCount;
    }
    
    // Standard Setter
    public void SetD31UpdateCount(int value)
    {
        _D31UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD31UpdateCountAsString()
    {
        return _D31UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD31UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D31UpdateCount = parsed;
    }
    
    // Standard Getter
    public D31InverseKey GetD31InverseKey()
    {
        return _D31InverseKey;
    }
    
    // Standard Setter
    public void SetD31InverseKey(D31InverseKey value)
    {
        _D31InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD31InverseKeyAsString()
    {
        return _D31InverseKey != null ? _D31InverseKey.GetD31InverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD31InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D31InverseKey == null)
        {
            _D31InverseKey = new D31InverseKey();
        }
        _D31InverseKey.SetD31InverseKeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD31Key(string value)
    {
        _D31Key.SetD31KeyAsString(value);
    }
    // Nested Class: D31Key
    public class D31Key
    {
        private static int _size = 2;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D31PrinterTableCode, is_external=, is_static_class=False, static_prefix=
        private string _D31PrinterTableCode ="";
        
        
        
        
    public D31Key() {}
    
    public D31Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD31PrinterTableCode(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD31KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D31PrinterTableCode.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetD31KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD31PrinterTableCode(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD31PrinterTableCode()
    {
        return _D31PrinterTableCode;
    }
    
    // Standard Setter
    public void SetD31PrinterTableCode(string value)
    {
        _D31PrinterTableCode = value;
    }
    
    // Get<>AsString()
    public string GetD31PrinterTableCodeAsString()
    {
        return _D31PrinterTableCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD31PrinterTableCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D31PrinterTableCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD31GroupAPrintCommands(string value)
{
    _D31GroupAPrintCommands.SetD31GroupAPrintCommandsAsString(value);
}
// Nested Class: D31GroupAPrintCommands
public class D31GroupAPrintCommands
{
    private static int _size = 135;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D31Reset, is_external=, is_static_class=False, static_prefix=
    private string _D31Reset ="";
    
    
    
    
    // [DEBUG] Field: D31OrientationLandscape, is_external=, is_static_class=False, static_prefix=
    private string _D31OrientationLandscape ="";
    
    
    
    
    // [DEBUG] Field: D31SysmbolSetIbmPc, is_external=, is_static_class=False, static_prefix=
    private string _D31SysmbolSetIbmPc ="";
    
    
    
    
    // [DEBUG] Field: D31SpacingFixed, is_external=, is_static_class=False, static_prefix=
    private string _D31SpacingFixed ="";
    
    
    
    
    // [DEBUG] Field: D31Pitch1666, is_external=, is_static_class=False, static_prefix=
    private string _D31Pitch1666 ="";
    
    
    
    
    // [DEBUG] Field: D31PointSize85, is_external=, is_static_class=False, static_prefix=
    private string _D31PointSize85 ="";
    
    
    
    
    // [DEBUG] Field: D31StyleUpright, is_external=, is_static_class=False, static_prefix=
    private string _D31StyleUpright ="";
    
    
    
    
    // [DEBUG] Field: D31StrokeWeightNormal, is_external=, is_static_class=False, static_prefix=
    private string _D31StrokeWeightNormal ="";
    
    
    
    
    // [DEBUG] Field: D31TypefaceLinePrinter, is_external=, is_static_class=False, static_prefix=
    private string _D31TypefaceLinePrinter ="";
    
    
    
    
public D31GroupAPrintCommands() {}

public D31GroupAPrintCommands(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD31Reset(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31OrientationLandscape(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31SysmbolSetIbmPc(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31SpacingFixed(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31Pitch1666(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31PointSize85(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31StyleUpright(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31StrokeWeightNormal(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31TypefaceLinePrinter(data.Substring(offset, 15).Trim());
    offset += 15;
    
}

// Serialization methods
public string GetD31GroupAPrintCommandsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D31Reset.PadRight(15));
    result.Append(_D31OrientationLandscape.PadRight(15));
    result.Append(_D31SysmbolSetIbmPc.PadRight(15));
    result.Append(_D31SpacingFixed.PadRight(15));
    result.Append(_D31Pitch1666.PadRight(15));
    result.Append(_D31PointSize85.PadRight(15));
    result.Append(_D31StyleUpright.PadRight(15));
    result.Append(_D31StrokeWeightNormal.PadRight(15));
    result.Append(_D31TypefaceLinePrinter.PadRight(15));
    
    return result.ToString();
}

public void SetD31GroupAPrintCommandsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31Reset(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31OrientationLandscape(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31SysmbolSetIbmPc(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31SpacingFixed(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31Pitch1666(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31PointSize85(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31StyleUpright(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31StrokeWeightNormal(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD31TypefaceLinePrinter(extracted);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public string GetD31Reset()
{
    return _D31Reset;
}

// Standard Setter
public void SetD31Reset(string value)
{
    _D31Reset = value;
}

// Get<>AsString()
public string GetD31ResetAsString()
{
    return _D31Reset.PadRight(15);
}

// Set<>AsString()
public void SetD31ResetAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31Reset = value;
}

// Standard Getter
public string GetD31OrientationLandscape()
{
    return _D31OrientationLandscape;
}

// Standard Setter
public void SetD31OrientationLandscape(string value)
{
    _D31OrientationLandscape = value;
}

// Get<>AsString()
public string GetD31OrientationLandscapeAsString()
{
    return _D31OrientationLandscape.PadRight(15);
}

// Set<>AsString()
public void SetD31OrientationLandscapeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31OrientationLandscape = value;
}

// Standard Getter
public string GetD31SysmbolSetIbmPc()
{
    return _D31SysmbolSetIbmPc;
}

// Standard Setter
public void SetD31SysmbolSetIbmPc(string value)
{
    _D31SysmbolSetIbmPc = value;
}

// Get<>AsString()
public string GetD31SysmbolSetIbmPcAsString()
{
    return _D31SysmbolSetIbmPc.PadRight(15);
}

// Set<>AsString()
public void SetD31SysmbolSetIbmPcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31SysmbolSetIbmPc = value;
}

// Standard Getter
public string GetD31SpacingFixed()
{
    return _D31SpacingFixed;
}

// Standard Setter
public void SetD31SpacingFixed(string value)
{
    _D31SpacingFixed = value;
}

// Get<>AsString()
public string GetD31SpacingFixedAsString()
{
    return _D31SpacingFixed.PadRight(15);
}

// Set<>AsString()
public void SetD31SpacingFixedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31SpacingFixed = value;
}

// Standard Getter
public string GetD31Pitch1666()
{
    return _D31Pitch1666;
}

// Standard Setter
public void SetD31Pitch1666(string value)
{
    _D31Pitch1666 = value;
}

// Get<>AsString()
public string GetD31Pitch1666AsString()
{
    return _D31Pitch1666.PadRight(15);
}

// Set<>AsString()
public void SetD31Pitch1666AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31Pitch1666 = value;
}

// Standard Getter
public string GetD31PointSize85()
{
    return _D31PointSize85;
}

// Standard Setter
public void SetD31PointSize85(string value)
{
    _D31PointSize85 = value;
}

// Get<>AsString()
public string GetD31PointSize85AsString()
{
    return _D31PointSize85.PadRight(15);
}

// Set<>AsString()
public void SetD31PointSize85AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31PointSize85 = value;
}

// Standard Getter
public string GetD31StyleUpright()
{
    return _D31StyleUpright;
}

// Standard Setter
public void SetD31StyleUpright(string value)
{
    _D31StyleUpright = value;
}

// Get<>AsString()
public string GetD31StyleUprightAsString()
{
    return _D31StyleUpright.PadRight(15);
}

// Set<>AsString()
public void SetD31StyleUprightAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31StyleUpright = value;
}

// Standard Getter
public string GetD31StrokeWeightNormal()
{
    return _D31StrokeWeightNormal;
}

// Standard Setter
public void SetD31StrokeWeightNormal(string value)
{
    _D31StrokeWeightNormal = value;
}

// Get<>AsString()
public string GetD31StrokeWeightNormalAsString()
{
    return _D31StrokeWeightNormal.PadRight(15);
}

// Set<>AsString()
public void SetD31StrokeWeightNormalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31StrokeWeightNormal = value;
}

// Standard Getter
public string GetD31TypefaceLinePrinter()
{
    return _D31TypefaceLinePrinter;
}

// Standard Setter
public void SetD31TypefaceLinePrinter(string value)
{
    _D31TypefaceLinePrinter = value;
}

// Get<>AsString()
public string GetD31TypefaceLinePrinterAsString()
{
    return _D31TypefaceLinePrinter.PadRight(15);
}

// Set<>AsString()
public void SetD31TypefaceLinePrinterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31TypefaceLinePrinter = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD31GroupBPrintCommands(string value)
{
    _D31GroupBPrintCommands.SetD31GroupBPrintCommandsAsString(value);
}
// Nested Class: D31GroupBPrintCommands
public class D31GroupBPrintCommands
{
    private static int _size = 115;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D318LinesPerInch, is_external=, is_static_class=False, static_prefix=
    private string _D318LinesPerInch ="";
    
    
    
    
    // [DEBUG] Field: D31Copies1, is_external=, is_static_class=False, static_prefix=
    private string _D31Copies1 ="";
    
    
    
    
    // [DEBUG] Field: D31PageSizeA4, is_external=, is_static_class=False, static_prefix=
    private string _D31PageSizeA4 ="";
    
    
    
    
    // [DEBUG] Field: D31TopMargin2, is_external=, is_static_class=False, static_prefix=
    private string _D31TopMargin2 ="";
    
    
    
    
    // [DEBUG] Field: D31LeftMargin15, is_external=, is_static_class=False, static_prefix=
    private string _D31LeftMargin15 ="";
    
    
    
    
    // [DEBUG] Field: D31TextLength60, is_external=, is_static_class=False, static_prefix=
    private string _D31TextLength60 ="";
    
    
    
    
    // [DEBUG] Field: D31OtherCommands, is_external=, is_static_class=False, static_prefix=
    private string _D31OtherCommands ="";
    
    
    
    
public D31GroupBPrintCommands() {}

public D31GroupBPrintCommands(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD318LinesPerInch(data.Substring(offset, 15).Trim());
    offset += 15;
    SetD31Copies1(data.Substring(offset, 10).Trim());
    offset += 10;
    SetD31PageSizeA4(data.Substring(offset, 10).Trim());
    offset += 10;
    SetD31TopMargin2(data.Substring(offset, 10).Trim());
    offset += 10;
    SetD31LeftMargin15(data.Substring(offset, 10).Trim());
    offset += 10;
    SetD31TextLength60(data.Substring(offset, 10).Trim());
    offset += 10;
    SetD31OtherCommands(data.Substring(offset, 50).Trim());
    offset += 50;
    
}

// Serialization methods
public string GetD31GroupBPrintCommandsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D318LinesPerInch.PadRight(15));
    result.Append(_D31Copies1.PadRight(10));
    result.Append(_D31PageSizeA4.PadRight(10));
    result.Append(_D31TopMargin2.PadRight(10));
    result.Append(_D31LeftMargin15.PadRight(10));
    result.Append(_D31TextLength60.PadRight(10));
    result.Append(_D31OtherCommands.PadRight(50));
    
    return result.ToString();
}

public void SetD31GroupBPrintCommandsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetD318LinesPerInch(extracted);
    }
    offset += 15;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetD31Copies1(extracted);
    }
    offset += 10;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetD31PageSizeA4(extracted);
    }
    offset += 10;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetD31TopMargin2(extracted);
    }
    offset += 10;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetD31LeftMargin15(extracted);
    }
    offset += 10;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetD31TextLength60(extracted);
    }
    offset += 10;
    if (offset + 50 <= data.Length)
    {
        string extracted = data.Substring(offset, 50).Trim();
        SetD31OtherCommands(extracted);
    }
    offset += 50;
}

// Getter and Setter methods

// Standard Getter
public string GetD318LinesPerInch()
{
    return _D318LinesPerInch;
}

// Standard Setter
public void SetD318LinesPerInch(string value)
{
    _D318LinesPerInch = value;
}

// Get<>AsString()
public string GetD318LinesPerInchAsString()
{
    return _D318LinesPerInch.PadRight(15);
}

// Set<>AsString()
public void SetD318LinesPerInchAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D318LinesPerInch = value;
}

// Standard Getter
public string GetD31Copies1()
{
    return _D31Copies1;
}

// Standard Setter
public void SetD31Copies1(string value)
{
    _D31Copies1 = value;
}

// Get<>AsString()
public string GetD31Copies1AsString()
{
    return _D31Copies1.PadRight(10);
}

// Set<>AsString()
public void SetD31Copies1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31Copies1 = value;
}

// Standard Getter
public string GetD31PageSizeA4()
{
    return _D31PageSizeA4;
}

// Standard Setter
public void SetD31PageSizeA4(string value)
{
    _D31PageSizeA4 = value;
}

// Get<>AsString()
public string GetD31PageSizeA4AsString()
{
    return _D31PageSizeA4.PadRight(10);
}

// Set<>AsString()
public void SetD31PageSizeA4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31PageSizeA4 = value;
}

// Standard Getter
public string GetD31TopMargin2()
{
    return _D31TopMargin2;
}

// Standard Setter
public void SetD31TopMargin2(string value)
{
    _D31TopMargin2 = value;
}

// Get<>AsString()
public string GetD31TopMargin2AsString()
{
    return _D31TopMargin2.PadRight(10);
}

// Set<>AsString()
public void SetD31TopMargin2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31TopMargin2 = value;
}

// Standard Getter
public string GetD31LeftMargin15()
{
    return _D31LeftMargin15;
}

// Standard Setter
public void SetD31LeftMargin15(string value)
{
    _D31LeftMargin15 = value;
}

// Get<>AsString()
public string GetD31LeftMargin15AsString()
{
    return _D31LeftMargin15.PadRight(10);
}

// Set<>AsString()
public void SetD31LeftMargin15AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31LeftMargin15 = value;
}

// Standard Getter
public string GetD31TextLength60()
{
    return _D31TextLength60;
}

// Standard Setter
public void SetD31TextLength60(string value)
{
    _D31TextLength60 = value;
}

// Get<>AsString()
public string GetD31TextLength60AsString()
{
    return _D31TextLength60.PadRight(10);
}

// Set<>AsString()
public void SetD31TextLength60AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31TextLength60 = value;
}

// Standard Getter
public string GetD31OtherCommands()
{
    return _D31OtherCommands;
}

// Standard Setter
public void SetD31OtherCommands(string value)
{
    _D31OtherCommands = value;
}

// Get<>AsString()
public string GetD31OtherCommandsAsString()
{
    return _D31OtherCommands.PadRight(50);
}

// Set<>AsString()
public void SetD31OtherCommandsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31OtherCommands = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD31InverseKey(string value)
{
    _D31InverseKey.SetD31InverseKeyAsString(value);
}
// Nested Class: D31InverseKey
public class D31InverseKey
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D31InversePrinterTableCode, is_external=, is_static_class=False, static_prefix=
    private string _D31InversePrinterTableCode ="";
    
    
    
    
public D31InverseKey() {}

public D31InverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD31InversePrinterTableCode(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD31InverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D31InversePrinterTableCode.PadRight(2));
    
    return result.ToString();
}

public void SetD31InverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD31InversePrinterTableCode(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD31InversePrinterTableCode()
{
    return _D31InversePrinterTableCode;
}

// Standard Setter
public void SetD31InversePrinterTableCode(string value)
{
    _D31InversePrinterTableCode = value;
}

// Get<>AsString()
public string GetD31InversePrinterTableCodeAsString()
{
    return _D31InversePrinterTableCode.PadRight(2);
}

// Set<>AsString()
public void SetD31InversePrinterTableCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31InversePrinterTableCode = value;
}



public static int GetSize()
{
    return _size;
}

}

}}