using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D38Record Data Structure

public class D38Record
{
    private static int _size = 91;
    // [DEBUG] Class: D38Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D38Key, is_external=, is_static_class=False, static_prefix=
    private D38Key _D38Key = new D38Key();
    
    
    
    
    // [DEBUG] Field: D38Text, is_external=, is_static_class=False, static_prefix=
    private string _D38Text ="";
    
    
    
    
    
    // Serialization methods
    public string GetD38RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D38Key.GetD38KeyAsString());
        result.Append(_D38Text.PadRight(75));
        
        return result.ToString();
    }
    
    public void SetD38RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            _D38Key.SetD38KeyAsString(data.Substring(offset, 16));
        }
        else
        {
            _D38Key.SetD38KeyAsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 75 <= data.Length)
        {
            string extracted = data.Substring(offset, 75).Trim();
            SetD38Text(extracted);
        }
        offset += 75;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD38RecordAsString();
    }
    // Set<>String Override function
    public void SetD38Record(string value)
    {
        SetD38RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D38Key GetD38Key()
    {
        return _D38Key;
    }
    
    // Standard Setter
    public void SetD38Key(D38Key value)
    {
        _D38Key = value;
    }
    
    // Get<>AsString()
    public string GetD38KeyAsString()
    {
        return _D38Key != null ? _D38Key.GetD38KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD38KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D38Key == null)
        {
            _D38Key = new D38Key();
        }
        _D38Key.SetD38KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD38Text()
    {
        return _D38Text;
    }
    
    // Standard Setter
    public void SetD38Text(string value)
    {
        _D38Text = value;
    }
    
    // Get<>AsString()
    public string GetD38TextAsString()
    {
        return _D38Text.PadRight(75);
    }
    
    // Set<>AsString()
    public void SetD38TextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D38Text = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD38Key(string value)
    {
        _D38Key.SetD38KeyAsString(value);
    }
    // Nested Class: D38Key
    public class D38Key
    {
        private static int _size = 16;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D38Program, is_external=, is_static_class=False, static_prefix=
        private string _D38Program ="";
        
        
        
        
        // [DEBUG] Field: D38Field, is_external=, is_static_class=False, static_prefix=
        private int _D38Field =0;
        
        
        
        
        // [DEBUG] Field: D38PageNo, is_external=, is_static_class=False, static_prefix=
        private int _D38PageNo =0;
        
        
        
        
        // [DEBUG] Field: D38LineNo, is_external=, is_static_class=False, static_prefix=
        private int _D38LineNo =0;
        
        
        
        
    public D38Key() {}
    
    public D38Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD38Program(data.Substring(offset, 8).Trim());
        offset += 8;
        SetD38Field(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        SetD38PageNo(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        SetD38LineNo(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD38KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D38Program.PadRight(8));
        result.Append(_D38Field.ToString().PadLeft(3, '0'));
        result.Append(_D38PageNo.ToString().PadLeft(3, '0'));
        result.Append(_D38LineNo.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetD38KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD38Program(extracted);
        }
        offset += 8;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD38Field(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD38PageNo(parsedInt);
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD38LineNo(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD38Program()
    {
        return _D38Program;
    }
    
    // Standard Setter
    public void SetD38Program(string value)
    {
        _D38Program = value;
    }
    
    // Get<>AsString()
    public string GetD38ProgramAsString()
    {
        return _D38Program.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD38ProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D38Program = value;
    }
    
    // Standard Getter
    public int GetD38Field()
    {
        return _D38Field;
    }
    
    // Standard Setter
    public void SetD38Field(int value)
    {
        _D38Field = value;
    }
    
    // Get<>AsString()
    public string GetD38FieldAsString()
    {
        return _D38Field.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetD38FieldAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D38Field = parsed;
    }
    
    // Standard Getter
    public int GetD38PageNo()
    {
        return _D38PageNo;
    }
    
    // Standard Setter
    public void SetD38PageNo(int value)
    {
        _D38PageNo = value;
    }
    
    // Get<>AsString()
    public string GetD38PageNoAsString()
    {
        return _D38PageNo.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetD38PageNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D38PageNo = parsed;
    }
    
    // Standard Getter
    public int GetD38LineNo()
    {
        return _D38LineNo;
    }
    
    // Standard Setter
    public void SetD38LineNo(int value)
    {
        _D38LineNo = value;
    }
    
    // Get<>AsString()
    public string GetD38LineNoAsString()
    {
        return _D38LineNo.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD38LineNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D38LineNo = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}