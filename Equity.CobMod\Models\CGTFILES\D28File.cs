using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D28File Data Structure

public class D28File
{
    private static int _size = 12;
    // [DEBUG] Class: D28File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler199, is_external=, is_static_class=False, static_prefix=
    private string _Filler199 ="$";
    
    
    
    
    // [DEBUG] Field: D28UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D28UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler200, is_external=, is_static_class=False, static_prefix=
    private string _Filler200 ="R9";
    
    
    
    
    // [DEBUG] Field: D28ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D28ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler201, is_external=, is_static_class=False, static_prefix=
    private string _Filler201 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD28FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler199.PadRight(1));
        result.Append(_D28UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler200.PadRight(2));
        result.Append(_D28ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler201.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD28FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller199(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD28UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller200(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD28ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller201(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD28FileAsString();
    }
    // Set<>String Override function
    public void SetD28File(string value)
    {
        SetD28FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller199()
    {
        return _Filler199;
    }
    
    // Standard Setter
    public void SetFiller199(string value)
    {
        _Filler199 = value;
    }
    
    // Get<>AsString()
    public string GetFiller199AsString()
    {
        return _Filler199.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller199AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler199 = value;
    }
    
    // Standard Getter
    public int GetD28UserNo()
    {
        return _D28UserNo;
    }
    
    // Standard Setter
    public void SetD28UserNo(int value)
    {
        _D28UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD28UserNoAsString()
    {
        return _D28UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD28UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D28UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller200()
    {
        return _Filler200;
    }
    
    // Standard Setter
    public void SetFiller200(string value)
    {
        _Filler200 = value;
    }
    
    // Get<>AsString()
    public string GetFiller200AsString()
    {
        return _Filler200.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller200AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler200 = value;
    }
    
    // Standard Getter
    public int GetD28ReportNo()
    {
        return _D28ReportNo;
    }
    
    // Standard Setter
    public void SetD28ReportNo(int value)
    {
        _D28ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD28ReportNoAsString()
    {
        return _D28ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD28ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D28ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller201()
    {
        return _Filler201;
    }
    
    // Standard Setter
    public void SetFiller201(string value)
    {
        _Filler201 = value;
    }
    
    // Get<>AsString()
    public string GetFiller201AsString()
    {
        return _Filler201.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller201AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler201 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}