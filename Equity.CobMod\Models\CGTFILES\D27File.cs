using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D27File Data Structure

public class D27File
{
    private static int _size = 12;
    // [DEBUG] Class: D27File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler196, is_external=, is_static_class=False, static_prefix=
    private string _Filler196 ="$";
    
    
    
    
    // [DEBUG] Field: D27UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D27UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler197, is_external=, is_static_class=False, static_prefix=
    private string _Filler197 ="R8";
    
    
    
    
    // [DEBUG] Field: D27ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D27ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler198, is_external=, is_static_class=False, static_prefix=
    private string _Filler198 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD27FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler196.PadRight(1));
        result.Append(_D27UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler197.PadRight(2));
        result.Append(_D27ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler198.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD27FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller196(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD27UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller197(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD27ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller198(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD27FileAsString();
    }
    // Set<>String Override function
    public void SetD27File(string value)
    {
        SetD27FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller196()
    {
        return _Filler196;
    }
    
    // Standard Setter
    public void SetFiller196(string value)
    {
        _Filler196 = value;
    }
    
    // Get<>AsString()
    public string GetFiller196AsString()
    {
        return _Filler196.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller196AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler196 = value;
    }
    
    // Standard Getter
    public int GetD27UserNo()
    {
        return _D27UserNo;
    }
    
    // Standard Setter
    public void SetD27UserNo(int value)
    {
        _D27UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD27UserNoAsString()
    {
        return _D27UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD27UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D27UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller197()
    {
        return _Filler197;
    }
    
    // Standard Setter
    public void SetFiller197(string value)
    {
        _Filler197 = value;
    }
    
    // Get<>AsString()
    public string GetFiller197AsString()
    {
        return _Filler197.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller197AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler197 = value;
    }
    
    // Standard Getter
    public int GetD27ReportNo()
    {
        return _D27ReportNo;
    }
    
    // Standard Setter
    public void SetD27ReportNo(int value)
    {
        _D27ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD27ReportNoAsString()
    {
        return _D27ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD27ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D27ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller198()
    {
        return _Filler198;
    }
    
    // Standard Setter
    public void SetFiller198(string value)
    {
        _Filler198 = value;
    }
    
    // Get<>AsString()
    public string GetFiller198AsString()
    {
        return _Filler198.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller198AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler198 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}