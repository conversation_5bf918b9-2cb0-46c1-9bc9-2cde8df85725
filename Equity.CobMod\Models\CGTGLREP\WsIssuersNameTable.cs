using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsIssuersNameTable Data Structure

public class WsIssuersNameTable
{
    private static int _size = 35;
    // [DEBUG] Class: WsIssuersNameTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WIssuerChar, is_external=, is_static_class=False, static_prefix=
    private string[] _WIssuerChar = new string[35];
    
    
    
    
    
    // Serialization methods
    public string GetWsIssuersNameTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 35; i++)
        {
            result.Append(_WIssuerChar[i].PadRight(1));
        }
        
        return result.ToString();
    }
    
    public void SetWsIssuersNameTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 35; i++)
        {
            if (offset + 1 > data.Length) break;
            string val = data.Substring(offset, 1);
            
            _WIssuerChar[i] = val.Trim();
            offset += 1;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsIssuersNameTableAsString();
    }
    // Set<>String Override function
    public void SetWsIssuersNameTable(string value)
    {
        SetWsIssuersNameTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for WIssuerChar
    public string GetWIssuerCharAt(int index)
    {
        return _WIssuerChar[index];
    }
    
    public void SetWIssuerCharAt(int index, string value)
    {
        _WIssuerChar[index] = value;
    }
    
    public string GetWIssuerCharAsStringAt(int index)
    {
        return _WIssuerChar[index].PadRight(1);
    }
    
    public void SetWIssuerCharAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WIssuerChar[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWIssuerChar()
    {
        return _WIssuerChar != null && _WIssuerChar.Length > 0
        ? _WIssuerChar[0]
        : default(string);
    }
    
    public void SetWIssuerChar(string value)
    {
        if (_WIssuerChar == null || _WIssuerChar.Length == 0)
        _WIssuerChar = new string[1];
        _WIssuerChar[0] = value;
    }
    
    public string GetWIssuerCharAsString()
    {
        return _WIssuerChar != null && _WIssuerChar.Length > 0
        ? _WIssuerChar[0].ToString()
        : string.Empty;
    }
    
    public void SetWIssuerCharAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WIssuerChar == null || _WIssuerChar.Length == 0)
        _WIssuerChar = new string[1];
        
        _WIssuerChar[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}