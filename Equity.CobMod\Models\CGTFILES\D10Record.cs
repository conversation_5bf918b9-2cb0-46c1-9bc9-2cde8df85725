using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D10Record Data Structure

public class D10Record
{
    private static int _size = 133;
    // [DEBUG] Class: D10Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D10PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D10PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D10ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D10ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD10RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D10PrintControl.PadRight(1));
        result.Append(_D10ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD10RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD10PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD10ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD10RecordAsString();
    }
    // Set<>String Override function
    public void SetD10Record(string value)
    {
        SetD10RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD10PrintControl()
    {
        return _D10PrintControl;
    }
    
    // Standard Setter
    public void SetD10PrintControl(string value)
    {
        _D10PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD10PrintControlAsString()
    {
        return _D10PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD10PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D10PrintControl = value;
    }
    
    // Standard Getter
    public string GetD10ReportLine()
    {
        return _D10ReportLine;
    }
    
    // Standard Setter
    public void SetD10ReportLine(string value)
    {
        _D10ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD10ReportLineAsString()
    {
        return _D10ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD10ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D10ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}