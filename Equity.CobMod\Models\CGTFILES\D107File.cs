using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D107File Data Structure

public class D107File
{
    private static int _size = 12;
    // [DEBUG] Class: D107File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler306, is_external=, is_static_class=False, static_prefix=
    private string _Filler306 ="$";
    
    
    
    
    // [DEBUG] Field: D107UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D107UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler307, is_external=, is_static_class=False, static_prefix=
    private string _Filler307 ="END";
    
    
    
    
    // [DEBUG] Field: Filler308, is_external=, is_static_class=False, static_prefix=
    private string _Filler308 =".TXT";
    
    
    
    
    
    // Serialization methods
    public string GetD107FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler306.PadRight(1));
        result.Append(_D107UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler307.PadRight(3));
        result.Append(_Filler308.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD107FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller306(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD107UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetFiller307(extracted);
        }
        offset += 3;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller308(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD107FileAsString();
    }
    // Set<>String Override function
    public void SetD107File(string value)
    {
        SetD107FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller306()
    {
        return _Filler306;
    }
    
    // Standard Setter
    public void SetFiller306(string value)
    {
        _Filler306 = value;
    }
    
    // Get<>AsString()
    public string GetFiller306AsString()
    {
        return _Filler306.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller306AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler306 = value;
    }
    
    // Standard Getter
    public int GetD107UserNo()
    {
        return _D107UserNo;
    }
    
    // Standard Setter
    public void SetD107UserNo(int value)
    {
        _D107UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD107UserNoAsString()
    {
        return _D107UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD107UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D107UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller307()
    {
        return _Filler307;
    }
    
    // Standard Setter
    public void SetFiller307(string value)
    {
        _Filler307 = value;
    }
    
    // Get<>AsString()
    public string GetFiller307AsString()
    {
        return _Filler307.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetFiller307AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler307 = value;
    }
    
    // Standard Getter
    public string GetFiller308()
    {
        return _Filler308;
    }
    
    // Standard Setter
    public void SetFiller308(string value)
    {
        _Filler308 = value;
    }
    
    // Get<>AsString()
    public string GetFiller308AsString()
    {
        return _Filler308.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller308AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler308 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}