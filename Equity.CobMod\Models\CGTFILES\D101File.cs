using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D101File Data Structure

public class D101File
{
    private static int _size = 12;
    // [DEBUG] Class: D101File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler291, is_external=, is_static_class=False, static_prefix=
    private string _Filler291 ="$";
    
    
    
    
    // [DEBUG] Field: D101UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D101UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler292, is_external=, is_static_class=False, static_prefix=
    private string _Filler292 ="DQ";
    
    
    
    
    // [DEBUG] Field: D101ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D101ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler293, is_external=, is_static_class=False, static_prefix=
    private string _Filler293 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD101FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler291.PadRight(1));
        result.Append(_D101UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler292.PadRight(2));
        result.Append(_D101ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler293.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD101FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller291(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD101UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller292(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD101ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller293(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD101FileAsString();
    }
    // Set<>String Override function
    public void SetD101File(string value)
    {
        SetD101FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller291()
    {
        return _Filler291;
    }
    
    // Standard Setter
    public void SetFiller291(string value)
    {
        _Filler291 = value;
    }
    
    // Get<>AsString()
    public string GetFiller291AsString()
    {
        return _Filler291.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller291AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler291 = value;
    }
    
    // Standard Getter
    public int GetD101UserNo()
    {
        return _D101UserNo;
    }
    
    // Standard Setter
    public void SetD101UserNo(int value)
    {
        _D101UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD101UserNoAsString()
    {
        return _D101UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD101UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D101UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller292()
    {
        return _Filler292;
    }
    
    // Standard Setter
    public void SetFiller292(string value)
    {
        _Filler292 = value;
    }
    
    // Get<>AsString()
    public string GetFiller292AsString()
    {
        return _Filler292.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller292AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler292 = value;
    }
    
    // Standard Getter
    public int GetD101ReportNo()
    {
        return _D101ReportNo;
    }
    
    // Standard Setter
    public void SetD101ReportNo(int value)
    {
        _D101ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD101ReportNoAsString()
    {
        return _D101ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD101ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D101ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller293()
    {
        return _Filler293;
    }
    
    // Standard Setter
    public void SetFiller293(string value)
    {
        _Filler293 = value;
    }
    
    // Get<>AsString()
    public string GetFiller293AsString()
    {
        return _Filler293.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller293AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler293 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}