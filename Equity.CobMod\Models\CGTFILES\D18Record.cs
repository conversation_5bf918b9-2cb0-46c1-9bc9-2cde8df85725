using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D18Record Data Structure

public class D18Record
{
    private static int _size = 133;
    // [DEBUG] Class: D18Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D18PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D18PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D18ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D18ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD18RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D18PrintControl.PadRight(1));
        result.Append(_D18ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD18RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD18PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD18ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD18RecordAsString();
    }
    // Set<>String Override function
    public void SetD18Record(string value)
    {
        SetD18RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD18PrintControl()
    {
        return _D18PrintControl;
    }
    
    // Standard Setter
    public void SetD18PrintControl(string value)
    {
        _D18PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD18PrintControlAsString()
    {
        return _D18PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD18PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D18PrintControl = value;
    }
    
    // Standard Getter
    public string GetD18ReportLine()
    {
        return _D18ReportLine;
    }
    
    // Standard Setter
    public void SetD18ReportLine(string value)
    {
        _D18ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD18ReportLineAsString()
    {
        return _D18ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD18ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D18ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}