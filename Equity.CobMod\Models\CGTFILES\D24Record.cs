using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D24Record Data Structure

public class D24Record
{
    private static int _size = 133;
    // [DEBUG] Class: D24Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D24PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D24PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D24ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D24ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD24RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D24PrintControl.PadRight(1));
        result.Append(_D24ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD24RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD24PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD24ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD24RecordAsString();
    }
    // Set<>String Override function
    public void SetD24Record(string value)
    {
        SetD24RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD24PrintControl()
    {
        return _D24PrintControl;
    }
    
    // Standard Setter
    public void SetD24PrintControl(string value)
    {
        _D24PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD24PrintControlAsString()
    {
        return _D24PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD24PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D24PrintControl = value;
    }
    
    // Standard Getter
    public string GetD24ReportLine()
    {
        return _D24ReportLine;
    }
    
    // Standard Setter
    public void SetD24ReportLine(string value)
    {
        _D24ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD24ReportLineAsString()
    {
        return _D24ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD24ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D24ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}