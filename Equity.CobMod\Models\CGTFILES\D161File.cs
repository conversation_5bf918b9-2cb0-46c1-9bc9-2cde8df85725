using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D161File Data Structure

public class D161File
{
    private static int _size = 12;
    // [DEBUG] Class: D161File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler325, is_external=, is_static_class=False, static_prefix=
    private string _Filler325 ="$";
    
    
    
    
    // [DEBUG] Field: D161UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D161UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler326, is_external=, is_static_class=False, static_prefix=
    private string _Filler326 ="EXD.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD161FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler325.PadRight(1));
        result.Append(_D161UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler326.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD161FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller325(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD161UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller326(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD161FileAsString();
    }
    // Set<>String Override function
    public void SetD161File(string value)
    {
        SetD161FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller325()
    {
        return _Filler325;
    }
    
    // Standard Setter
    public void SetFiller325(string value)
    {
        _Filler325 = value;
    }
    
    // Get<>AsString()
    public string GetFiller325AsString()
    {
        return _Filler325.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller325AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler325 = value;
    }
    
    // Standard Getter
    public int GetD161UserNo()
    {
        return _D161UserNo;
    }
    
    // Standard Setter
    public void SetD161UserNo(int value)
    {
        _D161UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD161UserNoAsString()
    {
        return _D161UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD161UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D161UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller326()
    {
        return _Filler326;
    }
    
    // Standard Setter
    public void SetFiller326(string value)
    {
        _Filler326 = value;
    }
    
    // Get<>AsString()
    public string GetFiller326AsString()
    {
        return _Filler326.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller326AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler326 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}