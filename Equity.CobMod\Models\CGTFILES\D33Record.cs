using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D33Record Data Structure

public class D33Record
{
    private static int _size = 501;
    // [DEBUG] Class: D33Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D33PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D33PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D33ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D33ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD33RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D33PrintControl.PadRight(1));
        result.Append(_D33ReportLine.PadRight(500));
        
        return result.ToString();
    }
    
    public void SetD33RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD33PrintControl(extracted);
        }
        offset += 1;
        if (offset + 500 <= data.Length)
        {
            string extracted = data.Substring(offset, 500).Trim();
            SetD33ReportLine(extracted);
        }
        offset += 500;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD33RecordAsString();
    }
    // Set<>String Override function
    public void SetD33Record(string value)
    {
        SetD33RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD33PrintControl()
    {
        return _D33PrintControl;
    }
    
    // Standard Setter
    public void SetD33PrintControl(string value)
    {
        _D33PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD33PrintControlAsString()
    {
        return _D33PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD33PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D33PrintControl = value;
    }
    
    // Standard Getter
    public string GetD33ReportLine()
    {
        return _D33ReportLine;
    }
    
    // Standard Setter
    public void SetD33ReportLine(string value)
    {
        _D33ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD33ReportLineAsString()
    {
        return _D33ReportLine.PadRight(500);
    }
    
    // Set<>AsString()
    public void SetD33ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D33ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}