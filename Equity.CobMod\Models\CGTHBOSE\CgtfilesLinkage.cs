using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgthboseDTO
{// DTO class representing CgtfilesLinkage Data Structure

public class CgtfilesLinkage
{
    private static int _size = 16;
    // [DEBUG] Class: CgtfilesLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LFileName, is_external=, is_static_class=False, static_prefix=
    private string _LFileName ="";
    
    
    
    
    // [DEBUG] Field: LFileAction, is_external=, is_static_class=False, static_prefix=
    private string _LFileAction ="";
    
    
    
    
    // [DEBUG] Field: LFileReturnCode, is_external=, is_static_class=False, static_prefix=
    private string _LFileReturnCode ="";
    
    
    // 88-level condition checks for LFileReturnCode
    public bool IsSuccessful()
    {
        if (this._LFileReturnCode == "00") return true;
        return false;
    }
    public bool IsOpenOk()
    {
        if (this._LFileReturnCode == "00") return true;
        if (this._LFileReturnCode == "05") return true;
        if (this._LFileReturnCode == "41") return true;
        return false;
    }
    public bool IsOpenCreatedNewFile()
    {
        if (this._LFileReturnCode == "05") return true;
        return false;
    }
    public bool IsFileWasAlreadyOpen()
    {
        if (this._LFileReturnCode == "41") return true;
        return false;
    }
    public bool IsEndOfFile()
    {
        if (this._LFileReturnCode == "10") return true;
        return false;
    }
    public bool IsInvalidKey()
    {
        if (this._LFileReturnCode == "23") return true;
        return false;
    }
    public bool IsDuplicateKey()
    {
        if (this._LFileReturnCode == "22") return true;
        return false;
    }
    public bool IsDuplicateAlternateKey()
    {
        if (this._LFileReturnCode == "02") return true;
        return false;
    }
    public bool IsRecordLocked()
    {
        if (this._LFileReturnCode == "9D") return true;
        return false;
    }
    public bool IsFileMissingForOpen()
    {
        if (this._LFileReturnCode == "35") return true;
        return false;
    }
    
    
    // [DEBUG] Field: LFileReturnCode2, is_external=, is_static_class=False, static_prefix=
    private LFileReturnCode2 _LFileReturnCode2 = new LFileReturnCode2();
    
    
    
    
    // [DEBUG] Field: LReportNo, is_external=, is_static_class=False, static_prefix=
    private string _LReportNo ="";
    
    
    
    
    
    // Serialization methods
    public string GetCgtfilesLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LFileName.PadRight(8));
        result.Append(_LFileAction.PadRight(3));
        result.Append(_LFileReturnCode.PadRight(2));
        result.Append(_LFileReturnCode2.GetLFileReturnCode2AsString());
        result.Append(_LReportNo.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetCgtfilesLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetLFileName(extracted);
        }
        offset += 8;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetLFileAction(extracted);
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetLFileReturnCode(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            _LFileReturnCode2.SetLFileReturnCode2AsString(data.Substring(offset, 2));
        }
        else
        {
            _LFileReturnCode2.SetLFileReturnCode2AsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLReportNo(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtfilesLinkageAsString();
    }
    // Set<>String Override function
    public void SetCgtfilesLinkage(string value)
    {
        SetCgtfilesLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLFileName()
    {
        return _LFileName;
    }
    
    // Standard Setter
    public void SetLFileName(string value)
    {
        _LFileName = value;
    }
    
    // Get<>AsString()
    public string GetLFileNameAsString()
    {
        return _LFileName.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetLFileNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LFileName = value;
    }
    
    // Standard Getter
    public string GetLFileAction()
    {
        return _LFileAction;
    }
    
    // Standard Setter
    public void SetLFileAction(string value)
    {
        _LFileAction = value;
    }
    
    // Get<>AsString()
    public string GetLFileActionAsString()
    {
        return _LFileAction.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetLFileActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LFileAction = value;
    }
    
    // Standard Getter
    public string GetLFileReturnCode()
    {
        return _LFileReturnCode;
    }
    
    // Standard Setter
    public void SetLFileReturnCode(string value)
    {
        _LFileReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetLFileReturnCodeAsString()
    {
        return _LFileReturnCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetLFileReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LFileReturnCode = value;
    }
    
    // Standard Getter
    public LFileReturnCode2 GetLFileReturnCode2()
    {
        return _LFileReturnCode2;
    }
    
    // Standard Setter
    public void SetLFileReturnCode2(LFileReturnCode2 value)
    {
        _LFileReturnCode2 = value;
    }
    
    // Get<>AsString()
    public string GetLFileReturnCode2AsString()
    {
        return _LFileReturnCode2 != null ? _LFileReturnCode2.GetLFileReturnCode2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetLFileReturnCode2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LFileReturnCode2 == null)
        {
            _LFileReturnCode2 = new LFileReturnCode2();
        }
        _LFileReturnCode2.SetLFileReturnCode2AsString(value);
    }
    
    // Standard Getter
    public string GetLReportNo()
    {
        return _LReportNo;
    }
    
    // Standard Setter
    public void SetLReportNo(string value)
    {
        _LReportNo = value;
    }
    
    // Get<>AsString()
    public string GetLReportNoAsString()
    {
        return _LReportNo.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LReportNo = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLFileReturnCode2(string value)
    {
        _LFileReturnCode2.SetLFileReturnCode2AsString(value);
    }
    // Nested Class: LFileReturnCode2
    public class LFileReturnCode2
    {
        private static int _size = 2;
        
        // Fields in the class
        
        
        // [DEBUG] Field: LStatus1, is_external=, is_static_class=False, static_prefix=
        private string _LStatus1 ="";
        
        
        // 88-level condition checks for LStatus1
        public bool IsRunTimeError()
        {
            if (this._LStatus1 == "9") return true;
            return false;
        }
        
        
        // [DEBUG] Field: LStatus2, is_external=, is_static_class=False, static_prefix=
        private string _LStatus2 ="";
        
        
        // 88-level condition checks for LStatus2
        public bool IsDiskFull()
        {
            if (!string.IsNullOrEmpty(this._LStatus2))
            {
                if (Convert.ToByte(this._LStatus2, 16) == 0x07) return true;
            }
            return false;
        }
        public bool IsDirectoryFull()
        {
            if (!string.IsNullOrEmpty(this._LStatus2))
            {
                if (Convert.ToByte(this._LStatus2, 16) == 0x09) return true;
            }
            return false;
        }
        
        
    public LFileReturnCode2() {}
    
    public LFileReturnCode2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetLStatus1(data.Substring(offset, 1).Trim());
        offset += 1;
        SetLStatus2(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetLFileReturnCode2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LStatus1.PadRight(1));
        result.Append(_LStatus2.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetLFileReturnCode2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLStatus1(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLStatus2(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLStatus1()
    {
        return _LStatus1;
    }
    
    // Standard Setter
    public void SetLStatus1(string value)
    {
        _LStatus1 = value;
    }
    
    // Get<>AsString()
    public string GetLStatus1AsString()
    {
        return _LStatus1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLStatus1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LStatus1 = value;
    }
    
    // Standard Getter
    public string GetLStatus2()
    {
        return _LStatus2;
    }
    
    // Standard Setter
    public void SetLStatus2(string value)
    {
        _LStatus2 = value;
    }
    
    // Get<>AsString()
    public string GetLStatus2AsString()
    {
        return _LStatus2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLStatus2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LStatus2 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}