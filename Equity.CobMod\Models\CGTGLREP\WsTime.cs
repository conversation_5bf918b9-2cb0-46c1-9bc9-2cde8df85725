using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsTime Data Structure

public class WsTime
{
    private static int _size = 8;
    // [DEBUG] Class: WsTime, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsHour, is_external=, is_static_class=False, static_prefix=
    private int _WsHour =0;
    
    
    
    
    // [DEBUG] Field: WsMin, is_external=, is_static_class=False, static_prefix=
    private int _WsMin =0;
    
    
    
    
    // [DEBUG] Field: Filler31, is_external=, is_static_class=False, static_prefix=
    private int _Filler31 =0;
    
    
    
    
    // [DEBUG] Field: Filler32, is_external=, is_static_class=False, static_prefix=
    private int _Filler32 =0;
    
    
    
    
    
    // Serialization methods
    public string GetWsTimeAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsHour.ToString().PadLeft(2, '0'));
        result.Append(_WsMin.ToString().PadLeft(2, '0'));
        result.Append(_Filler31.ToString().PadLeft(2, '0'));
        result.Append(_Filler32.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWsTimeAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsHour(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMin(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetFiller31(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetFiller32(parsedInt);
        }
        offset += 2;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsTimeAsString();
    }
    // Set<>String Override function
    public void SetWsTime(string value)
    {
        SetWsTimeAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWsHour()
    {
        return _WsHour;
    }
    
    // Standard Setter
    public void SetWsHour(int value)
    {
        _WsHour = value;
    }
    
    // Get<>AsString()
    public string GetWsHourAsString()
    {
        return _WsHour.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsHourAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsHour = parsed;
    }
    
    // Standard Getter
    public int GetWsMin()
    {
        return _WsMin;
    }
    
    // Standard Setter
    public void SetWsMin(int value)
    {
        _WsMin = value;
    }
    
    // Get<>AsString()
    public string GetWsMinAsString()
    {
        return _WsMin.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMinAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMin = parsed;
    }
    
    // Standard Getter
    public int GetFiller31()
    {
        return _Filler31;
    }
    
    // Standard Setter
    public void SetFiller31(int value)
    {
        _Filler31 = value;
    }
    
    // Get<>AsString()
    public string GetFiller31AsString()
    {
        return _Filler31.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetFiller31AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Filler31 = parsed;
    }
    
    // Standard Getter
    public int GetFiller32()
    {
        return _Filler32;
    }
    
    // Standard Setter
    public void SetFiller32(int value)
    {
        _Filler32 = value;
    }
    
    // Get<>AsString()
    public string GetFiller32AsString()
    {
        return _Filler32.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetFiller32AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Filler32 = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}