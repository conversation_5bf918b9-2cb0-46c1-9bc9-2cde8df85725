using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D22Record Data Structure

public class D22Record
{
    private static int _size = 158;
    // [DEBUG] Class: D22Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D22PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D22PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D22ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D22ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD22RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D22PrintControl.PadRight(1));
        result.Append(_D22ReportLine.PadRight(157));
        
        return result.ToString();
    }
    
    public void SetD22RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD22PrintControl(extracted);
        }
        offset += 1;
        if (offset + 157 <= data.Length)
        {
            string extracted = data.Substring(offset, 157).Trim();
            SetD22ReportLine(extracted);
        }
        offset += 157;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD22RecordAsString();
    }
    // Set<>String Override function
    public void SetD22Record(string value)
    {
        SetD22RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD22PrintControl()
    {
        return _D22PrintControl;
    }
    
    // Standard Setter
    public void SetD22PrintControl(string value)
    {
        _D22PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD22PrintControlAsString()
    {
        return _D22PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD22PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D22PrintControl = value;
    }
    
    // Standard Getter
    public string GetD22ReportLine()
    {
        return _D22ReportLine;
    }
    
    // Standard Setter
    public void SetD22ReportLine(string value)
    {
        _D22ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD22ReportLineAsString()
    {
        return _D22ReportLine.PadRight(157);
    }
    
    // Set<>AsString()
    public void SetD22ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D22ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}