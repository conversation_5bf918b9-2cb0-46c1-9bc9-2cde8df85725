using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D1Record Data Structure

public class D1Record
{
    private static int _size = 50;
    // [DEBUG] Class: D1Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D1Key, is_external=, is_static_class=False, static_prefix=
    private D1Key _D1Key = new D1Key();
    
    
    
    
    // [DEBUG] Field: D1CountryName, is_external=, is_static_class=False, static_prefix=
    private string _D1CountryName ="";
    
    
    
    
    // [DEBUG] Field: D1UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D1UpdateCount =0;
    
    
    
    
    // [DEBUG] Field: D1InverseKey, is_external=, is_static_class=False, static_prefix=
    private D1InverseKey _D1InverseKey = new D1InverseKey();
    
    
    
    
    
    // Serialization methods
    public string GetD1RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D1Key.GetD1KeyAsString());
        result.Append(_D1CountryName.PadRight(40));
        result.Append(_D1UpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_D1InverseKey.GetD1InverseKeyAsString());
        
        return result.ToString();
    }
    
    public void SetD1RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            _D1Key.SetD1KeyAsString(data.Substring(offset, 3));
        }
        else
        {
            _D1Key.SetD1KeyAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD1CountryName(extracted);
        }
        offset += 40;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD1UpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            _D1InverseKey.SetD1InverseKeyAsString(data.Substring(offset, 3));
        }
        else
        {
            _D1InverseKey.SetD1InverseKeyAsString(data.Substring(offset));
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD1RecordAsString();
    }
    // Set<>String Override function
    public void SetD1Record(string value)
    {
        SetD1RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D1Key GetD1Key()
    {
        return _D1Key;
    }
    
    // Standard Setter
    public void SetD1Key(D1Key value)
    {
        _D1Key = value;
    }
    
    // Get<>AsString()
    public string GetD1KeyAsString()
    {
        return _D1Key != null ? _D1Key.GetD1KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD1KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D1Key == null)
        {
            _D1Key = new D1Key();
        }
        _D1Key.SetD1KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD1CountryName()
    {
        return _D1CountryName;
    }
    
    // Standard Setter
    public void SetD1CountryName(string value)
    {
        _D1CountryName = value;
    }
    
    // Get<>AsString()
    public string GetD1CountryNameAsString()
    {
        return _D1CountryName.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD1CountryNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D1CountryName = value;
    }
    
    // Standard Getter
    public int GetD1UpdateCount()
    {
        return _D1UpdateCount;
    }
    
    // Standard Setter
    public void SetD1UpdateCount(int value)
    {
        _D1UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD1UpdateCountAsString()
    {
        return _D1UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD1UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D1UpdateCount = parsed;
    }
    
    // Standard Getter
    public D1InverseKey GetD1InverseKey()
    {
        return _D1InverseKey;
    }
    
    // Standard Setter
    public void SetD1InverseKey(D1InverseKey value)
    {
        _D1InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD1InverseKeyAsString()
    {
        return _D1InverseKey != null ? _D1InverseKey.GetD1InverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD1InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D1InverseKey == null)
        {
            _D1InverseKey = new D1InverseKey();
        }
        _D1InverseKey.SetD1InverseKeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD1Key(string value)
    {
        _D1Key.SetD1KeyAsString(value);
    }
    // Nested Class: D1Key
    public class D1Key
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D1CountryCode, is_external=, is_static_class=False, static_prefix=
        private string _D1CountryCode ="";
        
        
        
        
    public D1Key() {}
    
    public D1Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD1CountryCode(data.Substring(offset, 3).Trim());
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetD1KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D1CountryCode.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetD1KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD1CountryCode(extracted);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD1CountryCode()
    {
        return _D1CountryCode;
    }
    
    // Standard Setter
    public void SetD1CountryCode(string value)
    {
        _D1CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD1CountryCodeAsString()
    {
        return _D1CountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD1CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D1CountryCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD1InverseKey(string value)
{
    _D1InverseKey.SetD1InverseKeyAsString(value);
}
// Nested Class: D1InverseKey
public class D1InverseKey
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D1InverseCountryCode, is_external=, is_static_class=False, static_prefix=
    private string _D1InverseCountryCode ="";
    
    
    
    
public D1InverseKey() {}

public D1InverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD1InverseCountryCode(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetD1InverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D1InverseCountryCode.PadRight(3));
    
    return result.ToString();
}

public void SetD1InverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetD1InverseCountryCode(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetD1InverseCountryCode()
{
    return _D1InverseCountryCode;
}

// Standard Setter
public void SetD1InverseCountryCode(string value)
{
    _D1InverseCountryCode = value;
}

// Get<>AsString()
public string GetD1InverseCountryCodeAsString()
{
    return _D1InverseCountryCode.PadRight(3);
}

// Set<>AsString()
public void SetD1InverseCountryCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D1InverseCountryCode = value;
}



public static int GetSize()
{
    return _size;
}

}

}}