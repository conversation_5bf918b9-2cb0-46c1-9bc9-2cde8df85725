using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgthboseDTO
{// DTO class representing Cgtdate2LinkageDate7 Data Structure

public class Cgtdate2LinkageDate7
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate7, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd7 =0;
    
    
    
    
    // [DEBUG] Field: Filler56, is_external=, is_static_class=False, static_prefix=
    private Filler56 _Filler56 = new Filler56();
    
    
    
    
    // [DEBUG] Field: Filler57, is_external=, is_static_class=False, static_prefix=
    private Filler57 _Filler57 = new Filler57();
    
    
    
    
    // [DEBUG] Field: Filler58, is_external=, is_static_class=False, static_prefix=
    private Filler58 _Filler58 = new Filler58();
    
    
    
    
    // [DEBUG] Field: Filler59, is_external=, is_static_class=False, static_prefix=
    private Filler59 _Filler59 = new Filler59();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0'));
        result.Append(_Filler56.GetFiller56AsString());
        result.Append(_Filler57.GetFiller57AsString());
        result.Append(_Filler58.GetFiller58AsString());
        result.Append(_Filler59.GetFiller59AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd7(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler56.SetFiller56AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler56.SetFiller56AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler57.SetFiller57AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler57.SetFiller57AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler58.SetFiller58AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler58.SetFiller58AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler59.SetFiller59AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler59.SetFiller59AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate7AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate7(string value)
    {
        SetCgtdate2LinkageDate7AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd7()
    {
        return _Cgtdate2Ccyymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd7(int value)
    {
        _Cgtdate2Ccyymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd7AsString()
    {
        return _Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd7 = parsed;
    }
    
    // Standard Getter
    public Filler56 GetFiller56()
    {
        return _Filler56;
    }
    
    // Standard Setter
    public void SetFiller56(Filler56 value)
    {
        _Filler56 = value;
    }
    
    // Get<>AsString()
    public string GetFiller56AsString()
    {
        return _Filler56 != null ? _Filler56.GetFiller56AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller56AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler56 == null)
        {
            _Filler56 = new Filler56();
        }
        _Filler56.SetFiller56AsString(value);
    }
    
    // Standard Getter
    public Filler57 GetFiller57()
    {
        return _Filler57;
    }
    
    // Standard Setter
    public void SetFiller57(Filler57 value)
    {
        _Filler57 = value;
    }
    
    // Get<>AsString()
    public string GetFiller57AsString()
    {
        return _Filler57 != null ? _Filler57.GetFiller57AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller57AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler57 == null)
        {
            _Filler57 = new Filler57();
        }
        _Filler57.SetFiller57AsString(value);
    }
    
    // Standard Getter
    public Filler58 GetFiller58()
    {
        return _Filler58;
    }
    
    // Standard Setter
    public void SetFiller58(Filler58 value)
    {
        _Filler58 = value;
    }
    
    // Get<>AsString()
    public string GetFiller58AsString()
    {
        return _Filler58 != null ? _Filler58.GetFiller58AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller58AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler58 == null)
        {
            _Filler58 = new Filler58();
        }
        _Filler58.SetFiller58AsString(value);
    }
    
    // Standard Getter
    public Filler59 GetFiller59()
    {
        return _Filler59;
    }
    
    // Standard Setter
    public void SetFiller59(Filler59 value)
    {
        _Filler59 = value;
    }
    
    // Get<>AsString()
    public string GetFiller59AsString()
    {
        return _Filler59 != null ? _Filler59.GetFiller59AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller59AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler59 == null)
        {
            _Filler59 = new Filler59();
        }
        _Filler59.SetFiller59AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller56(string value)
    {
        _Filler56.SetFiller56AsString(value);
    }
    // Nested Class: Filler56
    public class Filler56
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd7, is_external=, is_static_class=False, static_prefix=
        private Filler56.Cgtdate2Yymmdd7 _Cgtdate2Yymmdd7 = new Filler56.Cgtdate2Yymmdd7();
        
        
        
        
    public Filler56() {}
    
    public Filler56(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, Cgtdate2Yymmdd7.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller56AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc7.PadRight(2));
        result.Append(_Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetFiller56AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc7(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc7()
    {
        return _Cgtdate2Cc7;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc7(string value)
    {
        _Cgtdate2Cc7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc7AsString()
    {
        return _Cgtdate2Cc7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd7 GetCgtdate2Yymmdd7()
    {
        return _Cgtdate2Yymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd7(Cgtdate2Yymmdd7 value)
    {
        _Cgtdate2Yymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd7AsString()
    {
        return _Cgtdate2Yymmdd7 != null ? _Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd7 == null)
        {
            _Cgtdate2Yymmdd7 = new Cgtdate2Yymmdd7();
        }
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd7
    public class Cgtdate2Yymmdd7
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd7, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd7.Cgtdate2Mmdd7 _Cgtdate2Mmdd7 = new Cgtdate2Yymmdd7.Cgtdate2Mmdd7();
        
        
        
        
    public Cgtdate2Yymmdd7() {}
    
    public Cgtdate2Yymmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, Cgtdate2Mmdd7.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy7.PadRight(2));
        result.Append(_Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy7(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy7()
    {
        return _Cgtdate2Yy7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy7(string value)
    {
        _Cgtdate2Yy7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy7AsString()
    {
        return _Cgtdate2Yy7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd7 GetCgtdate2Mmdd7()
    {
        return _Cgtdate2Mmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd7(Cgtdate2Mmdd7 value)
    {
        _Cgtdate2Mmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd7AsString()
    {
        return _Cgtdate2Mmdd7 != null ? _Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd7 == null)
        {
            _Cgtdate2Mmdd7 = new Cgtdate2Mmdd7();
        }
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd7
    public class Cgtdate2Mmdd7
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd7 ="";
        
        
        
        
    public Cgtdate2Mmdd7() {}
    
    public Cgtdate2Mmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm7(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd7(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm7.PadRight(2));
        result.Append(_Cgtdate2Dd7.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm7(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd7(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm7()
    {
        return _Cgtdate2Mm7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm7(string value)
    {
        _Cgtdate2Mm7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm7AsString()
    {
        return _Cgtdate2Mm7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm7 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd7()
    {
        return _Cgtdate2Dd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd7(string value)
    {
        _Cgtdate2Dd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd7AsString()
    {
        return _Cgtdate2Dd7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd7 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller57(string value)
{
    _Filler57.SetFiller57AsString(value);
}
// Nested Class: Filler57
public class Filler57
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd7 =0;
    
    
    
    
public Filler57() {}

public Filler57(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller57AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy7.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd7.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller57AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy7(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd7(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy7()
{
    return _Cgtdate2CCcyy7;
}

// Standard Setter
public void SetCgtdate2CCcyy7(int value)
{
    _Cgtdate2CCcyy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy7AsString()
{
    return _Cgtdate2CCcyy7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd7()
{
    return _Cgtdate2CMmdd7;
}

// Standard Setter
public void SetCgtdate2CMmdd7(int value)
{
    _Cgtdate2CMmdd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd7AsString()
{
    return _Cgtdate2CMmdd7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller58(string value)
{
    _Filler58.SetFiller58AsString(value);
}
// Nested Class: Filler58
public class Filler58
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd7 =0;
    
    
    
    
public Filler58() {}

public Filler58(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller58AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd7.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller58AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd7(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc7()
{
    return _Cgtdate2CCc7;
}

// Standard Setter
public void SetCgtdate2CCc7(int value)
{
    _Cgtdate2CCc7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc7AsString()
{
    return _Cgtdate2CCc7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc7 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy7()
{
    return _Cgtdate2CYy7;
}

// Standard Setter
public void SetCgtdate2CYy7(int value)
{
    _Cgtdate2CYy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy7AsString()
{
    return _Cgtdate2CYy7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm7()
{
    return _Cgtdate2CMm7;
}

// Standard Setter
public void SetCgtdate2CMm7(int value)
{
    _Cgtdate2CMm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm7AsString()
{
    return _Cgtdate2CMm7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm7 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd7()
{
    return _Cgtdate2CDd7;
}

// Standard Setter
public void SetCgtdate2CDd7(int value)
{
    _Cgtdate2CDd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd7AsString()
{
    return _Cgtdate2CDd7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller59(string value)
{
    _Filler59.SetFiller59AsString(value);
}
// Nested Class: Filler59
public class Filler59
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm7 =0;
    
    
    
    
    // [DEBUG] Field: Filler60, is_external=, is_static_class=False, static_prefix=
    private string _Filler60 ="";
    
    
    
    
public Filler59() {}

public Filler59(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm7(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller60(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller59AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm7.ToString().PadLeft(6, '0'));
    result.Append(_Filler60.PadRight(2));
    
    return result.ToString();
}

public void SetFiller59AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm7(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller60(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm7()
{
    return _Cgtdate2CCcyymm7;
}

// Standard Setter
public void SetCgtdate2CCcyymm7(int value)
{
    _Cgtdate2CCcyymm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm7AsString()
{
    return _Cgtdate2CCcyymm7.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm7 = parsed;
}

// Standard Getter
public string GetFiller60()
{
    return _Filler60;
}

// Standard Setter
public void SetFiller60(string value)
{
    _Filler60 = value;
}

// Get<>AsString()
public string GetFiller60AsString()
{
    return _Filler60.PadRight(2);
}

// Set<>AsString()
public void SetFiller60AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler60 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}