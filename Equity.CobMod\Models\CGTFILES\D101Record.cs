using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D101Record Data Structure

public class D101Record
{
    private static int _size = 120;
    // [DEBUG] Class: D101Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D101KeyRecord, is_external=, is_static_class=False, static_prefix=
    private D101KeyRecord _D101KeyRecord = new D101KeyRecord();
    
    
    
    
    
    // Serialization methods
    public string GetD101RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D101KeyRecord.GetD101KeyRecordAsString());
        
        return result.ToString();
    }
    
    public void SetD101RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 120 <= data.Length)
        {
            _D101KeyRecord.SetD101KeyRecordAsString(data.Substring(offset, 120));
        }
        else
        {
            _D101KeyRecord.SetD101KeyRecordAsString(data.Substring(offset));
        }
        offset += 120;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD101RecordAsString();
    }
    // Set<>String Override function
    public void SetD101Record(string value)
    {
        SetD101RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D101KeyRecord GetD101KeyRecord()
    {
        return _D101KeyRecord;
    }
    
    // Standard Setter
    public void SetD101KeyRecord(D101KeyRecord value)
    {
        _D101KeyRecord = value;
    }
    
    // Get<>AsString()
    public string GetD101KeyRecordAsString()
    {
        return _D101KeyRecord != null ? _D101KeyRecord.GetD101KeyRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101KeyRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101KeyRecord == null)
        {
            _D101KeyRecord = new D101KeyRecord();
        }
        _D101KeyRecord.SetD101KeyRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD101KeyRecord(string value)
    {
        _D101KeyRecord.SetD101KeyRecordAsString(value);
    }
    // Nested Class: D101KeyRecord
    public class D101KeyRecord
    {
        private static int _size = 120;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D101SplitKey01, is_external=, is_static_class=False, static_prefix=
        private D101KeyRecord.D101SplitKey01 _D101SplitKey01 = new D101KeyRecord.D101SplitKey01();
        
        
        
        
        // [DEBUG] Field: Filler144, is_external=, is_static_class=False, static_prefix=
        private string _Filler144 ="";
        
        
        
        
        // [DEBUG] Field: D101SplitKey02, is_external=, is_static_class=False, static_prefix=
        private D101KeyRecord.D101SplitKey02 _D101SplitKey02 = new D101KeyRecord.D101SplitKey02();
        
        
        
        
        // [DEBUG] Field: D101SplitKey04, is_external=, is_static_class=False, static_prefix=
        private D101KeyRecord.D101SplitKey04 _D101SplitKey04 = new D101KeyRecord.D101SplitKey04();
        
        
        
        
        // [DEBUG] Field: D101SplitKey06, is_external=, is_static_class=False, static_prefix=
        private D101KeyRecord.D101SplitKey06 _D101SplitKey06 = new D101KeyRecord.D101SplitKey06();
        
        
        
        
        // [DEBUG] Field: Filler145, is_external=, is_static_class=False, static_prefix=
        private string _Filler145 ="";
        
        
        
        
        // [DEBUG] Field: D101SplitKey07, is_external=, is_static_class=False, static_prefix=
        private D101KeyRecord.D101SplitKey07 _D101SplitKey07 = new D101KeyRecord.D101SplitKey07();
        
        
        
        
        // [DEBUG] Field: D101SplitKey03, is_external=, is_static_class=False, static_prefix=
        private D101KeyRecord.D101SplitKey03 _D101SplitKey03 = new D101KeyRecord.D101SplitKey03();
        
        
        
        
        // [DEBUG] Field: Filler146, is_external=, is_static_class=False, static_prefix=
        private string _Filler146 ="";
        
        
        
        
        // [DEBUG] Field: D101TrancheFlag, is_external=, is_static_class=False, static_prefix=
        private string _D101TrancheFlag ="";
        
        
        
        
        // [DEBUG] Field: Filler147, is_external=, is_static_class=False, static_prefix=
        private string _Filler147 ="";
        
        
        
        
        // [DEBUG] Field: Filler148, is_external=, is_static_class=False, static_prefix=
        private string _Filler148 ="";
        
        
        
        
        // [DEBUG] Field: Filler149, is_external=, is_static_class=False, static_prefix=
        private string _Filler149 ="";
        
        
        
        
        // [DEBUG] Field: D101SplitKey05, is_external=, is_static_class=False, static_prefix=
        private D101KeyRecord.D101SplitKey05 _D101SplitKey05 = new D101KeyRecord.D101SplitKey05();
        
        
        
        
        // [DEBUG] Field: Filler150, is_external=, is_static_class=False, static_prefix=
        private string _Filler150 ="";
        
        
        
        
        // [DEBUG] Field: Filler151, is_external=, is_static_class=False, static_prefix=
        private string _Filler151 ="";
        
        
        
        
        // [DEBUG] Field: Filler152, is_external=, is_static_class=False, static_prefix=
        private string _Filler152 ="";
        
        
        
        
        // [DEBUG] Field: Filler153, is_external=, is_static_class=False, static_prefix=
        private string _Filler153 ="";
        
        
        
        
    public D101KeyRecord() {}
    
    public D101KeyRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D101SplitKey01.SetD101SplitKey01AsString(data.Substring(offset, D101SplitKey01.GetSize()));
        offset += 1;
        SetFiller144(data.Substring(offset, 0).Trim());
        offset += 0;
        _D101SplitKey02.SetD101SplitKey02AsString(data.Substring(offset, D101SplitKey02.GetSize()));
        offset += 0;
        _D101SplitKey04.SetD101SplitKey04AsString(data.Substring(offset, D101SplitKey04.GetSize()));
        offset += 0;
        _D101SplitKey06.SetD101SplitKey06AsString(data.Substring(offset, D101SplitKey06.GetSize()));
        offset += 0;
        SetFiller145(data.Substring(offset, 110).Trim());
        offset += 110;
        _D101SplitKey07.SetD101SplitKey07AsString(data.Substring(offset, D101SplitKey07.GetSize()));
        offset += 9;
        _D101SplitKey03.SetD101SplitKey03AsString(data.Substring(offset, D101SplitKey03.GetSize()));
        offset += 0;
        SetFiller146(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD101TrancheFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller147(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller148(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller149(data.Substring(offset, 0).Trim());
        offset += 0;
        _D101SplitKey05.SetD101SplitKey05AsString(data.Substring(offset, D101SplitKey05.GetSize()));
        offset += 0;
        SetFiller150(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller151(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller152(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller153(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD101KeyRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D101SplitKey01.GetD101SplitKey01AsString());
        result.Append(_Filler144.PadRight(0));
        result.Append(_D101SplitKey02.GetD101SplitKey02AsString());
        result.Append(_D101SplitKey04.GetD101SplitKey04AsString());
        result.Append(_D101SplitKey06.GetD101SplitKey06AsString());
        result.Append(_Filler145.PadRight(110));
        result.Append(_D101SplitKey07.GetD101SplitKey07AsString());
        result.Append(_D101SplitKey03.GetD101SplitKey03AsString());
        result.Append(_Filler146.PadRight(0));
        result.Append(_D101TrancheFlag.PadRight(0));
        result.Append(_Filler147.PadRight(0));
        result.Append(_Filler148.PadRight(0));
        result.Append(_Filler149.PadRight(0));
        result.Append(_D101SplitKey05.GetD101SplitKey05AsString());
        result.Append(_Filler150.PadRight(0));
        result.Append(_Filler151.PadRight(0));
        result.Append(_Filler152.PadRight(0));
        result.Append(_Filler153.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD101KeyRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            _D101SplitKey01.SetD101SplitKey01AsString(data.Substring(offset, 1));
        }
        else
        {
            _D101SplitKey01.SetD101SplitKey01AsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller144(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D101SplitKey02.SetD101SplitKey02AsString(data.Substring(offset, 0));
        }
        else
        {
            _D101SplitKey02.SetD101SplitKey02AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D101SplitKey04.SetD101SplitKey04AsString(data.Substring(offset, 0));
        }
        else
        {
            _D101SplitKey04.SetD101SplitKey04AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D101SplitKey06.SetD101SplitKey06AsString(data.Substring(offset, 0));
        }
        else
        {
            _D101SplitKey06.SetD101SplitKey06AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 110 <= data.Length)
        {
            string extracted = data.Substring(offset, 110).Trim();
            SetFiller145(extracted);
        }
        offset += 110;
        if (offset + 9 <= data.Length)
        {
            _D101SplitKey07.SetD101SplitKey07AsString(data.Substring(offset, 9));
        }
        else
        {
            _D101SplitKey07.SetD101SplitKey07AsString(data.Substring(offset));
        }
        offset += 9;
        if (offset + 0 <= data.Length)
        {
            _D101SplitKey03.SetD101SplitKey03AsString(data.Substring(offset, 0));
        }
        else
        {
            _D101SplitKey03.SetD101SplitKey03AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller146(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD101TrancheFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller147(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller148(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller149(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D101SplitKey05.SetD101SplitKey05AsString(data.Substring(offset, 0));
        }
        else
        {
            _D101SplitKey05.SetD101SplitKey05AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller150(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller151(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller152(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller153(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D101SplitKey01 GetD101SplitKey01()
    {
        return _D101SplitKey01;
    }
    
    // Standard Setter
    public void SetD101SplitKey01(D101SplitKey01 value)
    {
        _D101SplitKey01 = value;
    }
    
    // Get<>AsString()
    public string GetD101SplitKey01AsString()
    {
        return _D101SplitKey01 != null ? _D101SplitKey01.GetD101SplitKey01AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101SplitKey01AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101SplitKey01 == null)
        {
            _D101SplitKey01 = new D101SplitKey01();
        }
        _D101SplitKey01.SetD101SplitKey01AsString(value);
    }
    
    // Standard Getter
    public string GetFiller144()
    {
        return _Filler144;
    }
    
    // Standard Setter
    public void SetFiller144(string value)
    {
        _Filler144 = value;
    }
    
    // Get<>AsString()
    public string GetFiller144AsString()
    {
        return _Filler144.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller144AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler144 = value;
    }
    
    // Standard Getter
    public D101SplitKey02 GetD101SplitKey02()
    {
        return _D101SplitKey02;
    }
    
    // Standard Setter
    public void SetD101SplitKey02(D101SplitKey02 value)
    {
        _D101SplitKey02 = value;
    }
    
    // Get<>AsString()
    public string GetD101SplitKey02AsString()
    {
        return _D101SplitKey02 != null ? _D101SplitKey02.GetD101SplitKey02AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101SplitKey02AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101SplitKey02 == null)
        {
            _D101SplitKey02 = new D101SplitKey02();
        }
        _D101SplitKey02.SetD101SplitKey02AsString(value);
    }
    
    // Standard Getter
    public D101SplitKey04 GetD101SplitKey04()
    {
        return _D101SplitKey04;
    }
    
    // Standard Setter
    public void SetD101SplitKey04(D101SplitKey04 value)
    {
        _D101SplitKey04 = value;
    }
    
    // Get<>AsString()
    public string GetD101SplitKey04AsString()
    {
        return _D101SplitKey04 != null ? _D101SplitKey04.GetD101SplitKey04AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101SplitKey04AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101SplitKey04 == null)
        {
            _D101SplitKey04 = new D101SplitKey04();
        }
        _D101SplitKey04.SetD101SplitKey04AsString(value);
    }
    
    // Standard Getter
    public D101SplitKey06 GetD101SplitKey06()
    {
        return _D101SplitKey06;
    }
    
    // Standard Setter
    public void SetD101SplitKey06(D101SplitKey06 value)
    {
        _D101SplitKey06 = value;
    }
    
    // Get<>AsString()
    public string GetD101SplitKey06AsString()
    {
        return _D101SplitKey06 != null ? _D101SplitKey06.GetD101SplitKey06AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101SplitKey06AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101SplitKey06 == null)
        {
            _D101SplitKey06 = new D101SplitKey06();
        }
        _D101SplitKey06.SetD101SplitKey06AsString(value);
    }
    
    // Standard Getter
    public string GetFiller145()
    {
        return _Filler145;
    }
    
    // Standard Setter
    public void SetFiller145(string value)
    {
        _Filler145 = value;
    }
    
    // Get<>AsString()
    public string GetFiller145AsString()
    {
        return _Filler145.PadRight(110);
    }
    
    // Set<>AsString()
    public void SetFiller145AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler145 = value;
    }
    
    // Standard Getter
    public D101SplitKey07 GetD101SplitKey07()
    {
        return _D101SplitKey07;
    }
    
    // Standard Setter
    public void SetD101SplitKey07(D101SplitKey07 value)
    {
        _D101SplitKey07 = value;
    }
    
    // Get<>AsString()
    public string GetD101SplitKey07AsString()
    {
        return _D101SplitKey07 != null ? _D101SplitKey07.GetD101SplitKey07AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101SplitKey07AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101SplitKey07 == null)
        {
            _D101SplitKey07 = new D101SplitKey07();
        }
        _D101SplitKey07.SetD101SplitKey07AsString(value);
    }
    
    // Standard Getter
    public D101SplitKey03 GetD101SplitKey03()
    {
        return _D101SplitKey03;
    }
    
    // Standard Setter
    public void SetD101SplitKey03(D101SplitKey03 value)
    {
        _D101SplitKey03 = value;
    }
    
    // Get<>AsString()
    public string GetD101SplitKey03AsString()
    {
        return _D101SplitKey03 != null ? _D101SplitKey03.GetD101SplitKey03AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101SplitKey03AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101SplitKey03 == null)
        {
            _D101SplitKey03 = new D101SplitKey03();
        }
        _D101SplitKey03.SetD101SplitKey03AsString(value);
    }
    
    // Standard Getter
    public string GetFiller146()
    {
        return _Filler146;
    }
    
    // Standard Setter
    public void SetFiller146(string value)
    {
        _Filler146 = value;
    }
    
    // Get<>AsString()
    public string GetFiller146AsString()
    {
        return _Filler146.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller146AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler146 = value;
    }
    
    // Standard Getter
    public string GetD101TrancheFlag()
    {
        return _D101TrancheFlag;
    }
    
    // Standard Setter
    public void SetD101TrancheFlag(string value)
    {
        _D101TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD101TrancheFlagAsString()
    {
        return _D101TrancheFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD101TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D101TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetFiller147()
    {
        return _Filler147;
    }
    
    // Standard Setter
    public void SetFiller147(string value)
    {
        _Filler147 = value;
    }
    
    // Get<>AsString()
    public string GetFiller147AsString()
    {
        return _Filler147.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller147AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler147 = value;
    }
    
    // Standard Getter
    public string GetFiller148()
    {
        return _Filler148;
    }
    
    // Standard Setter
    public void SetFiller148(string value)
    {
        _Filler148 = value;
    }
    
    // Get<>AsString()
    public string GetFiller148AsString()
    {
        return _Filler148.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller148AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler148 = value;
    }
    
    // Standard Getter
    public string GetFiller149()
    {
        return _Filler149;
    }
    
    // Standard Setter
    public void SetFiller149(string value)
    {
        _Filler149 = value;
    }
    
    // Get<>AsString()
    public string GetFiller149AsString()
    {
        return _Filler149.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller149AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler149 = value;
    }
    
    // Standard Getter
    public D101SplitKey05 GetD101SplitKey05()
    {
        return _D101SplitKey05;
    }
    
    // Standard Setter
    public void SetD101SplitKey05(D101SplitKey05 value)
    {
        _D101SplitKey05 = value;
    }
    
    // Get<>AsString()
    public string GetD101SplitKey05AsString()
    {
        return _D101SplitKey05 != null ? _D101SplitKey05.GetD101SplitKey05AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD101SplitKey05AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D101SplitKey05 == null)
        {
            _D101SplitKey05 = new D101SplitKey05();
        }
        _D101SplitKey05.SetD101SplitKey05AsString(value);
    }
    
    // Standard Getter
    public string GetFiller150()
    {
        return _Filler150;
    }
    
    // Standard Setter
    public void SetFiller150(string value)
    {
        _Filler150 = value;
    }
    
    // Get<>AsString()
    public string GetFiller150AsString()
    {
        return _Filler150.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller150AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler150 = value;
    }
    
    // Standard Getter
    public string GetFiller151()
    {
        return _Filler151;
    }
    
    // Standard Setter
    public void SetFiller151(string value)
    {
        _Filler151 = value;
    }
    
    // Get<>AsString()
    public string GetFiller151AsString()
    {
        return _Filler151.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller151AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler151 = value;
    }
    
    // Standard Getter
    public string GetFiller152()
    {
        return _Filler152;
    }
    
    // Standard Setter
    public void SetFiller152(string value)
    {
        _Filler152 = value;
    }
    
    // Get<>AsString()
    public string GetFiller152AsString()
    {
        return _Filler152.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller152AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler152 = value;
    }
    
    // Standard Getter
    public string GetFiller153()
    {
        return _Filler153;
    }
    
    // Standard Setter
    public void SetFiller153(string value)
    {
        _Filler153 = value;
    }
    
    // Get<>AsString()
    public string GetFiller153AsString()
    {
        return _Filler153.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller153AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler153 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D101SplitKey01
    public class D101SplitKey01
    {
        private static int _size = 1;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D101CurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _D101CurrencySort =0;
        
        
        // 88-level condition checks for D101CurrencySort
        public bool IsD101CurrencySterling()
        {
            if (this._D101CurrencySort == 0) return true;
            return false;
        }
        public bool IsD101CurrencyEuro()
        {
            if (this._D101CurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: D101CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D101CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D101CountryCode, is_external=, is_static_class=False, static_prefix=
        private string _D101CountryCode ="";
        
        
        
        
    public D101SplitKey01() {}
    
    public D101SplitKey01(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD101CurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetD101CoAcLk(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD101CountryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD101SplitKey01AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D101CurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_D101CoAcLk.PadRight(0));
        result.Append(_D101CountryCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD101SplitKey01AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD101CurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD101CoAcLk(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD101CountryCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD101CurrencySort()
    {
        return _D101CurrencySort;
    }
    
    // Standard Setter
    public void SetD101CurrencySort(int value)
    {
        _D101CurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetD101CurrencySortAsString()
    {
        return _D101CurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD101CurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D101CurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetD101CoAcLk()
    {
        return _D101CoAcLk;
    }
    
    // Standard Setter
    public void SetD101CoAcLk(string value)
    {
        _D101CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD101CoAcLkAsString()
    {
        return _D101CoAcLk.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD101CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D101CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD101CountryCode()
    {
        return _D101CountryCode;
    }
    
    // Standard Setter
    public void SetD101CountryCode(string value)
    {
        _D101CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD101CountryCodeAsString()
    {
        return _D101CountryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD101CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D101CountryCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D101SplitKey02
public class D101SplitKey02
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101MainGroup23, is_external=, is_static_class=False, static_prefix=
    private string _D101MainGroup23 ="";
    
    
    
    
    // [DEBUG] Field: D101SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D101SecurityType ="";
    
    
    
    
    // [DEBUG] Field: D101SecuritySortCode, is_external=, is_static_class=False, static_prefix=
    private string _D101SecuritySortCode ="";
    
    
    
    
    // [DEBUG] Field: D101SedolSort, is_external=, is_static_class=False, static_prefix=
    private string _D101SedolSort ="";
    
    
    
    
    // [DEBUG] Field: D101RecordType, is_external=, is_static_class=False, static_prefix=
    private string _D101RecordType ="";
    
    
    
    
public D101SplitKey02() {}

public D101SplitKey02(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD101MainGroup23(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101SecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101SecuritySortCode(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101SedolSort(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101RecordType(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD101SplitKey02AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101MainGroup23.PadRight(0));
    result.Append(_D101SecurityType.PadRight(0));
    result.Append(_D101SecuritySortCode.PadRight(0));
    result.Append(_D101SedolSort.PadRight(0));
    result.Append(_D101RecordType.PadRight(0));
    
    return result.ToString();
}

public void SetD101SplitKey02AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101MainGroup23(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101SecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101SecuritySortCode(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101SedolSort(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101RecordType(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD101MainGroup23()
{
    return _D101MainGroup23;
}

// Standard Setter
public void SetD101MainGroup23(string value)
{
    _D101MainGroup23 = value;
}

// Get<>AsString()
public string GetD101MainGroup23AsString()
{
    return _D101MainGroup23.PadRight(0);
}

// Set<>AsString()
public void SetD101MainGroup23AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101MainGroup23 = value;
}

// Standard Getter
public string GetD101SecurityType()
{
    return _D101SecurityType;
}

// Standard Setter
public void SetD101SecurityType(string value)
{
    _D101SecurityType = value;
}

// Get<>AsString()
public string GetD101SecurityTypeAsString()
{
    return _D101SecurityType.PadRight(0);
}

// Set<>AsString()
public void SetD101SecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101SecurityType = value;
}

// Standard Getter
public string GetD101SecuritySortCode()
{
    return _D101SecuritySortCode;
}

// Standard Setter
public void SetD101SecuritySortCode(string value)
{
    _D101SecuritySortCode = value;
}

// Get<>AsString()
public string GetD101SecuritySortCodeAsString()
{
    return _D101SecuritySortCode.PadRight(0);
}

// Set<>AsString()
public void SetD101SecuritySortCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101SecuritySortCode = value;
}

// Standard Getter
public string GetD101SedolSort()
{
    return _D101SedolSort;
}

// Standard Setter
public void SetD101SedolSort(string value)
{
    _D101SedolSort = value;
}

// Get<>AsString()
public string GetD101SedolSortAsString()
{
    return _D101SedolSort.PadRight(0);
}

// Set<>AsString()
public void SetD101SedolSortAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101SedolSort = value;
}

// Standard Getter
public string GetD101RecordType()
{
    return _D101RecordType;
}

// Standard Setter
public void SetD101RecordType(string value)
{
    _D101RecordType = value;
}

// Get<>AsString()
public string GetD101RecordTypeAsString()
{
    return _D101RecordType.PadRight(0);
}

// Set<>AsString()
public void SetD101RecordTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101RecordType = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D101SplitKey04
public class D101SplitKey04
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101AcquisitionDate, is_external=, is_static_class=False, static_prefix=
    private D101SplitKey04.D101AcquisitionDate _D101AcquisitionDate = new D101SplitKey04.D101AcquisitionDate();
    
    
    
    
public D101SplitKey04() {}

public D101SplitKey04(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D101AcquisitionDate.SetD101AcquisitionDateAsString(data.Substring(offset, D101AcquisitionDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD101SplitKey04AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101AcquisitionDate.GetD101AcquisitionDateAsString());
    
    return result.ToString();
}

public void SetD101SplitKey04AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        _D101AcquisitionDate.SetD101AcquisitionDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D101AcquisitionDate.SetD101AcquisitionDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public D101AcquisitionDate GetD101AcquisitionDate()
{
    return _D101AcquisitionDate;
}

// Standard Setter
public void SetD101AcquisitionDate(D101AcquisitionDate value)
{
    _D101AcquisitionDate = value;
}

// Get<>AsString()
public string GetD101AcquisitionDateAsString()
{
    return _D101AcquisitionDate != null ? _D101AcquisitionDate.GetD101AcquisitionDateAsString() : "";
}

// Set<>AsString()
public void SetD101AcquisitionDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D101AcquisitionDate == null)
    {
        _D101AcquisitionDate = new D101AcquisitionDate();
    }
    _D101AcquisitionDate.SetD101AcquisitionDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D101AcquisitionDate
public class D101AcquisitionDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101AcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D101AcquisitionDateYy ="";
    
    
    
    
    // [DEBUG] Field: D101AcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D101AcquisitionDateMm ="";
    
    
    
    
    // [DEBUG] Field: D101AcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D101AcquisitionDateDd ="";
    
    
    
    
public D101AcquisitionDate() {}

public D101AcquisitionDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD101AcquisitionDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101AcquisitionDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101AcquisitionDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD101AcquisitionDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101AcquisitionDateYy.PadRight(0));
    result.Append(_D101AcquisitionDateMm.PadRight(0));
    result.Append(_D101AcquisitionDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD101AcquisitionDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101AcquisitionDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101AcquisitionDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101AcquisitionDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD101AcquisitionDateYy()
{
    return _D101AcquisitionDateYy;
}

// Standard Setter
public void SetD101AcquisitionDateYy(string value)
{
    _D101AcquisitionDateYy = value;
}

// Get<>AsString()
public string GetD101AcquisitionDateYyAsString()
{
    return _D101AcquisitionDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD101AcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101AcquisitionDateYy = value;
}

// Standard Getter
public string GetD101AcquisitionDateMm()
{
    return _D101AcquisitionDateMm;
}

// Standard Setter
public void SetD101AcquisitionDateMm(string value)
{
    _D101AcquisitionDateMm = value;
}

// Get<>AsString()
public string GetD101AcquisitionDateMmAsString()
{
    return _D101AcquisitionDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD101AcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101AcquisitionDateMm = value;
}

// Standard Getter
public string GetD101AcquisitionDateDd()
{
    return _D101AcquisitionDateDd;
}

// Standard Setter
public void SetD101AcquisitionDateDd(string value)
{
    _D101AcquisitionDateDd = value;
}

// Get<>AsString()
public string GetD101AcquisitionDateDdAsString()
{
    return _D101AcquisitionDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD101AcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101AcquisitionDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D101SplitKey06
public class D101SplitKey06
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101TrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _D101TrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: D101LineNumber, is_external=, is_static_class=False, static_prefix=
    private string _D101LineNumber ="";
    
    
    
    
    // [DEBUG] Field: D101BdvIndicator, is_external=, is_static_class=False, static_prefix=
    private string _D101BdvIndicator ="";
    
    
    
    
public D101SplitKey06() {}

public D101SplitKey06(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD101TrancheContractNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101LineNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101BdvIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD101SplitKey06AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101TrancheContractNumber.PadRight(0));
    result.Append(_D101LineNumber.PadRight(0));
    result.Append(_D101BdvIndicator.PadRight(0));
    
    return result.ToString();
}

public void SetD101SplitKey06AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101TrancheContractNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101LineNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101BdvIndicator(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD101TrancheContractNumber()
{
    return _D101TrancheContractNumber;
}

// Standard Setter
public void SetD101TrancheContractNumber(string value)
{
    _D101TrancheContractNumber = value;
}

// Get<>AsString()
public string GetD101TrancheContractNumberAsString()
{
    return _D101TrancheContractNumber.PadRight(0);
}

// Set<>AsString()
public void SetD101TrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101TrancheContractNumber = value;
}

// Standard Getter
public string GetD101LineNumber()
{
    return _D101LineNumber;
}

// Standard Setter
public void SetD101LineNumber(string value)
{
    _D101LineNumber = value;
}

// Get<>AsString()
public string GetD101LineNumberAsString()
{
    return _D101LineNumber.PadRight(0);
}

// Set<>AsString()
public void SetD101LineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101LineNumber = value;
}

// Standard Getter
public string GetD101BdvIndicator()
{
    return _D101BdvIndicator;
}

// Standard Setter
public void SetD101BdvIndicator(string value)
{
    _D101BdvIndicator = value;
}

// Get<>AsString()
public string GetD101BdvIndicatorAsString()
{
    return _D101BdvIndicator.PadRight(0);
}

// Set<>AsString()
public void SetD101BdvIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101BdvIndicator = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D101SplitKey07
public class D101SplitKey07
{
    private static int _size = 9;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101SequenceNumber, is_external=, is_static_class=False, static_prefix=
    private int _D101SequenceNumber =0;
    
    
    
    
public D101SplitKey07() {}

public D101SplitKey07(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD101SequenceNumber(int.Parse(data.Substring(offset, 9).Trim()));
    offset += 9;
    
}

// Serialization methods
public string GetD101SplitKey07AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101SequenceNumber.ToString().PadLeft(9, '0'));
    
    return result.ToString();
}

public void SetD101SplitKey07AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD101SequenceNumber(parsedInt);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public int GetD101SequenceNumber()
{
    return _D101SequenceNumber;
}

// Standard Setter
public void SetD101SequenceNumber(int value)
{
    _D101SequenceNumber = value;
}

// Get<>AsString()
public string GetD101SequenceNumberAsString()
{
    return _D101SequenceNumber.ToString().PadLeft(9, '0');
}

// Set<>AsString()
public void SetD101SequenceNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D101SequenceNumber = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D101SplitKey03
public class D101SplitKey03
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101AcquisitionDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D101AcquisitionDateCc ="";
    
    
    
    
public D101SplitKey03() {}

public D101SplitKey03(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD101AcquisitionDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD101SplitKey03AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101AcquisitionDateCc.PadRight(0));
    
    return result.ToString();
}

public void SetD101SplitKey03AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101AcquisitionDateCc(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD101AcquisitionDateCc()
{
    return _D101AcquisitionDateCc;
}

// Standard Setter
public void SetD101AcquisitionDateCc(string value)
{
    _D101AcquisitionDateCc = value;
}

// Get<>AsString()
public string GetD101AcquisitionDateCcAsString()
{
    return _D101AcquisitionDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD101AcquisitionDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101AcquisitionDateCc = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D101SplitKey05
public class D101SplitKey05
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101TaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D101TaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: D101TaperDate, is_external=, is_static_class=False, static_prefix=
    private D101SplitKey05.D101TaperDate _D101TaperDate = new D101SplitKey05.D101TaperDate();
    
    
    
    
public D101SplitKey05() {}

public D101SplitKey05(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD101TaperDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    _D101TaperDate.SetD101TaperDateAsString(data.Substring(offset, D101TaperDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD101SplitKey05AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101TaperDateCc.PadRight(0));
    result.Append(_D101TaperDate.GetD101TaperDateAsString());
    
    return result.ToString();
}

public void SetD101SplitKey05AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101TaperDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _D101TaperDate.SetD101TaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D101TaperDate.SetD101TaperDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD101TaperDateCc()
{
    return _D101TaperDateCc;
}

// Standard Setter
public void SetD101TaperDateCc(string value)
{
    _D101TaperDateCc = value;
}

// Get<>AsString()
public string GetD101TaperDateCcAsString()
{
    return _D101TaperDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD101TaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101TaperDateCc = value;
}

// Standard Getter
public D101TaperDate GetD101TaperDate()
{
    return _D101TaperDate;
}

// Standard Setter
public void SetD101TaperDate(D101TaperDate value)
{
    _D101TaperDate = value;
}

// Get<>AsString()
public string GetD101TaperDateAsString()
{
    return _D101TaperDate != null ? _D101TaperDate.GetD101TaperDateAsString() : "";
}

// Set<>AsString()
public void SetD101TaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D101TaperDate == null)
    {
        _D101TaperDate = new D101TaperDate();
    }
    _D101TaperDate.SetD101TaperDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D101TaperDate
public class D101TaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D101TaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D101TaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: D101TaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D101TaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: D101TaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D101TaperDateDd ="";
    
    
    
    
public D101TaperDate() {}

public D101TaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD101TaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101TaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD101TaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD101TaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D101TaperDateYy.PadRight(0));
    result.Append(_D101TaperDateMm.PadRight(0));
    result.Append(_D101TaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD101TaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101TaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101TaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD101TaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD101TaperDateYy()
{
    return _D101TaperDateYy;
}

// Standard Setter
public void SetD101TaperDateYy(string value)
{
    _D101TaperDateYy = value;
}

// Get<>AsString()
public string GetD101TaperDateYyAsString()
{
    return _D101TaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD101TaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101TaperDateYy = value;
}

// Standard Getter
public string GetD101TaperDateMm()
{
    return _D101TaperDateMm;
}

// Standard Setter
public void SetD101TaperDateMm(string value)
{
    _D101TaperDateMm = value;
}

// Get<>AsString()
public string GetD101TaperDateMmAsString()
{
    return _D101TaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD101TaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101TaperDateMm = value;
}

// Standard Getter
public string GetD101TaperDateDd()
{
    return _D101TaperDateDd;
}

// Standard Setter
public void SetD101TaperDateDd(string value)
{
    _D101TaperDateDd = value;
}

// Get<>AsString()
public string GetD101TaperDateDdAsString()
{
    return _D101TaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD101TaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101TaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}