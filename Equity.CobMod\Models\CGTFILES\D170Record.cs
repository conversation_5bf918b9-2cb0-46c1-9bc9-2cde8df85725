using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D170Record Data Structure

public class D170Record
{
    private static int _size = 272;
    // [DEBUG] Class: D170Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D170Key, is_external=, is_static_class=False, static_prefix=
    private D170Key _D170Key = new D170Key();
    
    
    
    
    // [DEBUG] Field: D170SystemArea, is_external=, is_static_class=False, static_prefix=
    private int _D170SystemArea =0;
    
    
    
    
    // [DEBUG] Field: D170Filename, is_external=, is_static_class=False, static_prefix=
    private string _D170Filename ="";
    
    
    
    
    
    // Serialization methods
    public string GetD170RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D170Key.GetD170KeyAsString());
        result.Append(_D170SystemArea.ToString().PadLeft(4, '0'));
        result.Append(_D170Filename.PadRight(255));
        
        return result.ToString();
    }
    
    public void SetD170RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 13 <= data.Length)
        {
            _D170Key.SetD170KeyAsString(data.Substring(offset, 13));
        }
        else
        {
            _D170Key.SetD170KeyAsString(data.Substring(offset));
        }
        offset += 13;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD170SystemArea(parsedInt);
        }
        offset += 4;
        if (offset + 255 <= data.Length)
        {
            string extracted = data.Substring(offset, 255).Trim();
            SetD170Filename(extracted);
        }
        offset += 255;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD170RecordAsString();
    }
    // Set<>String Override function
    public void SetD170Record(string value)
    {
        SetD170RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D170Key GetD170Key()
    {
        return _D170Key;
    }
    
    // Standard Setter
    public void SetD170Key(D170Key value)
    {
        _D170Key = value;
    }
    
    // Get<>AsString()
    public string GetD170KeyAsString()
    {
        return _D170Key != null ? _D170Key.GetD170KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD170KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D170Key == null)
        {
            _D170Key = new D170Key();
        }
        _D170Key.SetD170KeyAsString(value);
    }
    
    // Standard Getter
    public int GetD170SystemArea()
    {
        return _D170SystemArea;
    }
    
    // Standard Setter
    public void SetD170SystemArea(int value)
    {
        _D170SystemArea = value;
    }
    
    // Get<>AsString()
    public string GetD170SystemAreaAsString()
    {
        return _D170SystemArea.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD170SystemAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D170SystemArea = parsed;
    }
    
    // Standard Getter
    public string GetD170Filename()
    {
        return _D170Filename;
    }
    
    // Standard Setter
    public void SetD170Filename(string value)
    {
        _D170Filename = value;
    }
    
    // Get<>AsString()
    public string GetD170FilenameAsString()
    {
        return _D170Filename.PadRight(255);
    }
    
    // Set<>AsString()
    public void SetD170FilenameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D170Filename = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD170Key(string value)
    {
        _D170Key.SetD170KeyAsString(value);
    }
    // Nested Class: D170Key
    public class D170Key
    {
        private static int _size = 13;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D170PendingId, is_external=, is_static_class=False, static_prefix=
        private int _D170PendingId =0;
        
        
        
        
        // [DEBUG] Field: D170ItemId, is_external=, is_static_class=False, static_prefix=
        private int _D170ItemId =0;
        
        
        
        
    public D170Key() {}
    
    public D170Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD170PendingId(int.Parse(data.Substring(offset, 10).Trim()));
        offset += 10;
        SetD170ItemId(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetD170KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D170PendingId.ToString().PadLeft(10, '0'));
        result.Append(_D170ItemId.ToString().PadLeft(3, '0'));
        
        return result.ToString();
    }
    
    public void SetD170KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD170PendingId(parsedInt);
        }
        offset += 10;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD170ItemId(parsedInt);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD170PendingId()
    {
        return _D170PendingId;
    }
    
    // Standard Setter
    public void SetD170PendingId(int value)
    {
        _D170PendingId = value;
    }
    
    // Get<>AsString()
    public string GetD170PendingIdAsString()
    {
        return _D170PendingId.ToString().PadLeft(10, '0');
    }
    
    // Set<>AsString()
    public void SetD170PendingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D170PendingId = parsed;
    }
    
    // Standard Getter
    public int GetD170ItemId()
    {
        return _D170ItemId;
    }
    
    // Standard Setter
    public void SetD170ItemId(int value)
    {
        _D170ItemId = value;
    }
    
    // Get<>AsString()
    public string GetD170ItemIdAsString()
    {
        return _D170ItemId.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetD170ItemIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D170ItemId = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}