using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D21File Data Structure

public class D21File
{
    private static int _size = 12;
    // [DEBUG] Class: D21File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler178, is_external=, is_static_class=False, static_prefix=
    private string _Filler178 ="$";
    
    
    
    
    // [DEBUG] Field: D21UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D21UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler179, is_external=, is_static_class=False, static_prefix=
    private string _Filler179 ="R5";
    
    
    
    
    // [DEBUG] Field: D21ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D21ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler180, is_external=, is_static_class=False, static_prefix=
    private string _Filler180 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD21FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler178.PadRight(1));
        result.Append(_D21UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler179.PadRight(2));
        result.Append(_D21ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler180.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD21FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller178(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD21UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller179(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD21ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller180(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD21FileAsString();
    }
    // Set<>String Override function
    public void SetD21File(string value)
    {
        SetD21FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller178()
    {
        return _Filler178;
    }
    
    // Standard Setter
    public void SetFiller178(string value)
    {
        _Filler178 = value;
    }
    
    // Get<>AsString()
    public string GetFiller178AsString()
    {
        return _Filler178.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller178AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler178 = value;
    }
    
    // Standard Getter
    public int GetD21UserNo()
    {
        return _D21UserNo;
    }
    
    // Standard Setter
    public void SetD21UserNo(int value)
    {
        _D21UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD21UserNoAsString()
    {
        return _D21UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD21UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D21UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller179()
    {
        return _Filler179;
    }
    
    // Standard Setter
    public void SetFiller179(string value)
    {
        _Filler179 = value;
    }
    
    // Get<>AsString()
    public string GetFiller179AsString()
    {
        return _Filler179.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller179AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler179 = value;
    }
    
    // Standard Getter
    public int GetD21ReportNo()
    {
        return _D21ReportNo;
    }
    
    // Standard Setter
    public void SetD21ReportNo(int value)
    {
        _D21ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD21ReportNoAsString()
    {
        return _D21ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD21ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D21ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller180()
    {
        return _Filler180;
    }
    
    // Standard Setter
    public void SetFiller180(string value)
    {
        _Filler180 = value;
    }
    
    // Get<>AsString()
    public string GetFiller180AsString()
    {
        return _Filler180.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller180AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler180 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}