using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D21Record Data Structure

public class D21Record
{
    private static int _size = 158;
    // [DEBUG] Class: D21Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D21PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D21PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D21ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D21ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD21RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D21PrintControl.PadRight(1));
        result.Append(_D21ReportLine.PadRight(157));
        
        return result.ToString();
    }
    
    public void SetD21RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD21PrintControl(extracted);
        }
        offset += 1;
        if (offset + 157 <= data.Length)
        {
            string extracted = data.Substring(offset, 157).Trim();
            SetD21ReportLine(extracted);
        }
        offset += 157;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD21RecordAsString();
    }
    // Set<>String Override function
    public void SetD21Record(string value)
    {
        SetD21RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD21PrintControl()
    {
        return _D21PrintControl;
    }
    
    // Standard Setter
    public void SetD21PrintControl(string value)
    {
        _D21PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD21PrintControlAsString()
    {
        return _D21PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD21PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D21PrintControl = value;
    }
    
    // Standard Getter
    public string GetD21ReportLine()
    {
        return _D21ReportLine;
    }
    
    // Standard Setter
    public void SetD21ReportLine(string value)
    {
        _D21ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD21ReportLineAsString()
    {
        return _D21ReportLine.PadRight(157);
    }
    
    // Set<>AsString()
    public void SetD21ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D21ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}