using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D29File Data Structure

public class D29File
{
    private static int _size = 12;
    // [DEBUG] Class: D29File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler202, is_external=, is_static_class=False, static_prefix=
    private string _Filler202 ="$";
    
    
    
    
    // [DEBUG] Field: D29UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D29UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler203, is_external=, is_static_class=False, static_prefix=
    private string _Filler203 ="D4";
    
    
    
    
    // [DEBUG] Field: D29ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D29ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler204, is_external=, is_static_class=False, static_prefix=
    private string _Filler204 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD29FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler202.PadRight(1));
        result.Append(_D29UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler203.PadRight(2));
        result.Append(_D29ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler204.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD29FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller202(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD29UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller203(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD29ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller204(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD29FileAsString();
    }
    // Set<>String Override function
    public void SetD29File(string value)
    {
        SetD29FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller202()
    {
        return _Filler202;
    }
    
    // Standard Setter
    public void SetFiller202(string value)
    {
        _Filler202 = value;
    }
    
    // Get<>AsString()
    public string GetFiller202AsString()
    {
        return _Filler202.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller202AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler202 = value;
    }
    
    // Standard Getter
    public int GetD29UserNo()
    {
        return _D29UserNo;
    }
    
    // Standard Setter
    public void SetD29UserNo(int value)
    {
        _D29UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD29UserNoAsString()
    {
        return _D29UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD29UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D29UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller203()
    {
        return _Filler203;
    }
    
    // Standard Setter
    public void SetFiller203(string value)
    {
        _Filler203 = value;
    }
    
    // Get<>AsString()
    public string GetFiller203AsString()
    {
        return _Filler203.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller203AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler203 = value;
    }
    
    // Standard Getter
    public int GetD29ReportNo()
    {
        return _D29ReportNo;
    }
    
    // Standard Setter
    public void SetD29ReportNo(int value)
    {
        _D29ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD29ReportNoAsString()
    {
        return _D29ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD29ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D29ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller204()
    {
        return _Filler204;
    }
    
    // Standard Setter
    public void SetFiller204(string value)
    {
        _Filler204 = value;
    }
    
    // Get<>AsString()
    public string GetFiller204AsString()
    {
        return _Filler204.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller204AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler204 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}