using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D112Record Data Structure

public class D112Record
{
    private static int _size = 12;
    // [DEBUG] Class: D112Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D112Key, is_external=, is_static_class=False, static_prefix=
    private D112Key _D112Key = new D112Key();
    
    
    
    
    // [DEBUG] Field: D112BusAssetRate, is_external=, is_static_class=False, static_prefix=
    private decimal _D112BusAssetRate =0;
    
    
    
    
    // [DEBUG] Field: D112NonBusAssetRate, is_external=, is_static_class=False, static_prefix=
    private decimal _D112NonBusAssetRate =0;
    
    
    
    
    // [DEBUG] Field: D112InverseDate, is_external=, is_static_class=False, static_prefix=
    private string _D112InverseDate ="";
    
    
    
    
    
    // Serialization methods
    public string GetD112RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D112Key.GetD112KeyAsString());
        result.Append(_D112BusAssetRate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D112NonBusAssetRate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D112InverseDate.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD112RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            _D112Key.SetD112KeyAsString(data.Substring(offset, 2));
        }
        else
        {
            _D112Key.SetD112KeyAsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD112BusAssetRate(parsedDec);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD112NonBusAssetRate(parsedDec);
        }
        offset += 5;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD112InverseDate(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD112RecordAsString();
    }
    // Set<>String Override function
    public void SetD112Record(string value)
    {
        SetD112RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D112Key GetD112Key()
    {
        return _D112Key;
    }
    
    // Standard Setter
    public void SetD112Key(D112Key value)
    {
        _D112Key = value;
    }
    
    // Get<>AsString()
    public string GetD112KeyAsString()
    {
        return _D112Key != null ? _D112Key.GetD112KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD112KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D112Key == null)
        {
            _D112Key = new D112Key();
        }
        _D112Key.SetD112KeyAsString(value);
    }
    
    // Standard Getter
    public decimal GetD112BusAssetRate()
    {
        return _D112BusAssetRate;
    }
    
    // Standard Setter
    public void SetD112BusAssetRate(decimal value)
    {
        _D112BusAssetRate = value;
    }
    
    // Get<>AsString()
    public string GetD112BusAssetRateAsString()
    {
        return _D112BusAssetRate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD112BusAssetRateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D112BusAssetRate = parsed;
    }
    
    // Standard Getter
    public decimal GetD112NonBusAssetRate()
    {
        return _D112NonBusAssetRate;
    }
    
    // Standard Setter
    public void SetD112NonBusAssetRate(decimal value)
    {
        _D112NonBusAssetRate = value;
    }
    
    // Get<>AsString()
    public string GetD112NonBusAssetRateAsString()
    {
        return _D112NonBusAssetRate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD112NonBusAssetRateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D112NonBusAssetRate = parsed;
    }
    
    // Standard Getter
    public string GetD112InverseDate()
    {
        return _D112InverseDate;
    }
    
    // Standard Setter
    public void SetD112InverseDate(string value)
    {
        _D112InverseDate = value;
    }
    
    // Get<>AsString()
    public string GetD112InverseDateAsString()
    {
        return _D112InverseDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD112InverseDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D112InverseDate = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD112Key(string value)
    {
        _D112Key.SetD112KeyAsString(value);
    }
    // Nested Class: D112Key
    public class D112Key
    {
        private static int _size = 2;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D112DateEffective, is_external=, is_static_class=False, static_prefix=
        private string _D112DateEffective ="";
        
        
        
        
        // [DEBUG] Field: D112YearsAssetHeld, is_external=, is_static_class=False, static_prefix=
        private int _D112YearsAssetHeld =0;
        
        
        
        
    public D112Key() {}
    
    public D112Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD112DateEffective(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD112YearsAssetHeld(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD112KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D112DateEffective.PadRight(0));
        result.Append(_D112YearsAssetHeld.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetD112KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD112DateEffective(extracted);
        }
        offset += 0;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD112YearsAssetHeld(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD112DateEffective()
    {
        return _D112DateEffective;
    }
    
    // Standard Setter
    public void SetD112DateEffective(string value)
    {
        _D112DateEffective = value;
    }
    
    // Get<>AsString()
    public string GetD112DateEffectiveAsString()
    {
        return _D112DateEffective.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD112DateEffectiveAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D112DateEffective = value;
    }
    
    // Standard Getter
    public int GetD112YearsAssetHeld()
    {
        return _D112YearsAssetHeld;
    }
    
    // Standard Setter
    public void SetD112YearsAssetHeld(int value)
    {
        _D112YearsAssetHeld = value;
    }
    
    // Get<>AsString()
    public string GetD112YearsAssetHeldAsString()
    {
        return _D112YearsAssetHeld.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD112YearsAssetHeldAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D112YearsAssetHeld = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}