using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D30File Data Structure

public class D30File
{
    private static int _size = 12;
    // [DEBUG] Class: D30File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler205, is_external=, is_static_class=False, static_prefix=
    private string _Filler205 ="$";
    
    
    
    
    // [DEBUG] Field: D30UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D30UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler206, is_external=, is_static_class=False, static_prefix=
    private string _Filler206 ="RA";
    
    
    
    
    // [DEBUG] Field: D30ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D30ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler207, is_external=, is_static_class=False, static_prefix=
    private string _Filler207 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD30FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler205.PadRight(1));
        result.Append(_D30UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler206.PadRight(2));
        result.Append(_D30ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler207.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD30FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller205(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD30UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller206(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD30ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller207(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD30FileAsString();
    }
    // Set<>String Override function
    public void SetD30File(string value)
    {
        SetD30FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller205()
    {
        return _Filler205;
    }
    
    // Standard Setter
    public void SetFiller205(string value)
    {
        _Filler205 = value;
    }
    
    // Get<>AsString()
    public string GetFiller205AsString()
    {
        return _Filler205.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller205AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler205 = value;
    }
    
    // Standard Getter
    public int GetD30UserNo()
    {
        return _D30UserNo;
    }
    
    // Standard Setter
    public void SetD30UserNo(int value)
    {
        _D30UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD30UserNoAsString()
    {
        return _D30UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD30UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D30UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller206()
    {
        return _Filler206;
    }
    
    // Standard Setter
    public void SetFiller206(string value)
    {
        _Filler206 = value;
    }
    
    // Get<>AsString()
    public string GetFiller206AsString()
    {
        return _Filler206.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller206AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler206 = value;
    }
    
    // Standard Getter
    public int GetD30ReportNo()
    {
        return _D30ReportNo;
    }
    
    // Standard Setter
    public void SetD30ReportNo(int value)
    {
        _D30ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD30ReportNoAsString()
    {
        return _D30ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD30ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D30ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller207()
    {
        return _Filler207;
    }
    
    // Standard Setter
    public void SetFiller207(string value)
    {
        _Filler207 = value;
    }
    
    // Get<>AsString()
    public string GetFiller207AsString()
    {
        return _Filler207.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller207AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler207 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}