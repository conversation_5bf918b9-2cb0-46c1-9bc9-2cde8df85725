using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D11File Data Structure

public class D11File
{
    private static int _size = 12;
    // [DEBUG] Class: D11File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler165, is_external=, is_static_class=False, static_prefix=
    private string _Filler165 ="$";
    
    
    
    
    // [DEBUG] Field: D11UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D11UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler166, is_external=, is_static_class=False, static_prefix=
    private string _Filler166 ="EXF.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD11FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler165.PadRight(1));
        result.Append(_D11UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler166.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD11FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller165(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD11UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller166(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD11FileAsString();
    }
    // Set<>String Override function
    public void SetD11File(string value)
    {
        SetD11FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller165()
    {
        return _Filler165;
    }
    
    // Standard Setter
    public void SetFiller165(string value)
    {
        _Filler165 = value;
    }
    
    // Get<>AsString()
    public string GetFiller165AsString()
    {
        return _Filler165.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller165AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler165 = value;
    }
    
    // Standard Getter
    public int GetD11UserNo()
    {
        return _D11UserNo;
    }
    
    // Standard Setter
    public void SetD11UserNo(int value)
    {
        _D11UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD11UserNoAsString()
    {
        return _D11UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD11UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D11UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller166()
    {
        return _Filler166;
    }
    
    // Standard Setter
    public void SetFiller166(string value)
    {
        _Filler166 = value;
    }
    
    // Get<>AsString()
    public string GetFiller166AsString()
    {
        return _Filler166.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller166AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler166 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}