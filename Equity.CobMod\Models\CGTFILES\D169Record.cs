using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D169Record Data Structure

public class D169Record
{
    private static int _size = 284;
    // [DEBUG] Class: D169Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D169Key, is_external=, is_static_class=False, static_prefix=
    private D169Key _D169Key = new D169Key();
    
    
    
    
    // [DEBUG] Field: D169Key2A, is_external=, is_static_class=False, static_prefix=
    private D169Key2A _D169Key2A = new D169Key2A();
    
    
    
    
    // [DEBUG] Field: D169Status, is_external=, is_static_class=False, static_prefix=
    private string _D169Status ="";
    
    
    
    
    // [DEBUG] Field: D169UserId, is_external=, is_static_class=False, static_prefix=
    private string _D169UserId ="";
    
    
    
    
    // [DEBUG] Field: D169Function, is_external=, is_static_class=False, static_prefix=
    private string _D169Function ="";
    
    
    
    
    // [DEBUG] Field: D169InverseKey2, is_external=, is_static_class=False, static_prefix=
    private string _D169InverseKey2 ="";
    
    
    
    
    // [DEBUG] Field: D169Information, is_external=, is_static_class=False, static_prefix=
    private string _D169Information ="";
    
    
    
    
    
    // Serialization methods
    public string GetD169RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D169Key.GetD169KeyAsString());
        result.Append(_D169Key2A.GetD169Key2AAsString());
        result.Append(_D169Status.PadRight(0));
        result.Append(_D169UserId.PadRight(0));
        result.Append(_D169Function.PadRight(0));
        result.Append(_D169InverseKey2.PadRight(15));
        result.Append(_D169Information.PadRight(255));
        
        return result.ToString();
    }
    
    public void SetD169RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 10 <= data.Length)
        {
            _D169Key.SetD169KeyAsString(data.Substring(offset, 10));
        }
        else
        {
            _D169Key.SetD169KeyAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 4 <= data.Length)
        {
            _D169Key2A.SetD169Key2AAsString(data.Substring(offset, 4));
        }
        else
        {
            _D169Key2A.SetD169Key2AAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD169Status(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD169UserId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD169Function(extracted);
        }
        offset += 0;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetD169InverseKey2(extracted);
        }
        offset += 15;
        if (offset + 255 <= data.Length)
        {
            string extracted = data.Substring(offset, 255).Trim();
            SetD169Information(extracted);
        }
        offset += 255;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD169RecordAsString();
    }
    // Set<>String Override function
    public void SetD169Record(string value)
    {
        SetD169RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D169Key GetD169Key()
    {
        return _D169Key;
    }
    
    // Standard Setter
    public void SetD169Key(D169Key value)
    {
        _D169Key = value;
    }
    
    // Get<>AsString()
    public string GetD169KeyAsString()
    {
        return _D169Key != null ? _D169Key.GetD169KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD169KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D169Key == null)
        {
            _D169Key = new D169Key();
        }
        _D169Key.SetD169KeyAsString(value);
    }
    
    // Standard Getter
    public D169Key2A GetD169Key2A()
    {
        return _D169Key2A;
    }
    
    // Standard Setter
    public void SetD169Key2A(D169Key2A value)
    {
        _D169Key2A = value;
    }
    
    // Get<>AsString()
    public string GetD169Key2AAsString()
    {
        return _D169Key2A != null ? _D169Key2A.GetD169Key2AAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD169Key2AAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D169Key2A == null)
        {
            _D169Key2A = new D169Key2A();
        }
        _D169Key2A.SetD169Key2AAsString(value);
    }
    
    // Standard Getter
    public string GetD169Status()
    {
        return _D169Status;
    }
    
    // Standard Setter
    public void SetD169Status(string value)
    {
        _D169Status = value;
    }
    
    // Get<>AsString()
    public string GetD169StatusAsString()
    {
        return _D169Status.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD169StatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D169Status = value;
    }
    
    // Standard Getter
    public string GetD169UserId()
    {
        return _D169UserId;
    }
    
    // Standard Setter
    public void SetD169UserId(string value)
    {
        _D169UserId = value;
    }
    
    // Get<>AsString()
    public string GetD169UserIdAsString()
    {
        return _D169UserId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD169UserIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D169UserId = value;
    }
    
    // Standard Getter
    public string GetD169Function()
    {
        return _D169Function;
    }
    
    // Standard Setter
    public void SetD169Function(string value)
    {
        _D169Function = value;
    }
    
    // Get<>AsString()
    public string GetD169FunctionAsString()
    {
        return _D169Function.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD169FunctionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D169Function = value;
    }
    
    // Standard Getter
    public string GetD169InverseKey2()
    {
        return _D169InverseKey2;
    }
    
    // Standard Setter
    public void SetD169InverseKey2(string value)
    {
        _D169InverseKey2 = value;
    }
    
    // Get<>AsString()
    public string GetD169InverseKey2AsString()
    {
        return _D169InverseKey2.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetD169InverseKey2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D169InverseKey2 = value;
    }
    
    // Standard Getter
    public string GetD169Information()
    {
        return _D169Information;
    }
    
    // Standard Setter
    public void SetD169Information(string value)
    {
        _D169Information = value;
    }
    
    // Get<>AsString()
    public string GetD169InformationAsString()
    {
        return _D169Information.PadRight(255);
    }
    
    // Set<>AsString()
    public void SetD169InformationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D169Information = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD169Key(string value)
    {
        _D169Key.SetD169KeyAsString(value);
    }
    // Nested Class: D169Key
    public class D169Key
    {
        private static int _size = 10;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D169PendingId, is_external=, is_static_class=False, static_prefix=
        private int _D169PendingId =0;
        
        
        
        
    public D169Key() {}
    
    public D169Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD169PendingId(int.Parse(data.Substring(offset, 10).Trim()));
        offset += 10;
        
    }
    
    // Serialization methods
    public string GetD169KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D169PendingId.ToString().PadLeft(10, '0'));
        
        return result.ToString();
    }
    
    public void SetD169KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD169PendingId(parsedInt);
        }
        offset += 10;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD169PendingId()
    {
        return _D169PendingId;
    }
    
    // Standard Setter
    public void SetD169PendingId(int value)
    {
        _D169PendingId = value;
    }
    
    // Get<>AsString()
    public string GetD169PendingIdAsString()
    {
        return _D169PendingId.ToString().PadLeft(10, '0');
    }
    
    // Set<>AsString()
    public void SetD169PendingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D169PendingId = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD169Key2A(string value)
{
    _D169Key2A.SetD169Key2AAsString(value);
}
// Nested Class: D169Key2A
public class D169Key2A
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D169SystemArea, is_external=, is_static_class=False, static_prefix=
    private int _D169SystemArea =0;
    
    
    
    
    // [DEBUG] Field: D169PendingType, is_external=, is_static_class=False, static_prefix=
    private string _D169PendingType ="";
    
    
    
    
public D169Key2A() {}

public D169Key2A(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD169SystemArea(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetD169PendingType(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD169Key2AAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D169SystemArea.ToString().PadLeft(4, '0'));
    result.Append(_D169PendingType.PadRight(0));
    
    return result.ToString();
}

public void SetD169Key2AAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD169SystemArea(parsedInt);
    }
    offset += 4;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD169PendingType(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public int GetD169SystemArea()
{
    return _D169SystemArea;
}

// Standard Setter
public void SetD169SystemArea(int value)
{
    _D169SystemArea = value;
}

// Get<>AsString()
public string GetD169SystemAreaAsString()
{
    return _D169SystemArea.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetD169SystemAreaAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D169SystemArea = parsed;
}

// Standard Getter
public string GetD169PendingType()
{
    return _D169PendingType;
}

// Standard Setter
public void SetD169PendingType(string value)
{
    _D169PendingType = value;
}

// Get<>AsString()
public string GetD169PendingTypeAsString()
{
    return _D169PendingType.PadRight(0);
}

// Set<>AsString()
public void SetD169PendingTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D169PendingType = value;
}



public static int GetSize()
{
    return _size;
}

}

}}