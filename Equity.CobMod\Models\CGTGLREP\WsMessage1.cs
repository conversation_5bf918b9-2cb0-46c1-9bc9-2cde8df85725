using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsMessage1 Data Structure

public class WsMessage1
{
    private static int _size = 22;
    // [DEBUG] Class: WsMessage1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler33, is_external=, is_static_class=False, static_prefix=
    private string _Filler33 ="RUNTIME ";
    
    
    
    
    // [DEBUG] Field: WsMessDd, is_external=, is_static_class=False, static_prefix=
    private int _WsMessDd =0;
    
    
    
    
    // [DEBUG] Field: Filler34, is_external=, is_static_class=False, static_prefix=
    private string _Filler34 ="/";
    
    
    
    
    // [DEBUG] Field: WsMessMm, is_external=, is_static_class=False, static_prefix=
    private int _WsMessMm =0;
    
    
    
    
    // [DEBUG] Field: Filler35, is_external=, is_static_class=False, static_prefix=
    private string _Filler35 ="/";
    
    
    
    
    // [DEBUG] Field: WsMessYy, is_external=, is_static_class=False, static_prefix=
    private int _WsMessYy =0;
    
    
    
    
    // [DEBUG] Field: Filler36, is_external=, is_static_class=False, static_prefix=
    private string _Filler36 ="";
    
    
    
    
    // [DEBUG] Field: WsMessHour, is_external=, is_static_class=False, static_prefix=
    private int _WsMessHour =0;
    
    
    
    
    // [DEBUG] Field: Filler37, is_external=, is_static_class=False, static_prefix=
    private string _Filler37 =":";
    
    
    
    
    // [DEBUG] Field: WsMessMin, is_external=, is_static_class=False, static_prefix=
    private int _WsMessMin =0;
    
    
    
    
    
    // Serialization methods
    public string GetWsMessage1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler33.PadRight(8));
        result.Append(_WsMessDd.ToString().PadLeft(2, '0'));
        result.Append(_Filler34.PadRight(1));
        result.Append(_WsMessMm.ToString().PadLeft(2, '0'));
        result.Append(_Filler35.PadRight(1));
        result.Append(_WsMessYy.ToString().PadLeft(2, '0'));
        result.Append(_Filler36.PadRight(1));
        result.Append(_WsMessHour.ToString().PadLeft(2, '0'));
        result.Append(_Filler37.PadRight(1));
        result.Append(_WsMessMin.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWsMessage1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller33(extracted);
        }
        offset += 8;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessDd(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller34(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessMm(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller35(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessYy(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller36(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessHour(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller37(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessMin(parsedInt);
        }
        offset += 2;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsMessage1AsString();
    }
    // Set<>String Override function
    public void SetWsMessage1(string value)
    {
        SetWsMessage1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller33()
    {
        return _Filler33;
    }
    
    // Standard Setter
    public void SetFiller33(string value)
    {
        _Filler33 = value;
    }
    
    // Get<>AsString()
    public string GetFiller33AsString()
    {
        return _Filler33.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller33AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler33 = value;
    }
    
    // Standard Getter
    public int GetWsMessDd()
    {
        return _WsMessDd;
    }
    
    // Standard Setter
    public void SetWsMessDd(int value)
    {
        _WsMessDd = value;
    }
    
    // Get<>AsString()
    public string GetWsMessDdAsString()
    {
        return _WsMessDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessDd = parsed;
    }
    
    // Standard Getter
    public string GetFiller34()
    {
        return _Filler34;
    }
    
    // Standard Setter
    public void SetFiller34(string value)
    {
        _Filler34 = value;
    }
    
    // Get<>AsString()
    public string GetFiller34AsString()
    {
        return _Filler34.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller34AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler34 = value;
    }
    
    // Standard Getter
    public int GetWsMessMm()
    {
        return _WsMessMm;
    }
    
    // Standard Setter
    public void SetWsMessMm(int value)
    {
        _WsMessMm = value;
    }
    
    // Get<>AsString()
    public string GetWsMessMmAsString()
    {
        return _WsMessMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessMm = parsed;
    }
    
    // Standard Getter
    public string GetFiller35()
    {
        return _Filler35;
    }
    
    // Standard Setter
    public void SetFiller35(string value)
    {
        _Filler35 = value;
    }
    
    // Get<>AsString()
    public string GetFiller35AsString()
    {
        return _Filler35.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller35AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler35 = value;
    }
    
    // Standard Getter
    public int GetWsMessYy()
    {
        return _WsMessYy;
    }
    
    // Standard Setter
    public void SetWsMessYy(int value)
    {
        _WsMessYy = value;
    }
    
    // Get<>AsString()
    public string GetWsMessYyAsString()
    {
        return _WsMessYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessYy = parsed;
    }
    
    // Standard Getter
    public string GetFiller36()
    {
        return _Filler36;
    }
    
    // Standard Setter
    public void SetFiller36(string value)
    {
        _Filler36 = value;
    }
    
    // Get<>AsString()
    public string GetFiller36AsString()
    {
        return _Filler36.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller36AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler36 = value;
    }
    
    // Standard Getter
    public int GetWsMessHour()
    {
        return _WsMessHour;
    }
    
    // Standard Setter
    public void SetWsMessHour(int value)
    {
        _WsMessHour = value;
    }
    
    // Get<>AsString()
    public string GetWsMessHourAsString()
    {
        return _WsMessHour.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessHourAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessHour = parsed;
    }
    
    // Standard Getter
    public string GetFiller37()
    {
        return _Filler37;
    }
    
    // Standard Setter
    public void SetFiller37(string value)
    {
        _Filler37 = value;
    }
    
    // Get<>AsString()
    public string GetFiller37AsString()
    {
        return _Filler37.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller37AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler37 = value;
    }
    
    // Standard Getter
    public int GetWsMessMin()
    {
        return _WsMessMin;
    }
    
    // Standard Setter
    public void SetWsMessMin(int value)
    {
        _WsMessMin = value;
    }
    
    // Get<>AsString()
    public string GetWsMessMinAsString()
    {
        return _WsMessMin.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessMinAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessMin = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}