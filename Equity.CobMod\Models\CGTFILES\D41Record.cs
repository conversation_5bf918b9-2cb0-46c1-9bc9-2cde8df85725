using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D41Record Data Structure

public class D41Record
{
    private static int _size = 140;
    // [DEBUG] Class: D41Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D41Key, is_external=, is_static_class=False, static_prefix=
    private D41Key _D41Key = new D41Key();
    
    
    
    
    // [DEBUG] Field: D41Ind, is_external=, is_static_class=False, static_prefix=
    private int _D41Ind =0;
    
    
    
    
    // [DEBUG] Field: D41CompanyName, is_external=, is_static_class=False, static_prefix=
    private string _D41CompanyName ="";
    
    
    
    
    // [DEBUG] Field: D41SecurtyDesc, is_external=, is_static_class=False, static_prefix=
    private string _D41SecurtyDesc ="";
    
    
    
    
    // [DEBUG] Field: D41NomValueX, is_external=, is_static_class=False, static_prefix=
    private D41NomValueX _D41NomValueX = new D41NomValueX();
    
    
    
    
    // [DEBUG] Field: D41NomInd, is_external=, is_static_class=False, static_prefix=
    private int _D41NomInd =0;
    
    
    
    
    // [DEBUG] Field: D41CcyInd, is_external=, is_static_class=False, static_prefix=
    private int _D41CcyInd =0;
    
    
    
    
    // [DEBUG] Field: D41BomtPriceN, is_external=, is_static_class=False, static_prefix=
    private int _D41BomtPriceN =0;
    
    
    
    
    // [DEBUG] Field: D41BomtPriceX, is_external=, is_static_class=False, static_prefix=
    private D41BomtPriceX _D41BomtPriceX = new D41BomtPriceX();
    
    
    
    
    // [DEBUG] Field: D41OfferPriceN, is_external=, is_static_class=False, static_prefix=
    private int _D41OfferPriceN =0;
    
    
    
    
    // [DEBUG] Field: D41OfferPriceX, is_external=, is_static_class=False, static_prefix=
    private D41OfferPriceX _D41OfferPriceX = new D41OfferPriceX();
    
    
    
    
    // [DEBUG] Field: D41TypeInd, is_external=, is_static_class=False, static_prefix=
    private string _D41TypeInd ="";
    
    
    
    
    // [DEBUG] Field: D41FreqInd, is_external=, is_static_class=False, static_prefix=
    private int _D41FreqInd =0;
    
    
    
    
    // [DEBUG] Field: D41CumExdivInd, is_external=, is_static_class=False, static_prefix=
    private string _D41CumExdivInd ="";
    
    
    
    
    // [DEBUG] Field: D41OtherCumexInd, is_external=, is_static_class=False, static_prefix=
    private string _D41OtherCumexInd ="";
    
    
    
    
    // [DEBUG] Field: D41PpiInd, is_external=, is_static_class=False, static_prefix=
    private int _D41PpiInd =0;
    
    
    
    
    // [DEBUG] Field: D41UpdateDate, is_external=, is_static_class=False, static_prefix=
    private int _D41UpdateDate =0;
    
    
    
    
    // [DEBUG] Field: D41RedTypeInd, is_external=, is_static_class=False, static_prefix=
    private string _D41RedTypeInd ="";
    
    
    
    
    // [DEBUG] Field: D41EarlyRedDate, is_external=, is_static_class=False, static_prefix=
    private int _D41EarlyRedDate =0;
    
    
    
    
    // [DEBUG] Field: D41FinalRedDate, is_external=, is_static_class=False, static_prefix=
    private int _D41FinalRedDate =0;
    
    
    
    
    // [DEBUG] Field: D41ForcastTotDiv, is_external=, is_static_class=False, static_prefix=
    private int _D41ForcastTotDiv =0;
    
    
    
    
    // [DEBUG] Field: D41ForcastDivInd, is_external=, is_static_class=False, static_prefix=
    private string _D41ForcastDivInd ="";
    
    
    
    
    // [DEBUG] Field: Filler25, is_external=, is_static_class=False, static_prefix=
    private string _Filler25 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD41RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D41Key.GetD41KeyAsString());
        result.Append(_D41Ind.ToString().PadLeft(1, '0'));
        result.Append(_D41CompanyName.PadRight(18));
        result.Append(_D41SecurtyDesc.PadRight(27));
        result.Append(_D41NomValueX.GetD41NomValueXAsString());
        result.Append(_D41NomInd.ToString().PadLeft(1, '0'));
        result.Append(_D41CcyInd.ToString().PadLeft(1, '0'));
        result.Append(_D41BomtPriceN.ToString().PadLeft(11, '0'));
        result.Append(_D41BomtPriceX.GetD41BomtPriceXAsString());
        result.Append(_D41OfferPriceN.ToString().PadLeft(11, '0'));
        result.Append(_D41OfferPriceX.GetD41OfferPriceXAsString());
        result.Append(_D41TypeInd.PadRight(0));
        result.Append(_D41FreqInd.ToString().PadLeft(1, '0'));
        result.Append(_D41CumExdivInd.PadRight(0));
        result.Append(_D41OtherCumexInd.PadRight(0));
        result.Append(_D41PpiInd.ToString().PadLeft(1, '0'));
        result.Append(_D41UpdateDate.ToString().PadLeft(3, '0'));
        result.Append(_D41RedTypeInd.PadRight(0));
        result.Append(_D41EarlyRedDate.ToString().PadLeft(7, '0'));
        result.Append(_D41FinalRedDate.ToString().PadLeft(7, '0'));
        result.Append(_D41ForcastTotDiv.ToString().PadLeft(11, '0'));
        result.Append(_D41ForcastDivInd.PadRight(0));
        result.Append(_Filler25.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD41RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            _D41Key.SetD41KeyAsString(data.Substring(offset, 7));
        }
        else
        {
            _D41Key.SetD41KeyAsString(data.Substring(offset));
        }
        offset += 7;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41Ind(parsedInt);
        }
        offset += 1;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetD41CompanyName(extracted);
        }
        offset += 18;
        if (offset + 27 <= data.Length)
        {
            string extracted = data.Substring(offset, 27).Trim();
            SetD41SecurtyDesc(extracted);
        }
        offset += 27;
        if (offset + 11 <= data.Length)
        {
            _D41NomValueX.SetD41NomValueXAsString(data.Substring(offset, 11));
        }
        else
        {
            _D41NomValueX.SetD41NomValueXAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41NomInd(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41CcyInd(parsedInt);
        }
        offset += 1;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41BomtPriceN(parsedInt);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            _D41BomtPriceX.SetD41BomtPriceXAsString(data.Substring(offset, 11));
        }
        else
        {
            _D41BomtPriceX.SetD41BomtPriceXAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41OfferPriceN(parsedInt);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            _D41OfferPriceX.SetD41OfferPriceXAsString(data.Substring(offset, 11));
        }
        else
        {
            _D41OfferPriceX.SetD41OfferPriceXAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD41TypeInd(extracted);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41FreqInd(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD41CumExdivInd(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD41OtherCumexInd(extracted);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41PpiInd(parsedInt);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41UpdateDate(parsedInt);
        }
        offset += 3;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD41RedTypeInd(extracted);
        }
        offset += 0;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41EarlyRedDate(parsedInt);
        }
        offset += 7;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41FinalRedDate(parsedInt);
        }
        offset += 7;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41ForcastTotDiv(parsedInt);
        }
        offset += 11;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD41ForcastDivInd(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller25(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD41RecordAsString();
    }
    // Set<>String Override function
    public void SetD41Record(string value)
    {
        SetD41RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D41Key GetD41Key()
    {
        return _D41Key;
    }
    
    // Standard Setter
    public void SetD41Key(D41Key value)
    {
        _D41Key = value;
    }
    
    // Get<>AsString()
    public string GetD41KeyAsString()
    {
        return _D41Key != null ? _D41Key.GetD41KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD41KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D41Key == null)
        {
            _D41Key = new D41Key();
        }
        _D41Key.SetD41KeyAsString(value);
    }
    
    // Standard Getter
    public int GetD41Ind()
    {
        return _D41Ind;
    }
    
    // Standard Setter
    public void SetD41Ind(int value)
    {
        _D41Ind = value;
    }
    
    // Get<>AsString()
    public string GetD41IndAsString()
    {
        return _D41Ind.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD41IndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41Ind = parsed;
    }
    
    // Standard Getter
    public string GetD41CompanyName()
    {
        return _D41CompanyName;
    }
    
    // Standard Setter
    public void SetD41CompanyName(string value)
    {
        _D41CompanyName = value;
    }
    
    // Get<>AsString()
    public string GetD41CompanyNameAsString()
    {
        return _D41CompanyName.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetD41CompanyNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41CompanyName = value;
    }
    
    // Standard Getter
    public string GetD41SecurtyDesc()
    {
        return _D41SecurtyDesc;
    }
    
    // Standard Setter
    public void SetD41SecurtyDesc(string value)
    {
        _D41SecurtyDesc = value;
    }
    
    // Get<>AsString()
    public string GetD41SecurtyDescAsString()
    {
        return _D41SecurtyDesc.PadRight(27);
    }
    
    // Set<>AsString()
    public void SetD41SecurtyDescAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41SecurtyDesc = value;
    }
    
    // Standard Getter
    public D41NomValueX GetD41NomValueX()
    {
        return _D41NomValueX;
    }
    
    // Standard Setter
    public void SetD41NomValueX(D41NomValueX value)
    {
        _D41NomValueX = value;
    }
    
    // Get<>AsString()
    public string GetD41NomValueXAsString()
    {
        return _D41NomValueX != null ? _D41NomValueX.GetD41NomValueXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD41NomValueXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D41NomValueX == null)
        {
            _D41NomValueX = new D41NomValueX();
        }
        _D41NomValueX.SetD41NomValueXAsString(value);
    }
    
    // Standard Getter
    public int GetD41NomInd()
    {
        return _D41NomInd;
    }
    
    // Standard Setter
    public void SetD41NomInd(int value)
    {
        _D41NomInd = value;
    }
    
    // Get<>AsString()
    public string GetD41NomIndAsString()
    {
        return _D41NomInd.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD41NomIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41NomInd = parsed;
    }
    
    // Standard Getter
    public int GetD41CcyInd()
    {
        return _D41CcyInd;
    }
    
    // Standard Setter
    public void SetD41CcyInd(int value)
    {
        _D41CcyInd = value;
    }
    
    // Get<>AsString()
    public string GetD41CcyIndAsString()
    {
        return _D41CcyInd.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD41CcyIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41CcyInd = parsed;
    }
    
    // Standard Getter
    public int GetD41BomtPriceN()
    {
        return _D41BomtPriceN;
    }
    
    // Standard Setter
    public void SetD41BomtPriceN(int value)
    {
        _D41BomtPriceN = value;
    }
    
    // Get<>AsString()
    public string GetD41BomtPriceNAsString()
    {
        return _D41BomtPriceN.ToString().PadLeft(11, '0');
    }
    
    // Set<>AsString()
    public void SetD41BomtPriceNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41BomtPriceN = parsed;
    }
    
    // Standard Getter
    public D41BomtPriceX GetD41BomtPriceX()
    {
        return _D41BomtPriceX;
    }
    
    // Standard Setter
    public void SetD41BomtPriceX(D41BomtPriceX value)
    {
        _D41BomtPriceX = value;
    }
    
    // Get<>AsString()
    public string GetD41BomtPriceXAsString()
    {
        return _D41BomtPriceX != null ? _D41BomtPriceX.GetD41BomtPriceXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD41BomtPriceXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D41BomtPriceX == null)
        {
            _D41BomtPriceX = new D41BomtPriceX();
        }
        _D41BomtPriceX.SetD41BomtPriceXAsString(value);
    }
    
    // Standard Getter
    public int GetD41OfferPriceN()
    {
        return _D41OfferPriceN;
    }
    
    // Standard Setter
    public void SetD41OfferPriceN(int value)
    {
        _D41OfferPriceN = value;
    }
    
    // Get<>AsString()
    public string GetD41OfferPriceNAsString()
    {
        return _D41OfferPriceN.ToString().PadLeft(11, '0');
    }
    
    // Set<>AsString()
    public void SetD41OfferPriceNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41OfferPriceN = parsed;
    }
    
    // Standard Getter
    public D41OfferPriceX GetD41OfferPriceX()
    {
        return _D41OfferPriceX;
    }
    
    // Standard Setter
    public void SetD41OfferPriceX(D41OfferPriceX value)
    {
        _D41OfferPriceX = value;
    }
    
    // Get<>AsString()
    public string GetD41OfferPriceXAsString()
    {
        return _D41OfferPriceX != null ? _D41OfferPriceX.GetD41OfferPriceXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD41OfferPriceXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D41OfferPriceX == null)
        {
            _D41OfferPriceX = new D41OfferPriceX();
        }
        _D41OfferPriceX.SetD41OfferPriceXAsString(value);
    }
    
    // Standard Getter
    public string GetD41TypeInd()
    {
        return _D41TypeInd;
    }
    
    // Standard Setter
    public void SetD41TypeInd(string value)
    {
        _D41TypeInd = value;
    }
    
    // Get<>AsString()
    public string GetD41TypeIndAsString()
    {
        return _D41TypeInd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD41TypeIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41TypeInd = value;
    }
    
    // Standard Getter
    public int GetD41FreqInd()
    {
        return _D41FreqInd;
    }
    
    // Standard Setter
    public void SetD41FreqInd(int value)
    {
        _D41FreqInd = value;
    }
    
    // Get<>AsString()
    public string GetD41FreqIndAsString()
    {
        return _D41FreqInd.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD41FreqIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41FreqInd = parsed;
    }
    
    // Standard Getter
    public string GetD41CumExdivInd()
    {
        return _D41CumExdivInd;
    }
    
    // Standard Setter
    public void SetD41CumExdivInd(string value)
    {
        _D41CumExdivInd = value;
    }
    
    // Get<>AsString()
    public string GetD41CumExdivIndAsString()
    {
        return _D41CumExdivInd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD41CumExdivIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41CumExdivInd = value;
    }
    
    // Standard Getter
    public string GetD41OtherCumexInd()
    {
        return _D41OtherCumexInd;
    }
    
    // Standard Setter
    public void SetD41OtherCumexInd(string value)
    {
        _D41OtherCumexInd = value;
    }
    
    // Get<>AsString()
    public string GetD41OtherCumexIndAsString()
    {
        return _D41OtherCumexInd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD41OtherCumexIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41OtherCumexInd = value;
    }
    
    // Standard Getter
    public int GetD41PpiInd()
    {
        return _D41PpiInd;
    }
    
    // Standard Setter
    public void SetD41PpiInd(int value)
    {
        _D41PpiInd = value;
    }
    
    // Get<>AsString()
    public string GetD41PpiIndAsString()
    {
        return _D41PpiInd.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD41PpiIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41PpiInd = parsed;
    }
    
    // Standard Getter
    public int GetD41UpdateDate()
    {
        return _D41UpdateDate;
    }
    
    // Standard Setter
    public void SetD41UpdateDate(int value)
    {
        _D41UpdateDate = value;
    }
    
    // Get<>AsString()
    public string GetD41UpdateDateAsString()
    {
        return _D41UpdateDate.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetD41UpdateDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41UpdateDate = parsed;
    }
    
    // Standard Getter
    public string GetD41RedTypeInd()
    {
        return _D41RedTypeInd;
    }
    
    // Standard Setter
    public void SetD41RedTypeInd(string value)
    {
        _D41RedTypeInd = value;
    }
    
    // Get<>AsString()
    public string GetD41RedTypeIndAsString()
    {
        return _D41RedTypeInd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD41RedTypeIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41RedTypeInd = value;
    }
    
    // Standard Getter
    public int GetD41EarlyRedDate()
    {
        return _D41EarlyRedDate;
    }
    
    // Standard Setter
    public void SetD41EarlyRedDate(int value)
    {
        _D41EarlyRedDate = value;
    }
    
    // Get<>AsString()
    public string GetD41EarlyRedDateAsString()
    {
        return _D41EarlyRedDate.ToString().PadLeft(7, '0');
    }
    
    // Set<>AsString()
    public void SetD41EarlyRedDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41EarlyRedDate = parsed;
    }
    
    // Standard Getter
    public int GetD41FinalRedDate()
    {
        return _D41FinalRedDate;
    }
    
    // Standard Setter
    public void SetD41FinalRedDate(int value)
    {
        _D41FinalRedDate = value;
    }
    
    // Get<>AsString()
    public string GetD41FinalRedDateAsString()
    {
        return _D41FinalRedDate.ToString().PadLeft(7, '0');
    }
    
    // Set<>AsString()
    public void SetD41FinalRedDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41FinalRedDate = parsed;
    }
    
    // Standard Getter
    public int GetD41ForcastTotDiv()
    {
        return _D41ForcastTotDiv;
    }
    
    // Standard Setter
    public void SetD41ForcastTotDiv(int value)
    {
        _D41ForcastTotDiv = value;
    }
    
    // Get<>AsString()
    public string GetD41ForcastTotDivAsString()
    {
        return _D41ForcastTotDiv.ToString().PadLeft(11, '0');
    }
    
    // Set<>AsString()
    public void SetD41ForcastTotDivAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41ForcastTotDiv = parsed;
    }
    
    // Standard Getter
    public string GetD41ForcastDivInd()
    {
        return _D41ForcastDivInd;
    }
    
    // Standard Setter
    public void SetD41ForcastDivInd(string value)
    {
        _D41ForcastDivInd = value;
    }
    
    // Get<>AsString()
    public string GetD41ForcastDivIndAsString()
    {
        return _D41ForcastDivInd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD41ForcastDivIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41ForcastDivInd = value;
    }
    
    // Standard Getter
    public string GetFiller25()
    {
        return _Filler25;
    }
    
    // Standard Setter
    public void SetFiller25(string value)
    {
        _Filler25 = value;
    }
    
    // Get<>AsString()
    public string GetFiller25AsString()
    {
        return _Filler25.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller25AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler25 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD41Key(string value)
    {
        _D41Key.SetD41KeyAsString(value);
    }
    // Nested Class: D41Key
    public class D41Key
    {
        private static int _size = 7;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D41SedolCode, is_external=, is_static_class=False, static_prefix=
        private int _D41SedolCode =0;
        
        
        
        
    public D41Key() {}
    
    public D41Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD41SedolCode(int.Parse(data.Substring(offset, 7).Trim()));
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetD41KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D41SedolCode.ToString().PadLeft(7, '0'));
        
        return result.ToString();
    }
    
    public void SetD41KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41SedolCode(parsedInt);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD41SedolCode()
    {
        return _D41SedolCode;
    }
    
    // Standard Setter
    public void SetD41SedolCode(int value)
    {
        _D41SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD41SedolCodeAsString()
    {
        return _D41SedolCode.ToString().PadLeft(7, '0');
    }
    
    // Set<>AsString()
    public void SetD41SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41SedolCode = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD41NomValueX(string value)
{
    _D41NomValueX.SetD41NomValueXAsString(value);
}
// Nested Class: D41NomValueX
public class D41NomValueX
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D41NomCcy, is_external=, is_static_class=False, static_prefix=
    private int _D41NomCcy =0;
    
    
    
    
    // [DEBUG] Field: D41NomValu, is_external=, is_static_class=False, static_prefix=
    private int _D41NomValu =0;
    
    
    
    
public D41NomValueX() {}

public D41NomValueX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD41NomCcy(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetD41NomValu(int.Parse(data.Substring(offset, 8).Trim()));
    offset += 8;
    
}

// Serialization methods
public string GetD41NomValueXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D41NomCcy.ToString().PadLeft(3, '0'));
    result.Append(_D41NomValu.ToString().PadLeft(8, '0'));
    
    return result.ToString();
}

public void SetD41NomValueXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD41NomCcy(parsedInt);
    }
    offset += 3;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD41NomValu(parsedInt);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public int GetD41NomCcy()
{
    return _D41NomCcy;
}

// Standard Setter
public void SetD41NomCcy(int value)
{
    _D41NomCcy = value;
}

// Get<>AsString()
public string GetD41NomCcyAsString()
{
    return _D41NomCcy.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetD41NomCcyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D41NomCcy = parsed;
}

// Standard Getter
public int GetD41NomValu()
{
    return _D41NomValu;
}

// Standard Setter
public void SetD41NomValu(int value)
{
    _D41NomValu = value;
}

// Get<>AsString()
public string GetD41NomValuAsString()
{
    return _D41NomValu.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetD41NomValuAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D41NomValu = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD41BomtPriceX(string value)
{
    _D41BomtPriceX.SetD41BomtPriceXAsString(value);
}
// Nested Class: D41BomtPriceX
public class D41BomtPriceX
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D41BomtCcy, is_external=, is_static_class=False, static_prefix=
    private int _D41BomtCcy =0;
    
    
    
    
    // [DEBUG] Field: D41BomtPrice, is_external=, is_static_class=False, static_prefix=
    private int _D41BomtPrice =0;
    
    
    
    
public D41BomtPriceX() {}

public D41BomtPriceX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD41BomtCcy(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetD41BomtPrice(int.Parse(data.Substring(offset, 8).Trim()));
    offset += 8;
    
}

// Serialization methods
public string GetD41BomtPriceXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D41BomtCcy.ToString().PadLeft(3, '0'));
    result.Append(_D41BomtPrice.ToString().PadLeft(8, '0'));
    
    return result.ToString();
}

public void SetD41BomtPriceXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD41BomtCcy(parsedInt);
    }
    offset += 3;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD41BomtPrice(parsedInt);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public int GetD41BomtCcy()
{
    return _D41BomtCcy;
}

// Standard Setter
public void SetD41BomtCcy(int value)
{
    _D41BomtCcy = value;
}

// Get<>AsString()
public string GetD41BomtCcyAsString()
{
    return _D41BomtCcy.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetD41BomtCcyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D41BomtCcy = parsed;
}

// Standard Getter
public int GetD41BomtPrice()
{
    return _D41BomtPrice;
}

// Standard Setter
public void SetD41BomtPrice(int value)
{
    _D41BomtPrice = value;
}

// Get<>AsString()
public string GetD41BomtPriceAsString()
{
    return _D41BomtPrice.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetD41BomtPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D41BomtPrice = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD41OfferPriceX(string value)
{
    _D41OfferPriceX.SetD41OfferPriceXAsString(value);
}
// Nested Class: D41OfferPriceX
public class D41OfferPriceX
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D41OfferCcy, is_external=, is_static_class=False, static_prefix=
    private int _D41OfferCcy =0;
    
    
    
    
    // [DEBUG] Field: D41OfferPrice, is_external=, is_static_class=False, static_prefix=
    private int _D41OfferPrice =0;
    
    
    
    
public D41OfferPriceX() {}

public D41OfferPriceX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD41OfferCcy(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetD41OfferPrice(int.Parse(data.Substring(offset, 8).Trim()));
    offset += 8;
    
}

// Serialization methods
public string GetD41OfferPriceXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D41OfferCcy.ToString().PadLeft(3, '0'));
    result.Append(_D41OfferPrice.ToString().PadLeft(8, '0'));
    
    return result.ToString();
}

public void SetD41OfferPriceXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD41OfferCcy(parsedInt);
    }
    offset += 3;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD41OfferPrice(parsedInt);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public int GetD41OfferCcy()
{
    return _D41OfferCcy;
}

// Standard Setter
public void SetD41OfferCcy(int value)
{
    _D41OfferCcy = value;
}

// Get<>AsString()
public string GetD41OfferCcyAsString()
{
    return _D41OfferCcy.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetD41OfferCcyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D41OfferCcy = parsed;
}

// Standard Getter
public int GetD41OfferPrice()
{
    return _D41OfferPrice;
}

// Standard Setter
public void SetD41OfferPrice(int value)
{
    _D41OfferPrice = value;
}

// Get<>AsString()
public string GetD41OfferPriceAsString()
{
    return _D41OfferPrice.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetD41OfferPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D41OfferPrice = parsed;
}



public static int GetSize()
{
    return _size;
}

}

}}