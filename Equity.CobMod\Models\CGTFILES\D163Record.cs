using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D163Record Data Structure

public class D163Record
{
    private static int _size = 40;
    // [DEBUG] Class: D163Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D163Key, is_external=, is_static_class=False, static_prefix=
    private D163Key _D163Key = new D163Key();
    
    
    
    
    // [DEBUG] Field: D163ReconciliationCode, is_external=, is_static_class=False, static_prefix=
    private string _D163ReconciliationCode ="";
    
    
    
    
    // [DEBUG] Field: D163Description, is_external=, is_static_class=False, static_prefix=
    private string _D163Description ="";
    
    
    
    
    // [DEBUG] Field: D163InverseKey, is_external=, is_static_class=False, static_prefix=
    private string _D163InverseKey ="";
    
    
    
    
    
    // Serialization methods
    public string GetD163RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D163Key.GetD163KeyAsString());
        result.Append(_D163ReconciliationCode.PadRight(0));
        result.Append(_D163Description.PadRight(40));
        result.Append(_D163InverseKey.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD163RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            _D163Key.SetD163KeyAsString(data.Substring(offset, 0));
        }
        else
        {
            _D163Key.SetD163KeyAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD163ReconciliationCode(extracted);
        }
        offset += 0;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD163Description(extracted);
        }
        offset += 40;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD163InverseKey(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD163RecordAsString();
    }
    // Set<>String Override function
    public void SetD163Record(string value)
    {
        SetD163RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D163Key GetD163Key()
    {
        return _D163Key;
    }
    
    // Standard Setter
    public void SetD163Key(D163Key value)
    {
        _D163Key = value;
    }
    
    // Get<>AsString()
    public string GetD163KeyAsString()
    {
        return _D163Key != null ? _D163Key.GetD163KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD163KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D163Key == null)
        {
            _D163Key = new D163Key();
        }
        _D163Key.SetD163KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD163ReconciliationCode()
    {
        return _D163ReconciliationCode;
    }
    
    // Standard Setter
    public void SetD163ReconciliationCode(string value)
    {
        _D163ReconciliationCode = value;
    }
    
    // Get<>AsString()
    public string GetD163ReconciliationCodeAsString()
    {
        return _D163ReconciliationCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD163ReconciliationCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D163ReconciliationCode = value;
    }
    
    // Standard Getter
    public string GetD163Description()
    {
        return _D163Description;
    }
    
    // Standard Setter
    public void SetD163Description(string value)
    {
        _D163Description = value;
    }
    
    // Get<>AsString()
    public string GetD163DescriptionAsString()
    {
        return _D163Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD163DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D163Description = value;
    }
    
    // Standard Getter
    public string GetD163InverseKey()
    {
        return _D163InverseKey;
    }
    
    // Standard Setter
    public void SetD163InverseKey(string value)
    {
        _D163InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD163InverseKeyAsString()
    {
        return _D163InverseKey.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD163InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D163InverseKey = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD163Key(string value)
    {
        _D163Key.SetD163KeyAsString(value);
    }
    // Nested Class: D163Key
    public class D163Key
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D163NonOlabFundCode, is_external=, is_static_class=False, static_prefix=
        private string _D163NonOlabFundCode ="";
        
        
        
        
        // [DEBUG] Field: D163OlabFundCode, is_external=, is_static_class=False, static_prefix=
        private string _D163OlabFundCode ="";
        
        
        
        
    public D163Key() {}
    
    public D163Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD163NonOlabFundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD163OlabFundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD163KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D163NonOlabFundCode.PadRight(0));
        result.Append(_D163OlabFundCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD163KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD163NonOlabFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD163OlabFundCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD163NonOlabFundCode()
    {
        return _D163NonOlabFundCode;
    }
    
    // Standard Setter
    public void SetD163NonOlabFundCode(string value)
    {
        _D163NonOlabFundCode = value;
    }
    
    // Get<>AsString()
    public string GetD163NonOlabFundCodeAsString()
    {
        return _D163NonOlabFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD163NonOlabFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D163NonOlabFundCode = value;
    }
    
    // Standard Getter
    public string GetD163OlabFundCode()
    {
        return _D163OlabFundCode;
    }
    
    // Standard Setter
    public void SetD163OlabFundCode(string value)
    {
        _D163OlabFundCode = value;
    }
    
    // Get<>AsString()
    public string GetD163OlabFundCodeAsString()
    {
        return _D163OlabFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD163OlabFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D163OlabFundCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}