using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D105File Data Structure

public class D105File
{
    private static int _size = 12;
    // [DEBUG] Class: D105File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler303, is_external=, is_static_class=False, static_prefix=
    private string _Filler303 ="$";
    
    
    
    
    // [DEBUG] Field: D105UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D105UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler304, is_external=, is_static_class=False, static_prefix=
    private string _Filler304 ="RT";
    
    
    
    
    // [DEBUG] Field: D105ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D105ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler305, is_external=, is_static_class=False, static_prefix=
    private string _Filler305 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD105FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler303.PadRight(1));
        result.Append(_D105UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler304.PadRight(2));
        result.Append(_D105ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler305.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD105FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller303(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD105UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller304(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD105ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller305(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD105FileAsString();
    }
    // Set<>String Override function
    public void SetD105File(string value)
    {
        SetD105FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller303()
    {
        return _Filler303;
    }
    
    // Standard Setter
    public void SetFiller303(string value)
    {
        _Filler303 = value;
    }
    
    // Get<>AsString()
    public string GetFiller303AsString()
    {
        return _Filler303.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller303AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler303 = value;
    }
    
    // Standard Getter
    public int GetD105UserNo()
    {
        return _D105UserNo;
    }
    
    // Standard Setter
    public void SetD105UserNo(int value)
    {
        _D105UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD105UserNoAsString()
    {
        return _D105UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD105UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D105UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller304()
    {
        return _Filler304;
    }
    
    // Standard Setter
    public void SetFiller304(string value)
    {
        _Filler304 = value;
    }
    
    // Get<>AsString()
    public string GetFiller304AsString()
    {
        return _Filler304.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller304AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler304 = value;
    }
    
    // Standard Getter
    public int GetD105ReportNo()
    {
        return _D105ReportNo;
    }
    
    // Standard Setter
    public void SetD105ReportNo(int value)
    {
        _D105ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD105ReportNoAsString()
    {
        return _D105ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD105ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D105ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller305()
    {
        return _Filler305;
    }
    
    // Standard Setter
    public void SetFiller305(string value)
    {
        _Filler305 = value;
    }
    
    // Get<>AsString()
    public string GetFiller305AsString()
    {
        return _Filler305.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller305AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler305 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}