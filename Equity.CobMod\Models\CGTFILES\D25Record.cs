using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D25Record Data Structure

public class D25Record
{
    private static int _size = 133;
    // [DEBUG] Class: D25Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D25PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D25PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D25ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D25ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD25RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D25PrintControl.PadRight(1));
        result.Append(_D25ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD25RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD25PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD25ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD25RecordAsString();
    }
    // Set<>String Override function
    public void SetD25Record(string value)
    {
        SetD25RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD25PrintControl()
    {
        return _D25PrintControl;
    }
    
    // Standard Setter
    public void SetD25PrintControl(string value)
    {
        _D25PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD25PrintControlAsString()
    {
        return _D25PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD25PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D25PrintControl = value;
    }
    
    // Standard Getter
    public string GetD25ReportLine()
    {
        return _D25ReportLine;
    }
    
    // Standard Setter
    public void SetD25ReportLine(string value)
    {
        _D25ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD25ReportLineAsString()
    {
        return _D25ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD25ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D25ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}