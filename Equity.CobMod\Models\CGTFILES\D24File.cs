using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D24File Data Structure

public class D24File
{
    private static int _size = 12;
    // [DEBUG] Class: D24File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler187, is_external=, is_static_class=False, static_prefix=
    private string _Filler187 ="$";
    
    
    
    
    // [DEBUG] Field: D24UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D24UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler188, is_external=, is_static_class=False, static_prefix=
    private string _Filler188 ="R2";
    
    
    
    
    // [DEBUG] Field: D24ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D24ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler189, is_external=, is_static_class=False, static_prefix=
    private string _Filler189 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD24FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler187.PadRight(1));
        result.Append(_D24UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler188.PadRight(2));
        result.Append(_D24ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler189.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD24FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller187(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD24UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller188(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD24ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller189(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD24FileAsString();
    }
    // Set<>String Override function
    public void SetD24File(string value)
    {
        SetD24FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller187()
    {
        return _Filler187;
    }
    
    // Standard Setter
    public void SetFiller187(string value)
    {
        _Filler187 = value;
    }
    
    // Get<>AsString()
    public string GetFiller187AsString()
    {
        return _Filler187.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller187AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler187 = value;
    }
    
    // Standard Getter
    public int GetD24UserNo()
    {
        return _D24UserNo;
    }
    
    // Standard Setter
    public void SetD24UserNo(int value)
    {
        _D24UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD24UserNoAsString()
    {
        return _D24UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD24UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D24UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller188()
    {
        return _Filler188;
    }
    
    // Standard Setter
    public void SetFiller188(string value)
    {
        _Filler188 = value;
    }
    
    // Get<>AsString()
    public string GetFiller188AsString()
    {
        return _Filler188.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller188AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler188 = value;
    }
    
    // Standard Getter
    public int GetD24ReportNo()
    {
        return _D24ReportNo;
    }
    
    // Standard Setter
    public void SetD24ReportNo(int value)
    {
        _D24ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD24ReportNoAsString()
    {
        return _D24ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD24ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D24ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller189()
    {
        return _Filler189;
    }
    
    // Standard Setter
    public void SetFiller189(string value)
    {
        _Filler189 = value;
    }
    
    // Get<>AsString()
    public string GetFiller189AsString()
    {
        return _Filler189.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller189AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler189 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}