using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D12Record Data Structure

public class D12Record
{
    private static int _size = 210;
    // [DEBUG] Class: D12Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D12Key, is_external=, is_static_class=False, static_prefix=
    private D12Key _D12Key = new D12Key();
    
    
    
    
    // [DEBUG] Field: D12ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D12ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD12RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D12Key.GetD12KeyAsString());
        result.Append(_D12ReportLine.PadRight(200));
        
        return result.ToString();
    }
    
    public void SetD12RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 10 <= data.Length)
        {
            _D12Key.SetD12KeyAsString(data.Substring(offset, 10));
        }
        else
        {
            _D12Key.SetD12KeyAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 200 <= data.Length)
        {
            string extracted = data.Substring(offset, 200).Trim();
            SetD12ReportLine(extracted);
        }
        offset += 200;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD12RecordAsString();
    }
    // Set<>String Override function
    public void SetD12Record(string value)
    {
        SetD12RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D12Key GetD12Key()
    {
        return _D12Key;
    }
    
    // Standard Setter
    public void SetD12Key(D12Key value)
    {
        _D12Key = value;
    }
    
    // Get<>AsString()
    public string GetD12KeyAsString()
    {
        return _D12Key != null ? _D12Key.GetD12KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD12KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D12Key == null)
        {
            _D12Key = new D12Key();
        }
        _D12Key.SetD12KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD12ReportLine()
    {
        return _D12ReportLine;
    }
    
    // Standard Setter
    public void SetD12ReportLine(string value)
    {
        _D12ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD12ReportLineAsString()
    {
        return _D12ReportLine.PadRight(200);
    }
    
    // Set<>AsString()
    public void SetD12ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D12ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD12Key(string value)
    {
        _D12Key.SetD12KeyAsString(value);
    }
    // Nested Class: D12Key
    public class D12Key
    {
        private static int _size = 10;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D12LineNumber, is_external=, is_static_class=False, static_prefix=
        private int _D12LineNumber =0;
        
        
        
        
    public D12Key() {}
    
    public D12Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD12LineNumber(int.Parse(data.Substring(offset, 10).Trim()));
        offset += 10;
        
    }
    
    // Serialization methods
    public string GetD12KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D12LineNumber.ToString().PadLeft(10, '0'));
        
        return result.ToString();
    }
    
    public void SetD12KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD12LineNumber(parsedInt);
        }
        offset += 10;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD12LineNumber()
    {
        return _D12LineNumber;
    }
    
    // Standard Setter
    public void SetD12LineNumber(int value)
    {
        _D12LineNumber = value;
    }
    
    // Get<>AsString()
    public string GetD12LineNumberAsString()
    {
        return _D12LineNumber.ToString().PadLeft(10, '0');
    }
    
    // Set<>AsString()
    public void SetD12LineNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D12LineNumber = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}