using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D41RecHed Data Structure

public class D41RecHed
{
    private static int _size = 122;
    // [DEBUG] Class: D41RecHed, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D41Header, is_external=, is_static_class=False, static_prefix=
    private string _D41Header ="";
    
    
    
    
    // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
    private string _Filler26 ="";
    
    
    
    
    // [DEBUG] Field: D41DateX, is_external=, is_static_class=False, static_prefix=
    private string _D41DateX ="";
    
    
    
    
    // [DEBUG] Field: D41Date, is_external=, is_static_class=False, static_prefix=
    private D41Date _D41Date = new D41Date();
    
    
    
    
    // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
    private string _Filler27 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD41RecHedAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D41Header.PadRight(21));
        result.Append(_Filler26.PadRight(0));
        result.Append(_D41DateX.PadRight(0));
        result.Append(_D41Date.GetD41DateAsString());
        result.Append(_Filler27.PadRight(95));
        
        return result.ToString();
    }
    
    public void SetD41RecHedAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 21 <= data.Length)
        {
            string extracted = data.Substring(offset, 21).Trim();
            SetD41Header(extracted);
        }
        offset += 21;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller26(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD41DateX(extracted);
        }
        offset += 0;
        if (offset + 6 <= data.Length)
        {
            _D41Date.SetD41DateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D41Date.SetD41DateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 95 <= data.Length)
        {
            string extracted = data.Substring(offset, 95).Trim();
            SetFiller27(extracted);
        }
        offset += 95;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD41RecHedAsString();
    }
    // Set<>String Override function
    public void SetD41RecHed(string value)
    {
        SetD41RecHedAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD41Header()
    {
        return _D41Header;
    }
    
    // Standard Setter
    public void SetD41Header(string value)
    {
        _D41Header = value;
    }
    
    // Get<>AsString()
    public string GetD41HeaderAsString()
    {
        return _D41Header.PadRight(21);
    }
    
    // Set<>AsString()
    public void SetD41HeaderAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41Header = value;
    }
    
    // Standard Getter
    public string GetFiller26()
    {
        return _Filler26;
    }
    
    // Standard Setter
    public void SetFiller26(string value)
    {
        _Filler26 = value;
    }
    
    // Get<>AsString()
    public string GetFiller26AsString()
    {
        return _Filler26.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller26AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler26 = value;
    }
    
    // Standard Getter
    public string GetD41DateX()
    {
        return _D41DateX;
    }
    
    // Standard Setter
    public void SetD41DateX(string value)
    {
        _D41DateX = value;
    }
    
    // Get<>AsString()
    public string GetD41DateXAsString()
    {
        return _D41DateX.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD41DateXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41DateX = value;
    }
    
    // Standard Getter
    public D41Date GetD41Date()
    {
        return _D41Date;
    }
    
    // Standard Setter
    public void SetD41Date(D41Date value)
    {
        _D41Date = value;
    }
    
    // Get<>AsString()
    public string GetD41DateAsString()
    {
        return _D41Date != null ? _D41Date.GetD41DateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD41DateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D41Date == null)
        {
            _D41Date = new D41Date();
        }
        _D41Date.SetD41DateAsString(value);
    }
    
    // Standard Getter
    public string GetFiller27()
    {
        return _Filler27;
    }
    
    // Standard Setter
    public void SetFiller27(string value)
    {
        _Filler27 = value;
    }
    
    // Get<>AsString()
    public string GetFiller27AsString()
    {
        return _Filler27.PadRight(95);
    }
    
    // Set<>AsString()
    public void SetFiller27AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler27 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD41Date(string value)
    {
        _D41Date.SetD41DateAsString(value);
    }
    // Nested Class: D41Date
    public class D41Date
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D41DateYy, is_external=, is_static_class=False, static_prefix=
        private int _D41DateYy =0;
        
        
        
        
        // [DEBUG] Field: D41DateMm, is_external=, is_static_class=False, static_prefix=
        private int _D41DateMm =0;
        
        
        
        
        // [DEBUG] Field: D41DateDd, is_external=, is_static_class=False, static_prefix=
        private int _D41DateDd =0;
        
        
        
        
    public D41Date() {}
    
    public D41Date(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD41DateYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD41DateMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD41DateDd(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD41DateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D41DateYy.ToString().PadLeft(2, '0'));
        result.Append(_D41DateMm.ToString().PadLeft(2, '0'));
        result.Append(_D41DateDd.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetD41DateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41DateYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41DateMm(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD41DateDd(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD41DateYy()
    {
        return _D41DateYy;
    }
    
    // Standard Setter
    public void SetD41DateYy(int value)
    {
        _D41DateYy = value;
    }
    
    // Get<>AsString()
    public string GetD41DateYyAsString()
    {
        return _D41DateYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD41DateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41DateYy = parsed;
    }
    
    // Standard Getter
    public int GetD41DateMm()
    {
        return _D41DateMm;
    }
    
    // Standard Setter
    public void SetD41DateMm(int value)
    {
        _D41DateMm = value;
    }
    
    // Get<>AsString()
    public string GetD41DateMmAsString()
    {
        return _D41DateMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD41DateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41DateMm = parsed;
    }
    
    // Standard Getter
    public int GetD41DateDd()
    {
        return _D41DateDd;
    }
    
    // Standard Setter
    public void SetD41DateDd(int value)
    {
        _D41DateDd = value;
    }
    
    // Get<>AsString()
    public string GetD41DateDdAsString()
    {
        return _D41DateDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD41DateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D41DateDd = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}