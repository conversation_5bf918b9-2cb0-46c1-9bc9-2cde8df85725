using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D109File Data Structure

public class D109File
{
    private static int _size = 12;
    // [DEBUG] Class: D109File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler312, is_external=, is_static_class=False, static_prefix=
    private string _Filler312 ="$";
    
    
    
    
    // [DEBUG] Field: D109UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D109UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler313, is_external=, is_static_class=False, static_prefix=
    private string _Filler313 ="ERR";
    
    
    
    
    // [DEBUG] Field: Filler314, is_external=, is_static_class=False, static_prefix=
    private string _Filler314 =".TXT";
    
    
    
    
    
    // Serialization methods
    public string GetD109FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler312.PadRight(1));
        result.Append(_D109UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler313.PadRight(3));
        result.Append(_Filler314.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD109FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller312(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD109UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetFiller313(extracted);
        }
        offset += 3;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller314(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD109FileAsString();
    }
    // Set<>String Override function
    public void SetD109File(string value)
    {
        SetD109FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller312()
    {
        return _Filler312;
    }
    
    // Standard Setter
    public void SetFiller312(string value)
    {
        _Filler312 = value;
    }
    
    // Get<>AsString()
    public string GetFiller312AsString()
    {
        return _Filler312.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller312AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler312 = value;
    }
    
    // Standard Getter
    public int GetD109UserNo()
    {
        return _D109UserNo;
    }
    
    // Standard Setter
    public void SetD109UserNo(int value)
    {
        _D109UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD109UserNoAsString()
    {
        return _D109UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD109UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D109UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller313()
    {
        return _Filler313;
    }
    
    // Standard Setter
    public void SetFiller313(string value)
    {
        _Filler313 = value;
    }
    
    // Get<>AsString()
    public string GetFiller313AsString()
    {
        return _Filler313.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetFiller313AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler313 = value;
    }
    
    // Standard Getter
    public string GetFiller314()
    {
        return _Filler314;
    }
    
    // Standard Setter
    public void SetFiller314(string value)
    {
        _Filler314 = value;
    }
    
    // Get<>AsString()
    public string GetFiller314AsString()
    {
        return _Filler314.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller314AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler314 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}