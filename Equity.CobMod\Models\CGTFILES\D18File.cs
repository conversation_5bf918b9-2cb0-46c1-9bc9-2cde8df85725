using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D18File Data Structure

public class D18File
{
    private static int _size = 12;
    // [DEBUG] Class: D18File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler169, is_external=, is_static_class=False, static_prefix=
    private string _Filler169 ="$";
    
    
    
    
    // [DEBUG] Field: D18UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D18UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler170, is_external=, is_static_class=False, static_prefix=
    private string _Filler170 ="R4";
    
    
    
    
    // [DEBUG] Field: D18ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D18ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler171, is_external=, is_static_class=False, static_prefix=
    private string _Filler171 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD18FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler169.PadRight(1));
        result.Append(_D18UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler170.PadRight(2));
        result.Append(_D18ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler171.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD18FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller169(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD18UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller170(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD18ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller171(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD18FileAsString();
    }
    // Set<>String Override function
    public void SetD18File(string value)
    {
        SetD18FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller169()
    {
        return _Filler169;
    }
    
    // Standard Setter
    public void SetFiller169(string value)
    {
        _Filler169 = value;
    }
    
    // Get<>AsString()
    public string GetFiller169AsString()
    {
        return _Filler169.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller169AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler169 = value;
    }
    
    // Standard Getter
    public int GetD18UserNo()
    {
        return _D18UserNo;
    }
    
    // Standard Setter
    public void SetD18UserNo(int value)
    {
        _D18UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD18UserNoAsString()
    {
        return _D18UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD18UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D18UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller170()
    {
        return _Filler170;
    }
    
    // Standard Setter
    public void SetFiller170(string value)
    {
        _Filler170 = value;
    }
    
    // Get<>AsString()
    public string GetFiller170AsString()
    {
        return _Filler170.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller170AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler170 = value;
    }
    
    // Standard Getter
    public int GetD18ReportNo()
    {
        return _D18ReportNo;
    }
    
    // Standard Setter
    public void SetD18ReportNo(int value)
    {
        _D18ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD18ReportNoAsString()
    {
        return _D18ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD18ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D18ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller171()
    {
        return _Filler171;
    }
    
    // Standard Setter
    public void SetFiller171(string value)
    {
        _Filler171 = value;
    }
    
    // Get<>AsString()
    public string GetFiller171AsString()
    {
        return _Filler171.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller171AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler171 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}