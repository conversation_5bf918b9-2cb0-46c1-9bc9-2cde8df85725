using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsDate Data Structure

public class WsDate
{
    private static int _size = 6;
    // [DEBUG] Class: WsDate, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsYy, is_external=, is_static_class=False, static_prefix=
    private int _WsYy =0;
    
    
    
    
    // [DEBUG] Field: WsMm, is_external=, is_static_class=False, static_prefix=
    private int _WsMm =0;
    
    
    
    
    // [DEBUG] Field: WsDd, is_external=, is_static_class=False, static_prefix=
    private int _WsDd =0;
    
    
    
    
    
    // Serialization methods
    public string GetWsDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsYy.ToString().PadLeft(2, '0'));
        result.Append(_WsMm.ToString().PadLeft(2, '0'));
        result.Append(_WsDd.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWsDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMm(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsDd(parsedInt);
        }
        offset += 2;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsDateAsString();
    }
    // Set<>String Override function
    public void SetWsDate(string value)
    {
        SetWsDateAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWsYy()
    {
        return _WsYy;
    }
    
    // Standard Setter
    public void SetWsYy(int value)
    {
        _WsYy = value;
    }
    
    // Get<>AsString()
    public string GetWsYyAsString()
    {
        return _WsYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsYy = parsed;
    }
    
    // Standard Getter
    public int GetWsMm()
    {
        return _WsMm;
    }
    
    // Standard Setter
    public void SetWsMm(int value)
    {
        _WsMm = value;
    }
    
    // Get<>AsString()
    public string GetWsMmAsString()
    {
        return _WsMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMm = parsed;
    }
    
    // Standard Getter
    public int GetWsDd()
    {
        return _WsDd;
    }
    
    // Standard Setter
    public void SetWsDd(int value)
    {
        _WsDd = value;
    }
    
    // Get<>AsString()
    public string GetWsDdAsString()
    {
        return _WsDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsDd = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}