using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsReportItems Data Structure

public class WsReportItems
{
    private static int _size = 2356;
    // [DEBUG] Class: WsReportItems, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: GlHeading1, is_external=, is_static_class=False, static_prefix=
    private GlHeading1 _GlHeading1 = new GlHeading1();
    
    
    
    
    // [DEBUG] Field: GlHeading2, is_external=, is_static_class=False, static_prefix=
    private GlHeading2 _GlHeading2 = new GlHeading2();
    
    
    
    
    // [DEBUG] Field: GlHeading3, is_external=, is_static_class=False, static_prefix=
    private GlHeading3 _GlHeading3 = new GlHeading3();
    
    
    
    
    // [DEBUG] Field: GlHeading4, is_external=, is_static_class=False, static_prefix=
    private GlHeading4 _GlHeading4 = new GlHeading4();
    
    
    
    
    // [DEBUG] Field: GlHeading5, is_external=, is_static_class=False, static_prefix=
    private GlHeading5 _GlHeading5 = new GlHeading5();
    
    
    
    
    // [DEBUG] Field: GlSectionHeading1A, is_external=, is_static_class=False, static_prefix=
    private GlSectionHeading1A _GlSectionHeading1A = new GlSectionHeading1A();
    
    
    
    
    // [DEBUG] Field: GlSectionHeading1B, is_external=, is_static_class=False, static_prefix=
    private GlSectionHeading1B _GlSectionHeading1B = new GlSectionHeading1B();
    
    
    
    
    // [DEBUG] Field: GlSectionHeading2A, is_external=, is_static_class=False, static_prefix=
    private GlSectionHeading2A _GlSectionHeading2A = new GlSectionHeading2A();
    
    
    
    
    // [DEBUG] Field: GlSectionHeading2B, is_external=, is_static_class=False, static_prefix=
    private GlSectionHeading2B _GlSectionHeading2B = new GlSectionHeading2B();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter1A, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter1A _GlSectionFooter1A = new GlSectionFooter1A();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter1B, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter1B _GlSectionFooter1B = new GlSectionFooter1B();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter1C, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter1C _GlSectionFooter1C = new GlSectionFooter1C();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter2A, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter2A _GlSectionFooter2A = new GlSectionFooter2A();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter2B, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter2B _GlSectionFooter2B = new GlSectionFooter2B();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter2C, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter2C _GlSectionFooter2C = new GlSectionFooter2C();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter3A, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter3A _GlSectionFooter3A = new GlSectionFooter3A();
    
    
    
    
    // [DEBUG] Field: GlSectionFooter3B, is_external=, is_static_class=False, static_prefix=
    private GlSectionFooter3B _GlSectionFooter3B = new GlSectionFooter3B();
    
    
    
    
    // [DEBUG] Field: GlDetailLine, is_external=, is_static_class=False, static_prefix=
    private GlDetailLine _GlDetailLine = new GlDetailLine();
    
    
    
    
    // [DEBUG] Field: GlPreviousFundCode, is_external=, is_static_class=False, static_prefix=
    private string _GlPreviousFundCode =" ";
    
    
    
    
    // [DEBUG] Field: GlPreviousSectionType, is_external=, is_static_class=False, static_prefix=
    private string _GlPreviousSectionType =" ";
    
    
    
    
    // [DEBUG] Field: GlLineCount, is_external=, is_static_class=False, static_prefix=
    private int _GlLineCount =0;
    
    
    
    
    // [DEBUG] Field: GlPageCount, is_external=, is_static_class=False, static_prefix=
    private int _GlPageCount =0;
    
    
    
    
    // [DEBUG] Field: GlSub, is_external=, is_static_class=False, static_prefix=
    private int _GlSub =0;
    
    
    
    
    // [DEBUG] Field: GlAdvancingX, is_external=, is_static_class=False, static_prefix=
    private string _GlAdvancingX ="";
    
    
    
    
    // [DEBUG] Field: GlAdvancing, is_external=, is_static_class=False, static_prefix=
    private int _GlAdvancing =0;
    
    
    
    
    // [DEBUG] Field: GlTotalHolding, is_external=, is_static_class=False, static_prefix=
    private decimal _GlTotalHolding =0M;
    
    
    
    
    // [DEBUG] Field: GlTotalProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _GlTotalProceeds =0M;
    
    
    
    
    // [DEBUG] Field: GlTotalCost, is_external=, is_static_class=False, static_prefix=
    private decimal _GlTotalCost =0M;
    
    
    
    
    // [DEBUG] Field: GlTotalGain, is_external=, is_static_class=False, static_prefix=
    private decimal _GlTotalGain =0M;
    
    
    
    
    // [DEBUG] Field: GlGrandTotalGain, is_external=, is_static_class=False, static_prefix=
    private decimal _GlGrandTotalGain =0M;
    
    
    
    
    // [DEBUG] Field: WsIndexedCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIndexedCost =0M;
    
    
    
    
    // [DEBUG] Field: WD95RecordPart, is_external=, is_static_class=False, static_prefix=
    private string _WD95RecordPart ="";
    
    
    
    
    // [DEBUG] Field: WsProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WsProceeds =0M;
    
    
    
    
    // [DEBUG] Field: WsCgtCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WsCgtCost =0M;
    
    
    
    
    // [DEBUG] Field: WsCgtGl, is_external=, is_static_class=False, static_prefix=
    private decimal _WsCgtGl =0M;
    
    
    
    
    // [DEBUG] Field: WsIndex, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIndex =0M;
    
    
    
    
    
    // Serialization methods
    public string GetWsReportItemsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_GlHeading1.GetGlHeading1AsString());
        result.Append(_GlHeading2.GetGlHeading2AsString());
        result.Append(_GlHeading3.GetGlHeading3AsString());
        result.Append(_GlHeading4.GetGlHeading4AsString());
        result.Append(_GlHeading5.GetGlHeading5AsString());
        result.Append(_GlSectionHeading1A.GetGlSectionHeading1AAsString());
        result.Append(_GlSectionHeading1B.GetGlSectionHeading1BAsString());
        result.Append(_GlSectionHeading2A.GetGlSectionHeading2AAsString());
        result.Append(_GlSectionHeading2B.GetGlSectionHeading2BAsString());
        result.Append(_GlSectionFooter1A.GetGlSectionFooter1AAsString());
        result.Append(_GlSectionFooter1B.GetGlSectionFooter1BAsString());
        result.Append(_GlSectionFooter1C.GetGlSectionFooter1CAsString());
        result.Append(_GlSectionFooter2A.GetGlSectionFooter2AAsString());
        result.Append(_GlSectionFooter2B.GetGlSectionFooter2BAsString());
        result.Append(_GlSectionFooter2C.GetGlSectionFooter2CAsString());
        result.Append(_GlSectionFooter3A.GetGlSectionFooter3AAsString());
        result.Append(_GlSectionFooter3B.GetGlSectionFooter3BAsString());
        result.Append(_GlDetailLine.GetGlDetailLineAsString());
        result.Append(_GlPreviousFundCode.PadRight(4));
        result.Append(_GlPreviousSectionType.PadRight(1));
        result.Append(_GlLineCount.ToString().PadLeft(4, '0'));
        result.Append(_GlPageCount.ToString().PadLeft(4, '0'));
        result.Append(_GlSub.ToString().PadLeft(4, '0'));
        result.Append(_GlAdvancingX.PadRight(2));
        result.Append(_GlAdvancing.ToString().PadLeft(2, '0'));
        result.Append(_GlTotalHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_GlTotalProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_GlTotalCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_GlTotalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_GlGrandTotalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIndexedCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WD95RecordPart.PadRight(128));
        result.Append(_WsProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsCgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsCgtGl.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIndex.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWsReportItemsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 144 <= data.Length)
        {
            _GlHeading1.SetGlHeading1AsString(data.Substring(offset, 144));
        }
        else
        {
            _GlHeading1.SetGlHeading1AsString(data.Substring(offset));
        }
        offset += 144;
        if (offset + 135 <= data.Length)
        {
            _GlHeading2.SetGlHeading2AsString(data.Substring(offset, 135));
        }
        else
        {
            _GlHeading2.SetGlHeading2AsString(data.Substring(offset));
        }
        offset += 135;
        if (offset + 145 <= data.Length)
        {
            _GlHeading3.SetGlHeading3AsString(data.Substring(offset, 145));
        }
        else
        {
            _GlHeading3.SetGlHeading3AsString(data.Substring(offset));
        }
        offset += 145;
        if (offset + 135 <= data.Length)
        {
            _GlHeading4.SetGlHeading4AsString(data.Substring(offset, 135));
        }
        else
        {
            _GlHeading4.SetGlHeading4AsString(data.Substring(offset));
        }
        offset += 135;
        if (offset + 134 <= data.Length)
        {
            _GlHeading5.SetGlHeading5AsString(data.Substring(offset, 134));
        }
        else
        {
            _GlHeading5.SetGlHeading5AsString(data.Substring(offset));
        }
        offset += 134;
        if (offset + 25 <= data.Length)
        {
            _GlSectionHeading1A.SetGlSectionHeading1AAsString(data.Substring(offset, 25));
        }
        else
        {
            _GlSectionHeading1A.SetGlSectionHeading1AAsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            _GlSectionHeading1B.SetGlSectionHeading1BAsString(data.Substring(offset, 25));
        }
        else
        {
            _GlSectionHeading1B.SetGlSectionHeading1BAsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 104 <= data.Length)
        {
            _GlSectionHeading2A.SetGlSectionHeading2AAsString(data.Substring(offset, 104));
        }
        else
        {
            _GlSectionHeading2A.SetGlSectionHeading2AAsString(data.Substring(offset));
        }
        offset += 104;
        if (offset + 104 <= data.Length)
        {
            _GlSectionHeading2B.SetGlSectionHeading2BAsString(data.Substring(offset, 104));
        }
        else
        {
            _GlSectionHeading2B.SetGlSectionHeading2BAsString(data.Substring(offset));
        }
        offset += 104;
        if (offset + 134 <= data.Length)
        {
            _GlSectionFooter1A.SetGlSectionFooter1AAsString(data.Substring(offset, 134));
        }
        else
        {
            _GlSectionFooter1A.SetGlSectionFooter1AAsString(data.Substring(offset));
        }
        offset += 134;
        if (offset + 129 <= data.Length)
        {
            _GlSectionFooter1B.SetGlSectionFooter1BAsString(data.Substring(offset, 129));
        }
        else
        {
            _GlSectionFooter1B.SetGlSectionFooter1BAsString(data.Substring(offset));
        }
        offset += 129;
        if (offset + 134 <= data.Length)
        {
            _GlSectionFooter1C.SetGlSectionFooter1CAsString(data.Substring(offset, 134));
        }
        else
        {
            _GlSectionFooter1C.SetGlSectionFooter1CAsString(data.Substring(offset));
        }
        offset += 134;
        if (offset + 134 <= data.Length)
        {
            _GlSectionFooter2A.SetGlSectionFooter2AAsString(data.Substring(offset, 134));
        }
        else
        {
            _GlSectionFooter2A.SetGlSectionFooter2AAsString(data.Substring(offset));
        }
        offset += 134;
        if (offset + 132 <= data.Length)
        {
            _GlSectionFooter2B.SetGlSectionFooter2BAsString(data.Substring(offset, 132));
        }
        else
        {
            _GlSectionFooter2B.SetGlSectionFooter2BAsString(data.Substring(offset));
        }
        offset += 132;
        if (offset + 134 <= data.Length)
        {
            _GlSectionFooter2C.SetGlSectionFooter2CAsString(data.Substring(offset, 134));
        }
        else
        {
            _GlSectionFooter2C.SetGlSectionFooter2CAsString(data.Substring(offset));
        }
        offset += 134;
        if (offset + 135 <= data.Length)
        {
            _GlSectionFooter3A.SetGlSectionFooter3AAsString(data.Substring(offset, 135));
        }
        else
        {
            _GlSectionFooter3A.SetGlSectionFooter3AAsString(data.Substring(offset));
        }
        offset += 135;
        if (offset + 132 <= data.Length)
        {
            _GlSectionFooter3B.SetGlSectionFooter3BAsString(data.Substring(offset, 132));
        }
        else
        {
            _GlSectionFooter3B.SetGlSectionFooter3BAsString(data.Substring(offset));
        }
        offset += 132;
        if (offset + 66 <= data.Length)
        {
            _GlDetailLine.SetGlDetailLineAsString(data.Substring(offset, 66));
        }
        else
        {
            _GlDetailLine.SetGlDetailLineAsString(data.Substring(offset));
        }
        offset += 66;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetGlPreviousFundCode(extracted);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetGlPreviousSectionType(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetGlLineCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetGlPageCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetGlSub(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetGlAdvancingX(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetGlAdvancing(parsedInt);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetGlTotalHolding(parsedDec);
        }
        offset += 14;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetGlTotalProceeds(parsedDec);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetGlTotalCost(parsedDec);
        }
        offset += 8;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetGlTotalGain(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetGlGrandTotalGain(parsedDec);
        }
        offset += 14;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIndexedCost(parsedDec);
        }
        offset += 8;
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetWd95RecordPart(extracted);
        }
        offset += 128;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsProceeds(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsCgtCost(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsCgtGl(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIndex(parsedDec);
        }
        offset += 15;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsReportItemsAsString();
    }
    // Set<>String Override function
    public void SetWsReportItems(string value)
    {
        SetWsReportItemsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public GlHeading1 GetGlHeading1()
    {
        return _GlHeading1;
    }
    
    // Standard Setter
    public void SetGlHeading1(GlHeading1 value)
    {
        _GlHeading1 = value;
    }
    
    // Get<>AsString()
    public string GetGlHeading1AsString()
    {
        return _GlHeading1 != null ? _GlHeading1.GetGlHeading1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlHeading1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlHeading1 == null)
        {
            _GlHeading1 = new GlHeading1();
        }
        _GlHeading1.SetGlHeading1AsString(value);
    }
    
    // Standard Getter
    public GlHeading2 GetGlHeading2()
    {
        return _GlHeading2;
    }
    
    // Standard Setter
    public void SetGlHeading2(GlHeading2 value)
    {
        _GlHeading2 = value;
    }
    
    // Get<>AsString()
    public string GetGlHeading2AsString()
    {
        return _GlHeading2 != null ? _GlHeading2.GetGlHeading2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlHeading2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlHeading2 == null)
        {
            _GlHeading2 = new GlHeading2();
        }
        _GlHeading2.SetGlHeading2AsString(value);
    }
    
    // Standard Getter
    public GlHeading3 GetGlHeading3()
    {
        return _GlHeading3;
    }
    
    // Standard Setter
    public void SetGlHeading3(GlHeading3 value)
    {
        _GlHeading3 = value;
    }
    
    // Get<>AsString()
    public string GetGlHeading3AsString()
    {
        return _GlHeading3 != null ? _GlHeading3.GetGlHeading3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlHeading3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlHeading3 == null)
        {
            _GlHeading3 = new GlHeading3();
        }
        _GlHeading3.SetGlHeading3AsString(value);
    }
    
    // Standard Getter
    public GlHeading4 GetGlHeading4()
    {
        return _GlHeading4;
    }
    
    // Standard Setter
    public void SetGlHeading4(GlHeading4 value)
    {
        _GlHeading4 = value;
    }
    
    // Get<>AsString()
    public string GetGlHeading4AsString()
    {
        return _GlHeading4 != null ? _GlHeading4.GetGlHeading4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlHeading4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlHeading4 == null)
        {
            _GlHeading4 = new GlHeading4();
        }
        _GlHeading4.SetGlHeading4AsString(value);
    }
    
    // Standard Getter
    public GlHeading5 GetGlHeading5()
    {
        return _GlHeading5;
    }
    
    // Standard Setter
    public void SetGlHeading5(GlHeading5 value)
    {
        _GlHeading5 = value;
    }
    
    // Get<>AsString()
    public string GetGlHeading5AsString()
    {
        return _GlHeading5 != null ? _GlHeading5.GetGlHeading5AsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlHeading5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlHeading5 == null)
        {
            _GlHeading5 = new GlHeading5();
        }
        _GlHeading5.SetGlHeading5AsString(value);
    }
    
    // Standard Getter
    public GlSectionHeading1A GetGlSectionHeading1A()
    {
        return _GlSectionHeading1A;
    }
    
    // Standard Setter
    public void SetGlSectionHeading1A(GlSectionHeading1A value)
    {
        _GlSectionHeading1A = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionHeading1AAsString()
    {
        return _GlSectionHeading1A != null ? _GlSectionHeading1A.GetGlSectionHeading1AAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionHeading1AAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionHeading1A == null)
        {
            _GlSectionHeading1A = new GlSectionHeading1A();
        }
        _GlSectionHeading1A.SetGlSectionHeading1AAsString(value);
    }
    
    // Standard Getter
    public GlSectionHeading1B GetGlSectionHeading1B()
    {
        return _GlSectionHeading1B;
    }
    
    // Standard Setter
    public void SetGlSectionHeading1B(GlSectionHeading1B value)
    {
        _GlSectionHeading1B = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionHeading1BAsString()
    {
        return _GlSectionHeading1B != null ? _GlSectionHeading1B.GetGlSectionHeading1BAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionHeading1BAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionHeading1B == null)
        {
            _GlSectionHeading1B = new GlSectionHeading1B();
        }
        _GlSectionHeading1B.SetGlSectionHeading1BAsString(value);
    }
    
    // Standard Getter
    public GlSectionHeading2A GetGlSectionHeading2A()
    {
        return _GlSectionHeading2A;
    }
    
    // Standard Setter
    public void SetGlSectionHeading2A(GlSectionHeading2A value)
    {
        _GlSectionHeading2A = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionHeading2AAsString()
    {
        return _GlSectionHeading2A != null ? _GlSectionHeading2A.GetGlSectionHeading2AAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionHeading2AAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionHeading2A == null)
        {
            _GlSectionHeading2A = new GlSectionHeading2A();
        }
        _GlSectionHeading2A.SetGlSectionHeading2AAsString(value);
    }
    
    // Standard Getter
    public GlSectionHeading2B GetGlSectionHeading2B()
    {
        return _GlSectionHeading2B;
    }
    
    // Standard Setter
    public void SetGlSectionHeading2B(GlSectionHeading2B value)
    {
        _GlSectionHeading2B = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionHeading2BAsString()
    {
        return _GlSectionHeading2B != null ? _GlSectionHeading2B.GetGlSectionHeading2BAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionHeading2BAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionHeading2B == null)
        {
            _GlSectionHeading2B = new GlSectionHeading2B();
        }
        _GlSectionHeading2B.SetGlSectionHeading2BAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter1A GetGlSectionFooter1A()
    {
        return _GlSectionFooter1A;
    }
    
    // Standard Setter
    public void SetGlSectionFooter1A(GlSectionFooter1A value)
    {
        _GlSectionFooter1A = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter1AAsString()
    {
        return _GlSectionFooter1A != null ? _GlSectionFooter1A.GetGlSectionFooter1AAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter1AAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter1A == null)
        {
            _GlSectionFooter1A = new GlSectionFooter1A();
        }
        _GlSectionFooter1A.SetGlSectionFooter1AAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter1B GetGlSectionFooter1B()
    {
        return _GlSectionFooter1B;
    }
    
    // Standard Setter
    public void SetGlSectionFooter1B(GlSectionFooter1B value)
    {
        _GlSectionFooter1B = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter1BAsString()
    {
        return _GlSectionFooter1B != null ? _GlSectionFooter1B.GetGlSectionFooter1BAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter1BAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter1B == null)
        {
            _GlSectionFooter1B = new GlSectionFooter1B();
        }
        _GlSectionFooter1B.SetGlSectionFooter1BAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter1C GetGlSectionFooter1C()
    {
        return _GlSectionFooter1C;
    }
    
    // Standard Setter
    public void SetGlSectionFooter1C(GlSectionFooter1C value)
    {
        _GlSectionFooter1C = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter1CAsString()
    {
        return _GlSectionFooter1C != null ? _GlSectionFooter1C.GetGlSectionFooter1CAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter1CAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter1C == null)
        {
            _GlSectionFooter1C = new GlSectionFooter1C();
        }
        _GlSectionFooter1C.SetGlSectionFooter1CAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter2A GetGlSectionFooter2A()
    {
        return _GlSectionFooter2A;
    }
    
    // Standard Setter
    public void SetGlSectionFooter2A(GlSectionFooter2A value)
    {
        _GlSectionFooter2A = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter2AAsString()
    {
        return _GlSectionFooter2A != null ? _GlSectionFooter2A.GetGlSectionFooter2AAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter2AAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter2A == null)
        {
            _GlSectionFooter2A = new GlSectionFooter2A();
        }
        _GlSectionFooter2A.SetGlSectionFooter2AAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter2B GetGlSectionFooter2B()
    {
        return _GlSectionFooter2B;
    }
    
    // Standard Setter
    public void SetGlSectionFooter2B(GlSectionFooter2B value)
    {
        _GlSectionFooter2B = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter2BAsString()
    {
        return _GlSectionFooter2B != null ? _GlSectionFooter2B.GetGlSectionFooter2BAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter2BAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter2B == null)
        {
            _GlSectionFooter2B = new GlSectionFooter2B();
        }
        _GlSectionFooter2B.SetGlSectionFooter2BAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter2C GetGlSectionFooter2C()
    {
        return _GlSectionFooter2C;
    }
    
    // Standard Setter
    public void SetGlSectionFooter2C(GlSectionFooter2C value)
    {
        _GlSectionFooter2C = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter2CAsString()
    {
        return _GlSectionFooter2C != null ? _GlSectionFooter2C.GetGlSectionFooter2CAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter2CAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter2C == null)
        {
            _GlSectionFooter2C = new GlSectionFooter2C();
        }
        _GlSectionFooter2C.SetGlSectionFooter2CAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter3A GetGlSectionFooter3A()
    {
        return _GlSectionFooter3A;
    }
    
    // Standard Setter
    public void SetGlSectionFooter3A(GlSectionFooter3A value)
    {
        _GlSectionFooter3A = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter3AAsString()
    {
        return _GlSectionFooter3A != null ? _GlSectionFooter3A.GetGlSectionFooter3AAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter3AAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter3A == null)
        {
            _GlSectionFooter3A = new GlSectionFooter3A();
        }
        _GlSectionFooter3A.SetGlSectionFooter3AAsString(value);
    }
    
    // Standard Getter
    public GlSectionFooter3B GetGlSectionFooter3B()
    {
        return _GlSectionFooter3B;
    }
    
    // Standard Setter
    public void SetGlSectionFooter3B(GlSectionFooter3B value)
    {
        _GlSectionFooter3B = value;
    }
    
    // Get<>AsString()
    public string GetGlSectionFooter3BAsString()
    {
        return _GlSectionFooter3B != null ? _GlSectionFooter3B.GetGlSectionFooter3BAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlSectionFooter3BAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlSectionFooter3B == null)
        {
            _GlSectionFooter3B = new GlSectionFooter3B();
        }
        _GlSectionFooter3B.SetGlSectionFooter3BAsString(value);
    }
    
    // Standard Getter
    public GlDetailLine GetGlDetailLine()
    {
        return _GlDetailLine;
    }
    
    // Standard Setter
    public void SetGlDetailLine(GlDetailLine value)
    {
        _GlDetailLine = value;
    }
    
    // Get<>AsString()
    public string GetGlDetailLineAsString()
    {
        return _GlDetailLine != null ? _GlDetailLine.GetGlDetailLineAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGlDetailLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GlDetailLine == null)
        {
            _GlDetailLine = new GlDetailLine();
        }
        _GlDetailLine.SetGlDetailLineAsString(value);
    }
    
    // Standard Getter
    public string GetGlPreviousFundCode()
    {
        return _GlPreviousFundCode;
    }
    
    // Standard Setter
    public void SetGlPreviousFundCode(string value)
    {
        _GlPreviousFundCode = value;
    }
    
    // Get<>AsString()
    public string GetGlPreviousFundCodeAsString()
    {
        return _GlPreviousFundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetGlPreviousFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GlPreviousFundCode = value;
    }
    
    // Standard Getter
    public string GetGlPreviousSectionType()
    {
        return _GlPreviousSectionType;
    }
    
    // Standard Setter
    public void SetGlPreviousSectionType(string value)
    {
        _GlPreviousSectionType = value;
    }
    
    // Get<>AsString()
    public string GetGlPreviousSectionTypeAsString()
    {
        return _GlPreviousSectionType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetGlPreviousSectionTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GlPreviousSectionType = value;
    }
    
    // Standard Getter
    public int GetGlLineCount()
    {
        return _GlLineCount;
    }
    
    // Standard Setter
    public void SetGlLineCount(int value)
    {
        _GlLineCount = value;
    }
    
    // Get<>AsString()
    public string GetGlLineCountAsString()
    {
        return _GlLineCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetGlLineCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _GlLineCount = parsed;
    }
    
    // Standard Getter
    public int GetGlPageCount()
    {
        return _GlPageCount;
    }
    
    // Standard Setter
    public void SetGlPageCount(int value)
    {
        _GlPageCount = value;
    }
    
    // Get<>AsString()
    public string GetGlPageCountAsString()
    {
        return _GlPageCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetGlPageCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _GlPageCount = parsed;
    }
    
    // Standard Getter
    public int GetGlSub()
    {
        return _GlSub;
    }
    
    // Standard Setter
    public void SetGlSub(int value)
    {
        _GlSub = value;
    }
    
    // Get<>AsString()
    public string GetGlSubAsString()
    {
        return _GlSub.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetGlSubAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _GlSub = parsed;
    }
    
    // Standard Getter
    public string GetGlAdvancingX()
    {
        return _GlAdvancingX;
    }
    
    // Standard Setter
    public void SetGlAdvancingX(string value)
    {
        _GlAdvancingX = value;
    }
    
    // Get<>AsString()
    public string GetGlAdvancingXAsString()
    {
        return _GlAdvancingX.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetGlAdvancingXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GlAdvancingX = value;
    }
    
    // Standard Getter
    public int GetGlAdvancing()
    {
        return _GlAdvancing;
    }
    
    // Standard Setter
    public void SetGlAdvancing(int value)
    {
        _GlAdvancing = value;
    }
    
    // Get<>AsString()
    public string GetGlAdvancingAsString()
    {
        return _GlAdvancing.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetGlAdvancingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _GlAdvancing = parsed;
    }
    
    // Standard Getter
    public decimal GetGlTotalHolding()
    {
        return _GlTotalHolding;
    }
    
    // Standard Setter
    public void SetGlTotalHolding(decimal value)
    {
        _GlTotalHolding = value;
    }
    
    // Get<>AsString()
    public string GetGlTotalHoldingAsString()
    {
        return _GlTotalHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetGlTotalHoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlTotalHolding = parsed;
    }
    
    // Standard Getter
    public decimal GetGlTotalProceeds()
    {
        return _GlTotalProceeds;
    }
    
    // Standard Setter
    public void SetGlTotalProceeds(decimal value)
    {
        _GlTotalProceeds = value;
    }
    
    // Get<>AsString()
    public string GetGlTotalProceedsAsString()
    {
        return _GlTotalProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetGlTotalProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlTotalProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetGlTotalCost()
    {
        return _GlTotalCost;
    }
    
    // Standard Setter
    public void SetGlTotalCost(decimal value)
    {
        _GlTotalCost = value;
    }
    
    // Get<>AsString()
    public string GetGlTotalCostAsString()
    {
        return _GlTotalCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetGlTotalCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlTotalCost = parsed;
    }
    
    // Standard Getter
    public decimal GetGlTotalGain()
    {
        return _GlTotalGain;
    }
    
    // Standard Setter
    public void SetGlTotalGain(decimal value)
    {
        _GlTotalGain = value;
    }
    
    // Get<>AsString()
    public string GetGlTotalGainAsString()
    {
        return _GlTotalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetGlTotalGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlTotalGain = parsed;
    }
    
    // Standard Getter
    public decimal GetGlGrandTotalGain()
    {
        return _GlGrandTotalGain;
    }
    
    // Standard Setter
    public void SetGlGrandTotalGain(decimal value)
    {
        _GlGrandTotalGain = value;
    }
    
    // Get<>AsString()
    public string GetGlGrandTotalGainAsString()
    {
        return _GlGrandTotalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetGlGrandTotalGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlGrandTotalGain = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIndexedCost()
    {
        return _WsIndexedCost;
    }
    
    // Standard Setter
    public void SetWsIndexedCost(decimal value)
    {
        _WsIndexedCost = value;
    }
    
    // Get<>AsString()
    public string GetWsIndexedCostAsString()
    {
        return _WsIndexedCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIndexedCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIndexedCost = parsed;
    }
    
    // Standard Getter
    public string GetWd95RecordPart()
    {
        return _WD95RecordPart;
    }
    
    // Standard Setter
    public void SetWd95RecordPart(string value)
    {
        _WD95RecordPart = value;
    }
    
    // Get<>AsString()
    public string GetWd95RecordPartAsString()
    {
        return _WD95RecordPart.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetWd95RecordPartAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WD95RecordPart = value;
    }
    
    // Standard Getter
    public decimal GetWsProceeds()
    {
        return _WsProceeds;
    }
    
    // Standard Setter
    public void SetWsProceeds(decimal value)
    {
        _WsProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWsProceedsAsString()
    {
        return _WsProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWsCgtCost()
    {
        return _WsCgtCost;
    }
    
    // Standard Setter
    public void SetWsCgtCost(decimal value)
    {
        _WsCgtCost = value;
    }
    
    // Get<>AsString()
    public string GetWsCgtCostAsString()
    {
        return _WsCgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsCgtCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsCgtCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWsCgtGl()
    {
        return _WsCgtGl;
    }
    
    // Standard Setter
    public void SetWsCgtGl(decimal value)
    {
        _WsCgtGl = value;
    }
    
    // Get<>AsString()
    public string GetWsCgtGlAsString()
    {
        return _WsCgtGl.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsCgtGlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsCgtGl = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIndex()
    {
        return _WsIndex;
    }
    
    // Standard Setter
    public void SetWsIndex(decimal value)
    {
        _WsIndex = value;
    }
    
    // Get<>AsString()
    public string GetWsIndexAsString()
    {
        return _WsIndex.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIndex = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetGlHeading1(string value)
    {
        _GlHeading1.SetGlHeading1AsString(value);
    }
    // Nested Class: GlHeading1
    public class GlHeading1
    {
        private static int _size = 144;
        
        // Fields in the class
        
        
        // [DEBUG] Field: GlCompName, is_external=, is_static_class=False, static_prefix=
        private string _GlCompName ="";
        
        
        
        
        // [DEBUG] Field: Filler38, is_external=, is_static_class=False, static_prefix=
        private string _Filler38 ="";
        
        
        
        
        // [DEBUG] Field: Filler39, is_external=, is_static_class=False, static_prefix=
        private string _Filler39 ="Gain/Loss Summary ";
        
        
        
        
        // [DEBUG] Field: GlScheduleType, is_external=, is_static_class=False, static_prefix=
        private string _GlScheduleType ="";
        
        
        
        
        // [DEBUG] Field: GlFromDate, is_external=, is_static_class=False, static_prefix=
        private string _GlFromDate ="";
        
        
        
        
        // [DEBUG] Field: Filler40, is_external=, is_static_class=False, static_prefix=
        private string _Filler40 =" - ";
        
        
        
        
        // [DEBUG] Field: GlToDate, is_external=, is_static_class=False, static_prefix=
        private string _GlToDate ="";
        
        
        
        
        // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
        private string _Filler41 ="";
        
        
        
        
        // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
        private string _Filler42 ="Page No";
        
        
        
        
        // [DEBUG] Field: GlPageNo, is_external=, is_static_class=False, static_prefix=
        private decimal _GlPageNo =0;
        
        
        
        
    public GlHeading1() {}
    
    public GlHeading1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetGlCompName(data.Substring(offset, 60).Trim());
        offset += 60;
        SetFiller38(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller39(data.Substring(offset, 18).Trim());
        offset += 18;
        SetGlScheduleType(data.Substring(offset, 5).Trim());
        offset += 5;
        SetGlFromDate(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller40(data.Substring(offset, 0).Trim());
        offset += 0;
        SetGlToDate(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller41(data.Substring(offset, 41).Trim());
        offset += 41;
        SetFiller42(data.Substring(offset, 0).Trim());
        offset += 0;
        SetGlPageNo(PackedDecimalConverter.ToDecimal(data.Substring(offset, 4)));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetGlHeading1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_GlCompName.PadRight(60));
        result.Append(_Filler38.PadRight(0));
        result.Append(_Filler39.PadRight(18));
        result.Append(_GlScheduleType.PadRight(5));
        result.Append(_GlFromDate.PadRight(8));
        result.Append(_Filler40.PadRight(0));
        result.Append(_GlToDate.PadRight(8));
        result.Append(_Filler41.PadRight(41));
        result.Append(_Filler42.PadRight(0));
        result.Append(_GlPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetGlHeading1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 60 <= data.Length)
        {
            string extracted = data.Substring(offset, 60).Trim();
            SetGlCompName(extracted);
        }
        offset += 60;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller38(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetFiller39(extracted);
        }
        offset += 18;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetGlScheduleType(extracted);
        }
        offset += 5;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetGlFromDate(extracted);
        }
        offset += 8;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller40(extracted);
        }
        offset += 0;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetGlToDate(extracted);
        }
        offset += 8;
        if (offset + 41 <= data.Length)
        {
            string extracted = data.Substring(offset, 41).Trim();
            SetFiller41(extracted);
        }
        offset += 41;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller42(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetGlPageNo(parsedDec);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetGlCompName()
    {
        return _GlCompName;
    }
    
    // Standard Setter
    public void SetGlCompName(string value)
    {
        _GlCompName = value;
    }
    
    // Get<>AsString()
    public string GetGlCompNameAsString()
    {
        return _GlCompName.PadRight(60);
    }
    
    // Set<>AsString()
    public void SetGlCompNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GlCompName = value;
    }
    
    // Standard Getter
    public string GetFiller38()
    {
        return _Filler38;
    }
    
    // Standard Setter
    public void SetFiller38(string value)
    {
        _Filler38 = value;
    }
    
    // Get<>AsString()
    public string GetFiller38AsString()
    {
        return _Filler38.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller38AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler38 = value;
    }
    
    // Standard Getter
    public string GetFiller39()
    {
        return _Filler39;
    }
    
    // Standard Setter
    public void SetFiller39(string value)
    {
        _Filler39 = value;
    }
    
    // Get<>AsString()
    public string GetFiller39AsString()
    {
        return _Filler39.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetFiller39AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler39 = value;
    }
    
    // Standard Getter
    public string GetGlScheduleType()
    {
        return _GlScheduleType;
    }
    
    // Standard Setter
    public void SetGlScheduleType(string value)
    {
        _GlScheduleType = value;
    }
    
    // Get<>AsString()
    public string GetGlScheduleTypeAsString()
    {
        return _GlScheduleType.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetGlScheduleTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GlScheduleType = value;
    }
    
    // Standard Getter
    public string GetGlFromDate()
    {
        return _GlFromDate;
    }
    
    // Standard Setter
    public void SetGlFromDate(string value)
    {
        _GlFromDate = value;
    }
    
    // Get<>AsString()
    public string GetGlFromDateAsString()
    {
        return _GlFromDate.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetGlFromDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GlFromDate = value;
    }
    
    // Standard Getter
    public string GetFiller40()
    {
        return _Filler40;
    }
    
    // Standard Setter
    public void SetFiller40(string value)
    {
        _Filler40 = value;
    }
    
    // Get<>AsString()
    public string GetFiller40AsString()
    {
        return _Filler40.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller40AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler40 = value;
    }
    
    // Standard Getter
    public string GetGlToDate()
    {
        return _GlToDate;
    }
    
    // Standard Setter
    public void SetGlToDate(string value)
    {
        _GlToDate = value;
    }
    
    // Get<>AsString()
    public string GetGlToDateAsString()
    {
        return _GlToDate.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetGlToDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _GlToDate = value;
    }
    
    // Standard Getter
    public string GetFiller41()
    {
        return _Filler41;
    }
    
    // Standard Setter
    public void SetFiller41(string value)
    {
        _Filler41 = value;
    }
    
    // Get<>AsString()
    public string GetFiller41AsString()
    {
        return _Filler41.PadRight(41);
    }
    
    // Set<>AsString()
    public void SetFiller41AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler41 = value;
    }
    
    // Standard Getter
    public string GetFiller42()
    {
        return _Filler42;
    }
    
    // Standard Setter
    public void SetFiller42(string value)
    {
        _Filler42 = value;
    }
    
    // Get<>AsString()
    public string GetFiller42AsString()
    {
        return _Filler42.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller42AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler42 = value;
    }
    
    // Standard Getter
    public decimal GetGlPageNo()
    {
        return _GlPageNo;
    }
    
    // Standard Setter
    public void SetGlPageNo(decimal value)
    {
        _GlPageNo = value;
    }
    
    // Get<>AsString()
    public string GetGlPageNoAsString()
    {
        return _GlPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetGlPageNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlPageNo = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetGlHeading2(string value)
{
    _GlHeading2.SetGlHeading2AsString(value);
}
// Nested Class: GlHeading2
public class GlHeading2
{
    private static int _size = 135;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler43, is_external=, is_static_class=False, static_prefix=
    private string _Filler43 ="";
    
    
    
    
    // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
    private string _Filler44 ="";
    
    
    
    
    // [DEBUG] Field: Filler45, is_external=, is_static_class=False, static_prefix=
    private string _Filler45 ="";
    
    
    
    
    // [DEBUG] Field: Filler46, is_external=, is_static_class=False, static_prefix=
    private string _Filler46 ="";
    
    
    
    
    // [DEBUG] Field: Filler47, is_external=, is_static_class=False, static_prefix=
    private string _Filler47 ="";
    
    
    
    
    // [DEBUG] Field: Filler48, is_external=, is_static_class=False, static_prefix=
    private string _Filler48 ="";
    
    
    
    
    // [DEBUG] Field: Filler49, is_external=, is_static_class=False, static_prefix=
    private string _Filler49 ="";
    
    
    
    
    // [DEBUG] Field: Filler50, is_external=, is_static_class=False, static_prefix=
    private string _Filler50 ="";
    
    
    
    
    // [DEBUG] Field: Filler51, is_external=, is_static_class=False, static_prefix=
    private string _Filler51 ="";
    
    
    
    
    // [DEBUG] Field: Filler52, is_external=, is_static_class=False, static_prefix=
    private string _Filler52 ="";
    
    
    
    
    // [DEBUG] Field: Filler53, is_external=, is_static_class=False, static_prefix=
    private string _Filler53 ="";
    
    
    
    
    // [DEBUG] Field: Filler54, is_external=, is_static_class=False, static_prefix=
    private string _Filler54 ="";
    
    
    
    
    // [DEBUG] Field: Filler55, is_external=, is_static_class=False, static_prefix=
    private string _Filler55 ="  Apportioned";
    
    
    
    
    // [DEBUG] Field: Filler56, is_external=, is_static_class=False, static_prefix=
    private string _Filler56 ="";
    
    
    
    
    // [DEBUG] Field: Filler57, is_external=, is_static_class=False, static_prefix=
    private string _Filler57 ="";
    
    
    
    
    // [DEBUG] Field: Filler58, is_external=, is_static_class=False, static_prefix=
    private string _Filler58 ="";
    
    
    
    
public GlHeading2() {}

public GlHeading2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller43(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller44(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller45(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller46(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller47(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller48(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller49(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller50(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller51(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller52(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller53(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller54(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller55(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller56(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller57(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller58(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlHeading2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler43.PadRight(0));
    result.Append(_Filler44.PadRight(0));
    result.Append(_Filler45.PadRight(20));
    result.Append(_Filler46.PadRight(0));
    result.Append(_Filler47.PadRight(63));
    result.Append(_Filler48.PadRight(0));
    result.Append(_Filler49.PadRight(0));
    result.Append(_Filler50.PadRight(0));
    result.Append(_Filler51.PadRight(13));
    result.Append(_Filler52.PadRight(0));
    result.Append(_Filler53.PadRight(13));
    result.Append(_Filler54.PadRight(0));
    result.Append(_Filler55.PadRight(13));
    result.Append(_Filler56.PadRight(0));
    result.Append(_Filler57.PadRight(13));
    result.Append(_Filler58.PadRight(0));
    
    return result.ToString();
}

public void SetGlHeading2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller43(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller44(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller45(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller46(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller47(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller48(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller49(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller50(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller51(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller52(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller53(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller54(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller55(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller56(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller57(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller58(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller43()
{
    return _Filler43;
}

// Standard Setter
public void SetFiller43(string value)
{
    _Filler43 = value;
}

// Get<>AsString()
public string GetFiller43AsString()
{
    return _Filler43.PadRight(0);
}

// Set<>AsString()
public void SetFiller43AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler43 = value;
}

// Standard Getter
public string GetFiller44()
{
    return _Filler44;
}

// Standard Setter
public void SetFiller44(string value)
{
    _Filler44 = value;
}

// Get<>AsString()
public string GetFiller44AsString()
{
    return _Filler44.PadRight(0);
}

// Set<>AsString()
public void SetFiller44AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler44 = value;
}

// Standard Getter
public string GetFiller45()
{
    return _Filler45;
}

// Standard Setter
public void SetFiller45(string value)
{
    _Filler45 = value;
}

// Get<>AsString()
public string GetFiller45AsString()
{
    return _Filler45.PadRight(20);
}

// Set<>AsString()
public void SetFiller45AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler45 = value;
}

// Standard Getter
public string GetFiller46()
{
    return _Filler46;
}

// Standard Setter
public void SetFiller46(string value)
{
    _Filler46 = value;
}

// Get<>AsString()
public string GetFiller46AsString()
{
    return _Filler46.PadRight(0);
}

// Set<>AsString()
public void SetFiller46AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler46 = value;
}

// Standard Getter
public string GetFiller47()
{
    return _Filler47;
}

// Standard Setter
public void SetFiller47(string value)
{
    _Filler47 = value;
}

// Get<>AsString()
public string GetFiller47AsString()
{
    return _Filler47.PadRight(63);
}

// Set<>AsString()
public void SetFiller47AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler47 = value;
}

// Standard Getter
public string GetFiller48()
{
    return _Filler48;
}

// Standard Setter
public void SetFiller48(string value)
{
    _Filler48 = value;
}

// Get<>AsString()
public string GetFiller48AsString()
{
    return _Filler48.PadRight(0);
}

// Set<>AsString()
public void SetFiller48AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler48 = value;
}

// Standard Getter
public string GetFiller49()
{
    return _Filler49;
}

// Standard Setter
public void SetFiller49(string value)
{
    _Filler49 = value;
}

// Get<>AsString()
public string GetFiller49AsString()
{
    return _Filler49.PadRight(0);
}

// Set<>AsString()
public void SetFiller49AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler49 = value;
}

// Standard Getter
public string GetFiller50()
{
    return _Filler50;
}

// Standard Setter
public void SetFiller50(string value)
{
    _Filler50 = value;
}

// Get<>AsString()
public string GetFiller50AsString()
{
    return _Filler50.PadRight(0);
}

// Set<>AsString()
public void SetFiller50AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler50 = value;
}

// Standard Getter
public string GetFiller51()
{
    return _Filler51;
}

// Standard Setter
public void SetFiller51(string value)
{
    _Filler51 = value;
}

// Get<>AsString()
public string GetFiller51AsString()
{
    return _Filler51.PadRight(13);
}

// Set<>AsString()
public void SetFiller51AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler51 = value;
}

// Standard Getter
public string GetFiller52()
{
    return _Filler52;
}

// Standard Setter
public void SetFiller52(string value)
{
    _Filler52 = value;
}

// Get<>AsString()
public string GetFiller52AsString()
{
    return _Filler52.PadRight(0);
}

// Set<>AsString()
public void SetFiller52AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler52 = value;
}

// Standard Getter
public string GetFiller53()
{
    return _Filler53;
}

// Standard Setter
public void SetFiller53(string value)
{
    _Filler53 = value;
}

// Get<>AsString()
public string GetFiller53AsString()
{
    return _Filler53.PadRight(13);
}

// Set<>AsString()
public void SetFiller53AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler53 = value;
}

// Standard Getter
public string GetFiller54()
{
    return _Filler54;
}

// Standard Setter
public void SetFiller54(string value)
{
    _Filler54 = value;
}

// Get<>AsString()
public string GetFiller54AsString()
{
    return _Filler54.PadRight(0);
}

// Set<>AsString()
public void SetFiller54AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler54 = value;
}

// Standard Getter
public string GetFiller55()
{
    return _Filler55;
}

// Standard Setter
public void SetFiller55(string value)
{
    _Filler55 = value;
}

// Get<>AsString()
public string GetFiller55AsString()
{
    return _Filler55.PadRight(13);
}

// Set<>AsString()
public void SetFiller55AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler55 = value;
}

// Standard Getter
public string GetFiller56()
{
    return _Filler56;
}

// Standard Setter
public void SetFiller56(string value)
{
    _Filler56 = value;
}

// Get<>AsString()
public string GetFiller56AsString()
{
    return _Filler56.PadRight(0);
}

// Set<>AsString()
public void SetFiller56AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler56 = value;
}

// Standard Getter
public string GetFiller57()
{
    return _Filler57;
}

// Standard Setter
public void SetFiller57(string value)
{
    _Filler57 = value;
}

// Get<>AsString()
public string GetFiller57AsString()
{
    return _Filler57.PadRight(13);
}

// Set<>AsString()
public void SetFiller57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler57 = value;
}

// Standard Getter
public string GetFiller58()
{
    return _Filler58;
}

// Standard Setter
public void SetFiller58(string value)
{
    _Filler58 = value;
}

// Get<>AsString()
public string GetFiller58AsString()
{
    return _Filler58.PadRight(0);
}

// Set<>AsString()
public void SetFiller58AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler58 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlHeading3(string value)
{
    _GlHeading3.SetGlHeading3AsString(value);
}
// Nested Class: GlHeading3
public class GlHeading3
{
    private static int _size = 145;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Glh3CurrencyPrintString, is_external=, is_static_class=False, static_prefix=
    private string _Glh3CurrencyPrintString ="";
    
    
    
    
    // [DEBUG] Field: Filler59, is_external=, is_static_class=False, static_prefix=
    private string _Filler59 ="";
    
    
    
    
    // [DEBUG] Field: Filler60, is_external=, is_static_class=False, static_prefix=
    private string _Filler60 ="";
    
    
    
    
    // [DEBUG] Field: Filler61, is_external=, is_static_class=False, static_prefix=
    private string _Filler61 ="";
    
    
    
    
    // [DEBUG] Field: Filler62, is_external=, is_static_class=False, static_prefix=
    private string _Filler62 ="";
    
    
    
    
    // [DEBUG] Field: Filler63, is_external=, is_static_class=False, static_prefix=
    private string _Filler63 =" RPI ";
    
    
    
    
    // [DEBUG] Field: Filler64, is_external=, is_static_class=False, static_prefix=
    private string _Filler64 ="";
    
    
    
    
    // [DEBUG] Field: Filler65, is_external=, is_static_class=False, static_prefix=
    private string _Filler65 ="";
    
    
    
    
    // [DEBUG] Field: Filler66, is_external=, is_static_class=False, static_prefix=
    private string _Filler66 ="";
    
    
    
    
    // [DEBUG] Field: Filler67, is_external=, is_static_class=False, static_prefix=
    private string _Filler67 ="";
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private string _Filler68 ="";
    
    
    
    
    // [DEBUG] Field: Filler69, is_external=, is_static_class=False, static_prefix=
    private string _Filler69 ="    Cost with";
    
    
    
    
    // [DEBUG] Field: Filler70, is_external=, is_static_class=False, static_prefix=
    private string _Filler70 ="";
    
    
    
    
    // [DEBUG] Field: Filler71, is_external=, is_static_class=False, static_prefix=
    private string _Filler71 ="      Indexed";
    
    
    
    
    // [DEBUG] Field: Filler72, is_external=, is_static_class=False, static_prefix=
    private string _Filler72 ="";
    
    
    
    
public GlHeading3() {}

public GlHeading3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetGlh3CurrencyPrintString(data.Substring(offset, 10).Trim());
    offset += 10;
    SetFiller59(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller60(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller61(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller62(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller63(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller64(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller65(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller66(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller67(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller68(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller69(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller70(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller71(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller72(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlHeading3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Glh3CurrencyPrintString.PadRight(10));
    result.Append(_Filler59.PadRight(20));
    result.Append(_Filler60.PadRight(0));
    result.Append(_Filler61.PadRight(63));
    result.Append(_Filler62.PadRight(0));
    result.Append(_Filler63.PadRight(0));
    result.Append(_Filler64.PadRight(0));
    result.Append(_Filler65.PadRight(13));
    result.Append(_Filler66.PadRight(0));
    result.Append(_Filler67.PadRight(13));
    result.Append(_Filler68.PadRight(0));
    result.Append(_Filler69.PadRight(13));
    result.Append(_Filler70.PadRight(0));
    result.Append(_Filler71.PadRight(13));
    result.Append(_Filler72.PadRight(0));
    
    return result.ToString();
}

public void SetGlHeading3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetGlh3CurrencyPrintString(extracted);
    }
    offset += 10;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller59(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller60(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller61(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller62(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller63(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller64(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller65(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller66(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller67(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller68(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller69(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller70(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller71(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller72(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetGlh3CurrencyPrintString()
{
    return _Glh3CurrencyPrintString;
}

// Standard Setter
public void SetGlh3CurrencyPrintString(string value)
{
    _Glh3CurrencyPrintString = value;
}

// Get<>AsString()
public string GetGlh3CurrencyPrintStringAsString()
{
    return _Glh3CurrencyPrintString.PadRight(10);
}

// Set<>AsString()
public void SetGlh3CurrencyPrintStringAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Glh3CurrencyPrintString = value;
}

// Standard Getter
public string GetFiller59()
{
    return _Filler59;
}

// Standard Setter
public void SetFiller59(string value)
{
    _Filler59 = value;
}

// Get<>AsString()
public string GetFiller59AsString()
{
    return _Filler59.PadRight(20);
}

// Set<>AsString()
public void SetFiller59AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler59 = value;
}

// Standard Getter
public string GetFiller60()
{
    return _Filler60;
}

// Standard Setter
public void SetFiller60(string value)
{
    _Filler60 = value;
}

// Get<>AsString()
public string GetFiller60AsString()
{
    return _Filler60.PadRight(0);
}

// Set<>AsString()
public void SetFiller60AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler60 = value;
}

// Standard Getter
public string GetFiller61()
{
    return _Filler61;
}

// Standard Setter
public void SetFiller61(string value)
{
    _Filler61 = value;
}

// Get<>AsString()
public string GetFiller61AsString()
{
    return _Filler61.PadRight(63);
}

// Set<>AsString()
public void SetFiller61AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler61 = value;
}

// Standard Getter
public string GetFiller62()
{
    return _Filler62;
}

// Standard Setter
public void SetFiller62(string value)
{
    _Filler62 = value;
}

// Get<>AsString()
public string GetFiller62AsString()
{
    return _Filler62.PadRight(0);
}

// Set<>AsString()
public void SetFiller62AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler62 = value;
}

// Standard Getter
public string GetFiller63()
{
    return _Filler63;
}

// Standard Setter
public void SetFiller63(string value)
{
    _Filler63 = value;
}

// Get<>AsString()
public string GetFiller63AsString()
{
    return _Filler63.PadRight(0);
}

// Set<>AsString()
public void SetFiller63AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler63 = value;
}

// Standard Getter
public string GetFiller64()
{
    return _Filler64;
}

// Standard Setter
public void SetFiller64(string value)
{
    _Filler64 = value;
}

// Get<>AsString()
public string GetFiller64AsString()
{
    return _Filler64.PadRight(0);
}

// Set<>AsString()
public void SetFiller64AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler64 = value;
}

// Standard Getter
public string GetFiller65()
{
    return _Filler65;
}

// Standard Setter
public void SetFiller65(string value)
{
    _Filler65 = value;
}

// Get<>AsString()
public string GetFiller65AsString()
{
    return _Filler65.PadRight(13);
}

// Set<>AsString()
public void SetFiller65AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler65 = value;
}

// Standard Getter
public string GetFiller66()
{
    return _Filler66;
}

// Standard Setter
public void SetFiller66(string value)
{
    _Filler66 = value;
}

// Get<>AsString()
public string GetFiller66AsString()
{
    return _Filler66.PadRight(0);
}

// Set<>AsString()
public void SetFiller66AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler66 = value;
}

// Standard Getter
public string GetFiller67()
{
    return _Filler67;
}

// Standard Setter
public void SetFiller67(string value)
{
    _Filler67 = value;
}

// Get<>AsString()
public string GetFiller67AsString()
{
    return _Filler67.PadRight(13);
}

// Set<>AsString()
public void SetFiller67AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler67 = value;
}

// Standard Getter
public string GetFiller68()
{
    return _Filler68;
}

// Standard Setter
public void SetFiller68(string value)
{
    _Filler68 = value;
}

// Get<>AsString()
public string GetFiller68AsString()
{
    return _Filler68.PadRight(0);
}

// Set<>AsString()
public void SetFiller68AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler68 = value;
}

// Standard Getter
public string GetFiller69()
{
    return _Filler69;
}

// Standard Setter
public void SetFiller69(string value)
{
    _Filler69 = value;
}

// Get<>AsString()
public string GetFiller69AsString()
{
    return _Filler69.PadRight(13);
}

// Set<>AsString()
public void SetFiller69AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler69 = value;
}

// Standard Getter
public string GetFiller70()
{
    return _Filler70;
}

// Standard Setter
public void SetFiller70(string value)
{
    _Filler70 = value;
}

// Get<>AsString()
public string GetFiller70AsString()
{
    return _Filler70.PadRight(0);
}

// Set<>AsString()
public void SetFiller70AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler70 = value;
}

// Standard Getter
public string GetFiller71()
{
    return _Filler71;
}

// Standard Setter
public void SetFiller71(string value)
{
    _Filler71 = value;
}

// Get<>AsString()
public string GetFiller71AsString()
{
    return _Filler71.PadRight(13);
}

// Set<>AsString()
public void SetFiller71AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler71 = value;
}

// Standard Getter
public string GetFiller72()
{
    return _Filler72;
}

// Standard Setter
public void SetFiller72(string value)
{
    _Filler72 = value;
}

// Get<>AsString()
public string GetFiller72AsString()
{
    return _Filler72.PadRight(0);
}

// Set<>AsString()
public void SetFiller72AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler72 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlHeading4(string value)
{
    _GlHeading4.SetGlHeading4AsString(value);
}
// Nested Class: GlHeading4
public class GlHeading4
{
    private static int _size = 135;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler73, is_external=, is_static_class=False, static_prefix=
    private string _Filler73 ="Date";
    
    
    
    
    // [DEBUG] Field: Filler74, is_external=, is_static_class=False, static_prefix=
    private string _Filler74 ="";
    
    
    
    
    // [DEBUG] Field: Filler75, is_external=, is_static_class=False, static_prefix=
    private string _Filler75 ="Transaction Type";
    
    
    
    
    // [DEBUG] Field: Filler76, is_external=, is_static_class=False, static_prefix=
    private string _Filler76 ="";
    
    
    
    
    // [DEBUG] Field: Filler77, is_external=, is_static_class=False, static_prefix=
    private string _Filler77 ="Stock Issuer and Description";
    
    
    
    
    // [DEBUG] Field: Filler78, is_external=, is_static_class=False, static_prefix=
    private string _Filler78 ="";
    
    
    
    
    // [DEBUG] Field: Filler79, is_external=, is_static_class=False, static_prefix=
    private string _Filler79 ="Basis";
    
    
    
    
    // [DEBUG] Field: Filler80, is_external=, is_static_class=False, static_prefix=
    private string _Filler80 ="";
    
    
    
    
    // [DEBUG] Field: Filler81, is_external=, is_static_class=False, static_prefix=
    private string _Filler81 ="     Holding";
    
    
    
    
    // [DEBUG] Field: Filler82, is_external=, is_static_class=False, static_prefix=
    private string _Filler82 ="";
    
    
    
    
    // [DEBUG] Field: Filler83, is_external=, is_static_class=False, static_prefix=
    private string _Filler83 ="     Proceeds";
    
    
    
    
    // [DEBUG] Field: Filler84, is_external=, is_static_class=False, static_prefix=
    private string _Filler84 ="";
    
    
    
    
    // [DEBUG] Field: Filler85, is_external=, is_static_class=False, static_prefix=
    private string _Filler85 ="   Indexation";
    
    
    
    
    // [DEBUG] Field: Filler86, is_external=, is_static_class=False, static_prefix=
    private string _Filler86 ="";
    
    
    
    
    // [DEBUG] Field: Filler87, is_external=, is_static_class=False, static_prefix=
    private string _Filler87 ="    Gain/Loss";
    
    
    
    
    // [DEBUG] Field: Filler88, is_external=, is_static_class=False, static_prefix=
    private string _Filler88 ="";
    
    
    
    
public GlHeading4() {}

public GlHeading4(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller73(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller74(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller75(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller76(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller77(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller78(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller79(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller80(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller81(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller82(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller83(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller84(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller85(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller86(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller87(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller88(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlHeading4AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler73.PadRight(0));
    result.Append(_Filler74.PadRight(0));
    result.Append(_Filler75.PadRight(20));
    result.Append(_Filler76.PadRight(0));
    result.Append(_Filler77.PadRight(63));
    result.Append(_Filler78.PadRight(0));
    result.Append(_Filler79.PadRight(0));
    result.Append(_Filler80.PadRight(0));
    result.Append(_Filler81.PadRight(13));
    result.Append(_Filler82.PadRight(0));
    result.Append(_Filler83.PadRight(13));
    result.Append(_Filler84.PadRight(0));
    result.Append(_Filler85.PadRight(13));
    result.Append(_Filler86.PadRight(0));
    result.Append(_Filler87.PadRight(13));
    result.Append(_Filler88.PadRight(0));
    
    return result.ToString();
}

public void SetGlHeading4AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller73(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller74(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller75(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller76(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller77(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller78(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller79(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller80(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller81(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller82(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller83(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller84(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller85(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller86(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller87(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller88(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller73()
{
    return _Filler73;
}

// Standard Setter
public void SetFiller73(string value)
{
    _Filler73 = value;
}

// Get<>AsString()
public string GetFiller73AsString()
{
    return _Filler73.PadRight(0);
}

// Set<>AsString()
public void SetFiller73AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler73 = value;
}

// Standard Getter
public string GetFiller74()
{
    return _Filler74;
}

// Standard Setter
public void SetFiller74(string value)
{
    _Filler74 = value;
}

// Get<>AsString()
public string GetFiller74AsString()
{
    return _Filler74.PadRight(0);
}

// Set<>AsString()
public void SetFiller74AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler74 = value;
}

// Standard Getter
public string GetFiller75()
{
    return _Filler75;
}

// Standard Setter
public void SetFiller75(string value)
{
    _Filler75 = value;
}

// Get<>AsString()
public string GetFiller75AsString()
{
    return _Filler75.PadRight(20);
}

// Set<>AsString()
public void SetFiller75AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler75 = value;
}

// Standard Getter
public string GetFiller76()
{
    return _Filler76;
}

// Standard Setter
public void SetFiller76(string value)
{
    _Filler76 = value;
}

// Get<>AsString()
public string GetFiller76AsString()
{
    return _Filler76.PadRight(0);
}

// Set<>AsString()
public void SetFiller76AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler76 = value;
}

// Standard Getter
public string GetFiller77()
{
    return _Filler77;
}

// Standard Setter
public void SetFiller77(string value)
{
    _Filler77 = value;
}

// Get<>AsString()
public string GetFiller77AsString()
{
    return _Filler77.PadRight(63);
}

// Set<>AsString()
public void SetFiller77AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler77 = value;
}

// Standard Getter
public string GetFiller78()
{
    return _Filler78;
}

// Standard Setter
public void SetFiller78(string value)
{
    _Filler78 = value;
}

// Get<>AsString()
public string GetFiller78AsString()
{
    return _Filler78.PadRight(0);
}

// Set<>AsString()
public void SetFiller78AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler78 = value;
}

// Standard Getter
public string GetFiller79()
{
    return _Filler79;
}

// Standard Setter
public void SetFiller79(string value)
{
    _Filler79 = value;
}

// Get<>AsString()
public string GetFiller79AsString()
{
    return _Filler79.PadRight(0);
}

// Set<>AsString()
public void SetFiller79AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler79 = value;
}

// Standard Getter
public string GetFiller80()
{
    return _Filler80;
}

// Standard Setter
public void SetFiller80(string value)
{
    _Filler80 = value;
}

// Get<>AsString()
public string GetFiller80AsString()
{
    return _Filler80.PadRight(0);
}

// Set<>AsString()
public void SetFiller80AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler80 = value;
}

// Standard Getter
public string GetFiller81()
{
    return _Filler81;
}

// Standard Setter
public void SetFiller81(string value)
{
    _Filler81 = value;
}

// Get<>AsString()
public string GetFiller81AsString()
{
    return _Filler81.PadRight(13);
}

// Set<>AsString()
public void SetFiller81AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler81 = value;
}

// Standard Getter
public string GetFiller82()
{
    return _Filler82;
}

// Standard Setter
public void SetFiller82(string value)
{
    _Filler82 = value;
}

// Get<>AsString()
public string GetFiller82AsString()
{
    return _Filler82.PadRight(0);
}

// Set<>AsString()
public void SetFiller82AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler82 = value;
}

// Standard Getter
public string GetFiller83()
{
    return _Filler83;
}

// Standard Setter
public void SetFiller83(string value)
{
    _Filler83 = value;
}

// Get<>AsString()
public string GetFiller83AsString()
{
    return _Filler83.PadRight(13);
}

// Set<>AsString()
public void SetFiller83AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler83 = value;
}

// Standard Getter
public string GetFiller84()
{
    return _Filler84;
}

// Standard Setter
public void SetFiller84(string value)
{
    _Filler84 = value;
}

// Get<>AsString()
public string GetFiller84AsString()
{
    return _Filler84.PadRight(0);
}

// Set<>AsString()
public void SetFiller84AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler84 = value;
}

// Standard Getter
public string GetFiller85()
{
    return _Filler85;
}

// Standard Setter
public void SetFiller85(string value)
{
    _Filler85 = value;
}

// Get<>AsString()
public string GetFiller85AsString()
{
    return _Filler85.PadRight(13);
}

// Set<>AsString()
public void SetFiller85AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler85 = value;
}

// Standard Getter
public string GetFiller86()
{
    return _Filler86;
}

// Standard Setter
public void SetFiller86(string value)
{
    _Filler86 = value;
}

// Get<>AsString()
public string GetFiller86AsString()
{
    return _Filler86.PadRight(0);
}

// Set<>AsString()
public void SetFiller86AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler86 = value;
}

// Standard Getter
public string GetFiller87()
{
    return _Filler87;
}

// Standard Setter
public void SetFiller87(string value)
{
    _Filler87 = value;
}

// Get<>AsString()
public string GetFiller87AsString()
{
    return _Filler87.PadRight(13);
}

// Set<>AsString()
public void SetFiller87AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler87 = value;
}

// Standard Getter
public string GetFiller88()
{
    return _Filler88;
}

// Standard Setter
public void SetFiller88(string value)
{
    _Filler88 = value;
}

// Get<>AsString()
public string GetFiller88AsString()
{
    return _Filler88.PadRight(0);
}

// Set<>AsString()
public void SetFiller88AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler88 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlHeading5(string value)
{
    _GlHeading5.SetGlHeading5AsString(value);
}
// Nested Class: GlHeading5
public class GlHeading5
{
    private static int _size = 134;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler89, is_external=, is_static_class=False, static_prefix=
    private string _Filler89 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler90, is_external=, is_static_class=False, static_prefix=
    private string _Filler90 ="";
    
    
    
    
    // [DEBUG] Field: Filler91, is_external=, is_static_class=False, static_prefix=
    private string _Filler91 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler92, is_external=, is_static_class=False, static_prefix=
    private string _Filler92 ="";
    
    
    
    
    // [DEBUG] Field: Filler93, is_external=, is_static_class=False, static_prefix=
    private string _Filler93 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler94, is_external=, is_static_class=False, static_prefix=
    private string _Filler94 ="";
    
    
    
    
    // [DEBUG] Field: Filler95, is_external=, is_static_class=False, static_prefix=
    private string _Filler95 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler96, is_external=, is_static_class=False, static_prefix=
    private string _Filler96 ="";
    
    
    
    
    // [DEBUG] Field: Filler97, is_external=, is_static_class=False, static_prefix=
    private string _Filler97 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler98, is_external=, is_static_class=False, static_prefix=
    private string _Filler98 ="";
    
    
    
    
    // [DEBUG] Field: Filler99, is_external=, is_static_class=False, static_prefix=
    private string _Filler99 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler100, is_external=, is_static_class=False, static_prefix=
    private string _Filler100 ="";
    
    
    
    
    // [DEBUG] Field: Filler101, is_external=, is_static_class=False, static_prefix=
    private string _Filler101 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler102, is_external=, is_static_class=False, static_prefix=
    private string _Filler102 ="";
    
    
    
    
    // [DEBUG] Field: Filler103, is_external=, is_static_class=False, static_prefix=
    private string _Filler103 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler104, is_external=, is_static_class=False, static_prefix=
    private string _Filler104 ="";
    
    
    
    
public GlHeading5() {}

public GlHeading5(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller89(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller90(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller91(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller92(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller93(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller94(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller95(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller96(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller97(data.Substring(offset, 12).Trim());
    offset += 12;
    SetFiller98(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller99(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller100(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller101(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller102(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller103(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller104(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlHeading5AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler89.PadRight(0));
    result.Append(_Filler90.PadRight(0));
    result.Append(_Filler91.PadRight(20));
    result.Append(_Filler92.PadRight(0));
    result.Append(_Filler93.PadRight(63));
    result.Append(_Filler94.PadRight(0));
    result.Append(_Filler95.PadRight(0));
    result.Append(_Filler96.PadRight(0));
    result.Append(_Filler97.PadRight(12));
    result.Append(_Filler98.PadRight(0));
    result.Append(_Filler99.PadRight(13));
    result.Append(_Filler100.PadRight(0));
    result.Append(_Filler101.PadRight(13));
    result.Append(_Filler102.PadRight(0));
    result.Append(_Filler103.PadRight(13));
    result.Append(_Filler104.PadRight(0));
    
    return result.ToString();
}

public void SetGlHeading5AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller89(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller90(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller91(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller92(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller93(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller94(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller95(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller96(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller97(extracted);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller98(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller99(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller100(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller101(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller102(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller103(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller104(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller89()
{
    return _Filler89;
}

// Standard Setter
public void SetFiller89(string value)
{
    _Filler89 = value;
}

// Get<>AsString()
public string GetFiller89AsString()
{
    return _Filler89.PadRight(0);
}

// Set<>AsString()
public void SetFiller89AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler89 = value;
}

// Standard Getter
public string GetFiller90()
{
    return _Filler90;
}

// Standard Setter
public void SetFiller90(string value)
{
    _Filler90 = value;
}

// Get<>AsString()
public string GetFiller90AsString()
{
    return _Filler90.PadRight(0);
}

// Set<>AsString()
public void SetFiller90AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler90 = value;
}

// Standard Getter
public string GetFiller91()
{
    return _Filler91;
}

// Standard Setter
public void SetFiller91(string value)
{
    _Filler91 = value;
}

// Get<>AsString()
public string GetFiller91AsString()
{
    return _Filler91.PadRight(20);
}

// Set<>AsString()
public void SetFiller91AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler91 = value;
}

// Standard Getter
public string GetFiller92()
{
    return _Filler92;
}

// Standard Setter
public void SetFiller92(string value)
{
    _Filler92 = value;
}

// Get<>AsString()
public string GetFiller92AsString()
{
    return _Filler92.PadRight(0);
}

// Set<>AsString()
public void SetFiller92AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler92 = value;
}

// Standard Getter
public string GetFiller93()
{
    return _Filler93;
}

// Standard Setter
public void SetFiller93(string value)
{
    _Filler93 = value;
}

// Get<>AsString()
public string GetFiller93AsString()
{
    return _Filler93.PadRight(63);
}

// Set<>AsString()
public void SetFiller93AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler93 = value;
}

// Standard Getter
public string GetFiller94()
{
    return _Filler94;
}

// Standard Setter
public void SetFiller94(string value)
{
    _Filler94 = value;
}

// Get<>AsString()
public string GetFiller94AsString()
{
    return _Filler94.PadRight(0);
}

// Set<>AsString()
public void SetFiller94AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler94 = value;
}

// Standard Getter
public string GetFiller95()
{
    return _Filler95;
}

// Standard Setter
public void SetFiller95(string value)
{
    _Filler95 = value;
}

// Get<>AsString()
public string GetFiller95AsString()
{
    return _Filler95.PadRight(0);
}

// Set<>AsString()
public void SetFiller95AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler95 = value;
}

// Standard Getter
public string GetFiller96()
{
    return _Filler96;
}

// Standard Setter
public void SetFiller96(string value)
{
    _Filler96 = value;
}

// Get<>AsString()
public string GetFiller96AsString()
{
    return _Filler96.PadRight(0);
}

// Set<>AsString()
public void SetFiller96AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler96 = value;
}

// Standard Getter
public string GetFiller97()
{
    return _Filler97;
}

// Standard Setter
public void SetFiller97(string value)
{
    _Filler97 = value;
}

// Get<>AsString()
public string GetFiller97AsString()
{
    return _Filler97.PadRight(12);
}

// Set<>AsString()
public void SetFiller97AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler97 = value;
}

// Standard Getter
public string GetFiller98()
{
    return _Filler98;
}

// Standard Setter
public void SetFiller98(string value)
{
    _Filler98 = value;
}

// Get<>AsString()
public string GetFiller98AsString()
{
    return _Filler98.PadRight(0);
}

// Set<>AsString()
public void SetFiller98AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler98 = value;
}

// Standard Getter
public string GetFiller99()
{
    return _Filler99;
}

// Standard Setter
public void SetFiller99(string value)
{
    _Filler99 = value;
}

// Get<>AsString()
public string GetFiller99AsString()
{
    return _Filler99.PadRight(13);
}

// Set<>AsString()
public void SetFiller99AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler99 = value;
}

// Standard Getter
public string GetFiller100()
{
    return _Filler100;
}

// Standard Setter
public void SetFiller100(string value)
{
    _Filler100 = value;
}

// Get<>AsString()
public string GetFiller100AsString()
{
    return _Filler100.PadRight(0);
}

// Set<>AsString()
public void SetFiller100AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler100 = value;
}

// Standard Getter
public string GetFiller101()
{
    return _Filler101;
}

// Standard Setter
public void SetFiller101(string value)
{
    _Filler101 = value;
}

// Get<>AsString()
public string GetFiller101AsString()
{
    return _Filler101.PadRight(13);
}

// Set<>AsString()
public void SetFiller101AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler101 = value;
}

// Standard Getter
public string GetFiller102()
{
    return _Filler102;
}

// Standard Setter
public void SetFiller102(string value)
{
    _Filler102 = value;
}

// Get<>AsString()
public string GetFiller102AsString()
{
    return _Filler102.PadRight(0);
}

// Set<>AsString()
public void SetFiller102AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler102 = value;
}

// Standard Getter
public string GetFiller103()
{
    return _Filler103;
}

// Standard Setter
public void SetFiller103(string value)
{
    _Filler103 = value;
}

// Get<>AsString()
public string GetFiller103AsString()
{
    return _Filler103.PadRight(13);
}

// Set<>AsString()
public void SetFiller103AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler103 = value;
}

// Standard Getter
public string GetFiller104()
{
    return _Filler104;
}

// Standard Setter
public void SetFiller104(string value)
{
    _Filler104 = value;
}

// Get<>AsString()
public string GetFiller104AsString()
{
    return _Filler104.PadRight(0);
}

// Set<>AsString()
public void SetFiller104AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler104 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionHeading1A(string value)
{
    _GlSectionHeading1A.SetGlSectionHeading1AAsString(value);
}
// Nested Class: GlSectionHeading1A
public class GlSectionHeading1A
{
    private static int _size = 25;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler105, is_external=, is_static_class=False, static_prefix=
    private string _Filler105 ="Realised Gains and Losses";
    
    
    
    
public GlSectionHeading1A() {}

public GlSectionHeading1A(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller105(data.Substring(offset, 25).Trim());
    offset += 25;
    
}

// Serialization methods
public string GetGlSectionHeading1AAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler105.PadRight(25));
    
    return result.ToString();
}

public void SetGlSectionHeading1AAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 25 <= data.Length)
    {
        string extracted = data.Substring(offset, 25).Trim();
        SetFiller105(extracted);
    }
    offset += 25;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller105()
{
    return _Filler105;
}

// Standard Setter
public void SetFiller105(string value)
{
    _Filler105 = value;
}

// Get<>AsString()
public string GetFiller105AsString()
{
    return _Filler105.PadRight(25);
}

// Set<>AsString()
public void SetFiller105AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler105 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionHeading1B(string value)
{
    _GlSectionHeading1B.SetGlSectionHeading1BAsString(value);
}
// Nested Class: GlSectionHeading1B
public class GlSectionHeading1B
{
    private static int _size = 25;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler106, is_external=, is_static_class=False, static_prefix=
    private string _Filler106 ="ALL'-'";
    
    
    
    
public GlSectionHeading1B() {}

public GlSectionHeading1B(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller106(data.Substring(offset, 25).Trim());
    offset += 25;
    
}

// Serialization methods
public string GetGlSectionHeading1BAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler106.PadRight(25));
    
    return result.ToString();
}

public void SetGlSectionHeading1BAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 25 <= data.Length)
    {
        string extracted = data.Substring(offset, 25).Trim();
        SetFiller106(extracted);
    }
    offset += 25;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller106()
{
    return _Filler106;
}

// Standard Setter
public void SetFiller106(string value)
{
    _Filler106 = value;
}

// Get<>AsString()
public string GetFiller106AsString()
{
    return _Filler106.PadRight(25);
}

// Set<>AsString()
public void SetFiller106AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler106 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionHeading2A(string value)
{
    _GlSectionHeading2A.SetGlSectionHeading2AAsString(value);
}
// Nested Class: GlSectionHeading2A
public class GlSectionHeading2A
{
    private static int _size = 104;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler107, is_external=, is_static_class=False, static_prefix=
    private string _Filler107 ="Recommended Transactions";
    
    
    
    
    // [DEBUG] Field: Filler108, is_external=, is_static_class=False, static_prefix=
    private string _Filler108 ="  Sale Price";
    
    
    
    
public GlSectionHeading2A() {}

public GlSectionHeading2A(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller107(data.Substring(offset, 104).Trim());
    offset += 104;
    SetFiller108(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionHeading2AAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler107.PadRight(104));
    result.Append(_Filler108.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionHeading2AAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 104 <= data.Length)
    {
        string extracted = data.Substring(offset, 104).Trim();
        SetFiller107(extracted);
    }
    offset += 104;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller108(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller107()
{
    return _Filler107;
}

// Standard Setter
public void SetFiller107(string value)
{
    _Filler107 = value;
}

// Get<>AsString()
public string GetFiller107AsString()
{
    return _Filler107.PadRight(104);
}

// Set<>AsString()
public void SetFiller107AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler107 = value;
}

// Standard Getter
public string GetFiller108()
{
    return _Filler108;
}

// Standard Setter
public void SetFiller108(string value)
{
    _Filler108 = value;
}

// Get<>AsString()
public string GetFiller108AsString()
{
    return _Filler108.PadRight(0);
}

// Set<>AsString()
public void SetFiller108AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler108 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionHeading2B(string value)
{
    _GlSectionHeading2B.SetGlSectionHeading2BAsString(value);
}
// Nested Class: GlSectionHeading2B
public class GlSectionHeading2B
{
    private static int _size = 104;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler109, is_external=, is_static_class=False, static_prefix=
    private string _Filler109 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler110, is_external=, is_static_class=False, static_prefix=
    private string _Filler110 ="ALL'-'";
    
    
    
    
public GlSectionHeading2B() {}

public GlSectionHeading2B(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller109(data.Substring(offset, 104).Trim());
    offset += 104;
    SetFiller110(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionHeading2BAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler109.PadRight(104));
    result.Append(_Filler110.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionHeading2BAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 104 <= data.Length)
    {
        string extracted = data.Substring(offset, 104).Trim();
        SetFiller109(extracted);
    }
    offset += 104;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller110(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller109()
{
    return _Filler109;
}

// Standard Setter
public void SetFiller109(string value)
{
    _Filler109 = value;
}

// Get<>AsString()
public string GetFiller109AsString()
{
    return _Filler109.PadRight(104);
}

// Set<>AsString()
public void SetFiller109AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler109 = value;
}

// Standard Getter
public string GetFiller110()
{
    return _Filler110;
}

// Standard Setter
public void SetFiller110(string value)
{
    _Filler110 = value;
}

// Get<>AsString()
public string GetFiller110AsString()
{
    return _Filler110.PadRight(0);
}

// Set<>AsString()
public void SetFiller110AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler110 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter1A(string value)
{
    _GlSectionFooter1A.SetGlSectionFooter1AAsString(value);
}
// Nested Class: GlSectionFooter1A
public class GlSectionFooter1A
{
    private static int _size = 134;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler111, is_external=, is_static_class=False, static_prefix=
    private string _Filler111 ="";
    
    
    
    
    // [DEBUG] Field: Filler112, is_external=, is_static_class=False, static_prefix=
    private string _Filler112 ="";
    
    
    
    
    // [DEBUG] Field: Filler113, is_external=, is_static_class=False, static_prefix=
    private string _Filler113 ="";
    
    
    
    
    // [DEBUG] Field: Filler114, is_external=, is_static_class=False, static_prefix=
    private string _Filler114 ="";
    
    
    
    
    // [DEBUG] Field: Filler115, is_external=, is_static_class=False, static_prefix=
    private string _Filler115 ="";
    
    
    
    
    // [DEBUG] Field: Filler116, is_external=, is_static_class=False, static_prefix=
    private string _Filler116 ="";
    
    
    
    
    // [DEBUG] Field: Filler117, is_external=, is_static_class=False, static_prefix=
    private string _Filler117 ="";
    
    
    
    
    // [DEBUG] Field: Filler118, is_external=, is_static_class=False, static_prefix=
    private string _Filler118 ="";
    
    
    
    
    // [DEBUG] Field: Filler119, is_external=, is_static_class=False, static_prefix=
    private string _Filler119 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler120, is_external=, is_static_class=False, static_prefix=
    private string _Filler120 ="";
    
    
    
    
    // [DEBUG] Field: Filler121, is_external=, is_static_class=False, static_prefix=
    private string _Filler121 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler122, is_external=, is_static_class=False, static_prefix=
    private string _Filler122 ="";
    
    
    
    
    // [DEBUG] Field: Filler123, is_external=, is_static_class=False, static_prefix=
    private string _Filler123 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler124, is_external=, is_static_class=False, static_prefix=
    private string _Filler124 ="";
    
    
    
    
    // [DEBUG] Field: Filler125, is_external=, is_static_class=False, static_prefix=
    private string _Filler125 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler126, is_external=, is_static_class=False, static_prefix=
    private string _Filler126 ="";
    
    
    
    
public GlSectionFooter1A() {}

public GlSectionFooter1A(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller111(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller112(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller113(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller114(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller115(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller116(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller117(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller118(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller119(data.Substring(offset, 12).Trim());
    offset += 12;
    SetFiller120(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller121(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller122(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller123(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller124(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller125(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller126(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter1AAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler111.PadRight(0));
    result.Append(_Filler112.PadRight(0));
    result.Append(_Filler113.PadRight(20));
    result.Append(_Filler114.PadRight(0));
    result.Append(_Filler115.PadRight(63));
    result.Append(_Filler116.PadRight(0));
    result.Append(_Filler117.PadRight(0));
    result.Append(_Filler118.PadRight(0));
    result.Append(_Filler119.PadRight(12));
    result.Append(_Filler120.PadRight(0));
    result.Append(_Filler121.PadRight(13));
    result.Append(_Filler122.PadRight(0));
    result.Append(_Filler123.PadRight(13));
    result.Append(_Filler124.PadRight(0));
    result.Append(_Filler125.PadRight(13));
    result.Append(_Filler126.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter1AAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller111(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller112(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller113(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller114(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller115(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller116(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller117(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller118(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller119(extracted);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller120(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller121(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller122(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller123(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller124(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller125(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller126(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller111()
{
    return _Filler111;
}

// Standard Setter
public void SetFiller111(string value)
{
    _Filler111 = value;
}

// Get<>AsString()
public string GetFiller111AsString()
{
    return _Filler111.PadRight(0);
}

// Set<>AsString()
public void SetFiller111AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler111 = value;
}

// Standard Getter
public string GetFiller112()
{
    return _Filler112;
}

// Standard Setter
public void SetFiller112(string value)
{
    _Filler112 = value;
}

// Get<>AsString()
public string GetFiller112AsString()
{
    return _Filler112.PadRight(0);
}

// Set<>AsString()
public void SetFiller112AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler112 = value;
}

// Standard Getter
public string GetFiller113()
{
    return _Filler113;
}

// Standard Setter
public void SetFiller113(string value)
{
    _Filler113 = value;
}

// Get<>AsString()
public string GetFiller113AsString()
{
    return _Filler113.PadRight(20);
}

// Set<>AsString()
public void SetFiller113AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler113 = value;
}

// Standard Getter
public string GetFiller114()
{
    return _Filler114;
}

// Standard Setter
public void SetFiller114(string value)
{
    _Filler114 = value;
}

// Get<>AsString()
public string GetFiller114AsString()
{
    return _Filler114.PadRight(0);
}

// Set<>AsString()
public void SetFiller114AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler114 = value;
}

// Standard Getter
public string GetFiller115()
{
    return _Filler115;
}

// Standard Setter
public void SetFiller115(string value)
{
    _Filler115 = value;
}

// Get<>AsString()
public string GetFiller115AsString()
{
    return _Filler115.PadRight(63);
}

// Set<>AsString()
public void SetFiller115AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler115 = value;
}

// Standard Getter
public string GetFiller116()
{
    return _Filler116;
}

// Standard Setter
public void SetFiller116(string value)
{
    _Filler116 = value;
}

// Get<>AsString()
public string GetFiller116AsString()
{
    return _Filler116.PadRight(0);
}

// Set<>AsString()
public void SetFiller116AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler116 = value;
}

// Standard Getter
public string GetFiller117()
{
    return _Filler117;
}

// Standard Setter
public void SetFiller117(string value)
{
    _Filler117 = value;
}

// Get<>AsString()
public string GetFiller117AsString()
{
    return _Filler117.PadRight(0);
}

// Set<>AsString()
public void SetFiller117AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler117 = value;
}

// Standard Getter
public string GetFiller118()
{
    return _Filler118;
}

// Standard Setter
public void SetFiller118(string value)
{
    _Filler118 = value;
}

// Get<>AsString()
public string GetFiller118AsString()
{
    return _Filler118.PadRight(0);
}

// Set<>AsString()
public void SetFiller118AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler118 = value;
}

// Standard Getter
public string GetFiller119()
{
    return _Filler119;
}

// Standard Setter
public void SetFiller119(string value)
{
    _Filler119 = value;
}

// Get<>AsString()
public string GetFiller119AsString()
{
    return _Filler119.PadRight(12);
}

// Set<>AsString()
public void SetFiller119AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler119 = value;
}

// Standard Getter
public string GetFiller120()
{
    return _Filler120;
}

// Standard Setter
public void SetFiller120(string value)
{
    _Filler120 = value;
}

// Get<>AsString()
public string GetFiller120AsString()
{
    return _Filler120.PadRight(0);
}

// Set<>AsString()
public void SetFiller120AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler120 = value;
}

// Standard Getter
public string GetFiller121()
{
    return _Filler121;
}

// Standard Setter
public void SetFiller121(string value)
{
    _Filler121 = value;
}

// Get<>AsString()
public string GetFiller121AsString()
{
    return _Filler121.PadRight(13);
}

// Set<>AsString()
public void SetFiller121AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler121 = value;
}

// Standard Getter
public string GetFiller122()
{
    return _Filler122;
}

// Standard Setter
public void SetFiller122(string value)
{
    _Filler122 = value;
}

// Get<>AsString()
public string GetFiller122AsString()
{
    return _Filler122.PadRight(0);
}

// Set<>AsString()
public void SetFiller122AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler122 = value;
}

// Standard Getter
public string GetFiller123()
{
    return _Filler123;
}

// Standard Setter
public void SetFiller123(string value)
{
    _Filler123 = value;
}

// Get<>AsString()
public string GetFiller123AsString()
{
    return _Filler123.PadRight(13);
}

// Set<>AsString()
public void SetFiller123AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler123 = value;
}

// Standard Getter
public string GetFiller124()
{
    return _Filler124;
}

// Standard Setter
public void SetFiller124(string value)
{
    _Filler124 = value;
}

// Get<>AsString()
public string GetFiller124AsString()
{
    return _Filler124.PadRight(0);
}

// Set<>AsString()
public void SetFiller124AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler124 = value;
}

// Standard Getter
public string GetFiller125()
{
    return _Filler125;
}

// Standard Setter
public void SetFiller125(string value)
{
    _Filler125 = value;
}

// Get<>AsString()
public string GetFiller125AsString()
{
    return _Filler125.PadRight(13);
}

// Set<>AsString()
public void SetFiller125AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler125 = value;
}

// Standard Getter
public string GetFiller126()
{
    return _Filler126;
}

// Standard Setter
public void SetFiller126(string value)
{
    _Filler126 = value;
}

// Get<>AsString()
public string GetFiller126AsString()
{
    return _Filler126.PadRight(0);
}

// Set<>AsString()
public void SetFiller126AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler126 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter1B(string value)
{
    _GlSectionFooter1B.SetGlSectionFooter1BAsString(value);
}
// Nested Class: GlSectionFooter1B
public class GlSectionFooter1B
{
    private static int _size = 129;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler127, is_external=, is_static_class=False, static_prefix=
    private string _Filler127 ="";
    
    
    
    
    // [DEBUG] Field: Filler128, is_external=, is_static_class=False, static_prefix=
    private string _Filler128 ="";
    
    
    
    
    // [DEBUG] Field: Filler129, is_external=, is_static_class=False, static_prefix=
    private string _Filler129 ="";
    
    
    
    
    // [DEBUG] Field: Filler130, is_external=, is_static_class=False, static_prefix=
    private string _Filler130 ="";
    
    
    
    
    // [DEBUG] Field: Filler131, is_external=, is_static_class=False, static_prefix=
    private string _Filler131 ="";
    
    
    
    
    // [DEBUG] Field: Filler132, is_external=, is_static_class=False, static_prefix=
    private string _Filler132 ="Portfolio Total";
    
    
    
    
    // [DEBUG] Field: GlFooter1Holding, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter1Holding =0;
    
    
    
    
    // [DEBUG] Field: Filler133, is_external=, is_static_class=False, static_prefix=
    private string _Filler133 ="";
    
    
    
    
    // [DEBUG] Field: GlFooter1Proceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter1Proceeds =0;
    
    
    
    
    // [DEBUG] Field: Filler134, is_external=, is_static_class=False, static_prefix=
    private string _Filler134 ="";
    
    
    
    
    // [DEBUG] Field: GlFooter1Cost, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter1Cost =0;
    
    
    
    
    // [DEBUG] Field: Filler135, is_external=, is_static_class=False, static_prefix=
    private string _Filler135 ="";
    
    
    
    
    // [DEBUG] Field: GlFooter1Gain, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter1Gain =0;
    
    
    
    
    // [DEBUG] Field: Filler136, is_external=, is_static_class=False, static_prefix=
    private string _Filler136 ="";
    
    
    
    
public GlSectionFooter1B() {}

public GlSectionFooter1B(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller127(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller128(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller129(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller130(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller131(data.Substring(offset, 53).Trim());
    offset += 53;
    SetFiller132(data.Substring(offset, 17).Trim());
    offset += 17;
    SetGlFooter1Holding(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetFiller133(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlFooter1Proceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller134(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlFooter1Cost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller135(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlFooter1Gain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller136(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter1BAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler127.PadRight(0));
    result.Append(_Filler128.PadRight(0));
    result.Append(_Filler129.PadRight(20));
    result.Append(_Filler130.PadRight(0));
    result.Append(_Filler131.PadRight(53));
    result.Append(_Filler132.PadRight(17));
    result.Append(_GlFooter1Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler133.PadRight(0));
    result.Append(_GlFooter1Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler134.PadRight(0));
    result.Append(_GlFooter1Cost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler135.PadRight(0));
    result.Append(_GlFooter1Gain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler136.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter1BAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller127(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller128(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller129(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller130(extracted);
    }
    offset += 0;
    if (offset + 53 <= data.Length)
    {
        string extracted = data.Substring(offset, 53).Trim();
        SetFiller131(extracted);
    }
    offset += 53;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        SetFiller132(extracted);
    }
    offset += 17;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter1Holding(parsedDec);
    }
    offset += 9;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller133(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter1Proceeds(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller134(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter1Cost(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller135(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter1Gain(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller136(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller127()
{
    return _Filler127;
}

// Standard Setter
public void SetFiller127(string value)
{
    _Filler127 = value;
}

// Get<>AsString()
public string GetFiller127AsString()
{
    return _Filler127.PadRight(0);
}

// Set<>AsString()
public void SetFiller127AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler127 = value;
}

// Standard Getter
public string GetFiller128()
{
    return _Filler128;
}

// Standard Setter
public void SetFiller128(string value)
{
    _Filler128 = value;
}

// Get<>AsString()
public string GetFiller128AsString()
{
    return _Filler128.PadRight(0);
}

// Set<>AsString()
public void SetFiller128AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler128 = value;
}

// Standard Getter
public string GetFiller129()
{
    return _Filler129;
}

// Standard Setter
public void SetFiller129(string value)
{
    _Filler129 = value;
}

// Get<>AsString()
public string GetFiller129AsString()
{
    return _Filler129.PadRight(20);
}

// Set<>AsString()
public void SetFiller129AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler129 = value;
}

// Standard Getter
public string GetFiller130()
{
    return _Filler130;
}

// Standard Setter
public void SetFiller130(string value)
{
    _Filler130 = value;
}

// Get<>AsString()
public string GetFiller130AsString()
{
    return _Filler130.PadRight(0);
}

// Set<>AsString()
public void SetFiller130AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler130 = value;
}

// Standard Getter
public string GetFiller131()
{
    return _Filler131;
}

// Standard Setter
public void SetFiller131(string value)
{
    _Filler131 = value;
}

// Get<>AsString()
public string GetFiller131AsString()
{
    return _Filler131.PadRight(53);
}

// Set<>AsString()
public void SetFiller131AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler131 = value;
}

// Standard Getter
public string GetFiller132()
{
    return _Filler132;
}

// Standard Setter
public void SetFiller132(string value)
{
    _Filler132 = value;
}

// Get<>AsString()
public string GetFiller132AsString()
{
    return _Filler132.PadRight(17);
}

// Set<>AsString()
public void SetFiller132AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler132 = value;
}

// Standard Getter
public decimal GetGlFooter1Holding()
{
    return _GlFooter1Holding;
}

// Standard Setter
public void SetGlFooter1Holding(decimal value)
{
    _GlFooter1Holding = value;
}

// Get<>AsString()
public string GetGlFooter1HoldingAsString()
{
    return _GlFooter1Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter1HoldingAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter1Holding = parsed;
}

// Standard Getter
public string GetFiller133()
{
    return _Filler133;
}

// Standard Setter
public void SetFiller133(string value)
{
    _Filler133 = value;
}

// Get<>AsString()
public string GetFiller133AsString()
{
    return _Filler133.PadRight(0);
}

// Set<>AsString()
public void SetFiller133AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler133 = value;
}

// Standard Getter
public decimal GetGlFooter1Proceeds()
{
    return _GlFooter1Proceeds;
}

// Standard Setter
public void SetGlFooter1Proceeds(decimal value)
{
    _GlFooter1Proceeds = value;
}

// Get<>AsString()
public string GetGlFooter1ProceedsAsString()
{
    return _GlFooter1Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter1ProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter1Proceeds = parsed;
}

// Standard Getter
public string GetFiller134()
{
    return _Filler134;
}

// Standard Setter
public void SetFiller134(string value)
{
    _Filler134 = value;
}

// Get<>AsString()
public string GetFiller134AsString()
{
    return _Filler134.PadRight(0);
}

// Set<>AsString()
public void SetFiller134AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler134 = value;
}

// Standard Getter
public decimal GetGlFooter1Cost()
{
    return _GlFooter1Cost;
}

// Standard Setter
public void SetGlFooter1Cost(decimal value)
{
    _GlFooter1Cost = value;
}

// Get<>AsString()
public string GetGlFooter1CostAsString()
{
    return _GlFooter1Cost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter1CostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter1Cost = parsed;
}

// Standard Getter
public string GetFiller135()
{
    return _Filler135;
}

// Standard Setter
public void SetFiller135(string value)
{
    _Filler135 = value;
}

// Get<>AsString()
public string GetFiller135AsString()
{
    return _Filler135.PadRight(0);
}

// Set<>AsString()
public void SetFiller135AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler135 = value;
}

// Standard Getter
public decimal GetGlFooter1Gain()
{
    return _GlFooter1Gain;
}

// Standard Setter
public void SetGlFooter1Gain(decimal value)
{
    _GlFooter1Gain = value;
}

// Get<>AsString()
public string GetGlFooter1GainAsString()
{
    return _GlFooter1Gain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter1GainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter1Gain = parsed;
}

// Standard Getter
public string GetFiller136()
{
    return _Filler136;
}

// Standard Setter
public void SetFiller136(string value)
{
    _Filler136 = value;
}

// Get<>AsString()
public string GetFiller136AsString()
{
    return _Filler136.PadRight(0);
}

// Set<>AsString()
public void SetFiller136AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler136 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter1C(string value)
{
    _GlSectionFooter1C.SetGlSectionFooter1CAsString(value);
}
// Nested Class: GlSectionFooter1C
public class GlSectionFooter1C
{
    private static int _size = 134;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler137, is_external=, is_static_class=False, static_prefix=
    private string _Filler137 ="";
    
    
    
    
    // [DEBUG] Field: Filler138, is_external=, is_static_class=False, static_prefix=
    private string _Filler138 ="";
    
    
    
    
    // [DEBUG] Field: Filler139, is_external=, is_static_class=False, static_prefix=
    private string _Filler139 ="";
    
    
    
    
    // [DEBUG] Field: Filler140, is_external=, is_static_class=False, static_prefix=
    private string _Filler140 ="";
    
    
    
    
    // [DEBUG] Field: Filler141, is_external=, is_static_class=False, static_prefix=
    private string _Filler141 ="";
    
    
    
    
    // [DEBUG] Field: Filler142, is_external=, is_static_class=False, static_prefix=
    private string _Filler142 ="";
    
    
    
    
    // [DEBUG] Field: Filler143, is_external=, is_static_class=False, static_prefix=
    private string _Filler143 ="";
    
    
    
    
    // [DEBUG] Field: Filler144, is_external=, is_static_class=False, static_prefix=
    private string _Filler144 ="";
    
    
    
    
    // [DEBUG] Field: Filler145, is_external=, is_static_class=False, static_prefix=
    private string _Filler145 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler146, is_external=, is_static_class=False, static_prefix=
    private string _Filler146 ="";
    
    
    
    
    // [DEBUG] Field: Filler147, is_external=, is_static_class=False, static_prefix=
    private string _Filler147 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler148, is_external=, is_static_class=False, static_prefix=
    private string _Filler148 ="";
    
    
    
    
    // [DEBUG] Field: Filler149, is_external=, is_static_class=False, static_prefix=
    private string _Filler149 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler150, is_external=, is_static_class=False, static_prefix=
    private string _Filler150 ="";
    
    
    
    
    // [DEBUG] Field: Filler151, is_external=, is_static_class=False, static_prefix=
    private string _Filler151 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler152, is_external=, is_static_class=False, static_prefix=
    private string _Filler152 ="";
    
    
    
    
public GlSectionFooter1C() {}

public GlSectionFooter1C(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller137(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller138(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller139(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller140(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller141(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller142(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller143(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller144(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller145(data.Substring(offset, 12).Trim());
    offset += 12;
    SetFiller146(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller147(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller148(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller149(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller150(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller151(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller152(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter1CAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler137.PadRight(0));
    result.Append(_Filler138.PadRight(0));
    result.Append(_Filler139.PadRight(20));
    result.Append(_Filler140.PadRight(0));
    result.Append(_Filler141.PadRight(63));
    result.Append(_Filler142.PadRight(0));
    result.Append(_Filler143.PadRight(0));
    result.Append(_Filler144.PadRight(0));
    result.Append(_Filler145.PadRight(12));
    result.Append(_Filler146.PadRight(0));
    result.Append(_Filler147.PadRight(13));
    result.Append(_Filler148.PadRight(0));
    result.Append(_Filler149.PadRight(13));
    result.Append(_Filler150.PadRight(0));
    result.Append(_Filler151.PadRight(13));
    result.Append(_Filler152.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter1CAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller137(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller138(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller139(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller140(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller141(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller142(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller143(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller144(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller145(extracted);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller146(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller147(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller148(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller149(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller150(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller151(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller152(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller137()
{
    return _Filler137;
}

// Standard Setter
public void SetFiller137(string value)
{
    _Filler137 = value;
}

// Get<>AsString()
public string GetFiller137AsString()
{
    return _Filler137.PadRight(0);
}

// Set<>AsString()
public void SetFiller137AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler137 = value;
}

// Standard Getter
public string GetFiller138()
{
    return _Filler138;
}

// Standard Setter
public void SetFiller138(string value)
{
    _Filler138 = value;
}

// Get<>AsString()
public string GetFiller138AsString()
{
    return _Filler138.PadRight(0);
}

// Set<>AsString()
public void SetFiller138AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler138 = value;
}

// Standard Getter
public string GetFiller139()
{
    return _Filler139;
}

// Standard Setter
public void SetFiller139(string value)
{
    _Filler139 = value;
}

// Get<>AsString()
public string GetFiller139AsString()
{
    return _Filler139.PadRight(20);
}

// Set<>AsString()
public void SetFiller139AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler139 = value;
}

// Standard Getter
public string GetFiller140()
{
    return _Filler140;
}

// Standard Setter
public void SetFiller140(string value)
{
    _Filler140 = value;
}

// Get<>AsString()
public string GetFiller140AsString()
{
    return _Filler140.PadRight(0);
}

// Set<>AsString()
public void SetFiller140AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler140 = value;
}

// Standard Getter
public string GetFiller141()
{
    return _Filler141;
}

// Standard Setter
public void SetFiller141(string value)
{
    _Filler141 = value;
}

// Get<>AsString()
public string GetFiller141AsString()
{
    return _Filler141.PadRight(63);
}

// Set<>AsString()
public void SetFiller141AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler141 = value;
}

// Standard Getter
public string GetFiller142()
{
    return _Filler142;
}

// Standard Setter
public void SetFiller142(string value)
{
    _Filler142 = value;
}

// Get<>AsString()
public string GetFiller142AsString()
{
    return _Filler142.PadRight(0);
}

// Set<>AsString()
public void SetFiller142AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler142 = value;
}

// Standard Getter
public string GetFiller143()
{
    return _Filler143;
}

// Standard Setter
public void SetFiller143(string value)
{
    _Filler143 = value;
}

// Get<>AsString()
public string GetFiller143AsString()
{
    return _Filler143.PadRight(0);
}

// Set<>AsString()
public void SetFiller143AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler143 = value;
}

// Standard Getter
public string GetFiller144()
{
    return _Filler144;
}

// Standard Setter
public void SetFiller144(string value)
{
    _Filler144 = value;
}

// Get<>AsString()
public string GetFiller144AsString()
{
    return _Filler144.PadRight(0);
}

// Set<>AsString()
public void SetFiller144AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler144 = value;
}

// Standard Getter
public string GetFiller145()
{
    return _Filler145;
}

// Standard Setter
public void SetFiller145(string value)
{
    _Filler145 = value;
}

// Get<>AsString()
public string GetFiller145AsString()
{
    return _Filler145.PadRight(12);
}

// Set<>AsString()
public void SetFiller145AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler145 = value;
}

// Standard Getter
public string GetFiller146()
{
    return _Filler146;
}

// Standard Setter
public void SetFiller146(string value)
{
    _Filler146 = value;
}

// Get<>AsString()
public string GetFiller146AsString()
{
    return _Filler146.PadRight(0);
}

// Set<>AsString()
public void SetFiller146AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler146 = value;
}

// Standard Getter
public string GetFiller147()
{
    return _Filler147;
}

// Standard Setter
public void SetFiller147(string value)
{
    _Filler147 = value;
}

// Get<>AsString()
public string GetFiller147AsString()
{
    return _Filler147.PadRight(13);
}

// Set<>AsString()
public void SetFiller147AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler147 = value;
}

// Standard Getter
public string GetFiller148()
{
    return _Filler148;
}

// Standard Setter
public void SetFiller148(string value)
{
    _Filler148 = value;
}

// Get<>AsString()
public string GetFiller148AsString()
{
    return _Filler148.PadRight(0);
}

// Set<>AsString()
public void SetFiller148AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler148 = value;
}

// Standard Getter
public string GetFiller149()
{
    return _Filler149;
}

// Standard Setter
public void SetFiller149(string value)
{
    _Filler149 = value;
}

// Get<>AsString()
public string GetFiller149AsString()
{
    return _Filler149.PadRight(13);
}

// Set<>AsString()
public void SetFiller149AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler149 = value;
}

// Standard Getter
public string GetFiller150()
{
    return _Filler150;
}

// Standard Setter
public void SetFiller150(string value)
{
    _Filler150 = value;
}

// Get<>AsString()
public string GetFiller150AsString()
{
    return _Filler150.PadRight(0);
}

// Set<>AsString()
public void SetFiller150AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler150 = value;
}

// Standard Getter
public string GetFiller151()
{
    return _Filler151;
}

// Standard Setter
public void SetFiller151(string value)
{
    _Filler151 = value;
}

// Get<>AsString()
public string GetFiller151AsString()
{
    return _Filler151.PadRight(13);
}

// Set<>AsString()
public void SetFiller151AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler151 = value;
}

// Standard Getter
public string GetFiller152()
{
    return _Filler152;
}

// Standard Setter
public void SetFiller152(string value)
{
    _Filler152 = value;
}

// Get<>AsString()
public string GetFiller152AsString()
{
    return _Filler152.PadRight(0);
}

// Set<>AsString()
public void SetFiller152AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler152 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter2A(string value)
{
    _GlSectionFooter2A.SetGlSectionFooter2AAsString(value);
}
// Nested Class: GlSectionFooter2A
public class GlSectionFooter2A
{
    private static int _size = 134;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler153, is_external=, is_static_class=False, static_prefix=
    private string _Filler153 ="";
    
    
    
    
    // [DEBUG] Field: Filler154, is_external=, is_static_class=False, static_prefix=
    private string _Filler154 ="";
    
    
    
    
    // [DEBUG] Field: Filler155, is_external=, is_static_class=False, static_prefix=
    private string _Filler155 ="";
    
    
    
    
    // [DEBUG] Field: Filler156, is_external=, is_static_class=False, static_prefix=
    private string _Filler156 ="";
    
    
    
    
    // [DEBUG] Field: Filler157, is_external=, is_static_class=False, static_prefix=
    private string _Filler157 ="";
    
    
    
    
    // [DEBUG] Field: Filler158, is_external=, is_static_class=False, static_prefix=
    private string _Filler158 ="";
    
    
    
    
    // [DEBUG] Field: Filler159, is_external=, is_static_class=False, static_prefix=
    private string _Filler159 ="";
    
    
    
    
    // [DEBUG] Field: Filler160, is_external=, is_static_class=False, static_prefix=
    private string _Filler160 ="";
    
    
    
    
    // [DEBUG] Field: Filler161, is_external=, is_static_class=False, static_prefix=
    private string _Filler161 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler162, is_external=, is_static_class=False, static_prefix=
    private string _Filler162 ="";
    
    
    
    
    // [DEBUG] Field: Filler163, is_external=, is_static_class=False, static_prefix=
    private string _Filler163 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler164, is_external=, is_static_class=False, static_prefix=
    private string _Filler164 ="";
    
    
    
    
    // [DEBUG] Field: Filler165, is_external=, is_static_class=False, static_prefix=
    private string _Filler165 ="";
    
    
    
    
    // [DEBUG] Field: Filler166, is_external=, is_static_class=False, static_prefix=
    private string _Filler166 ="";
    
    
    
    
    // [DEBUG] Field: Filler167, is_external=, is_static_class=False, static_prefix=
    private string _Filler167 ="ALL'-'";
    
    
    
    
    // [DEBUG] Field: Filler168, is_external=, is_static_class=False, static_prefix=
    private string _Filler168 ="";
    
    
    
    
public GlSectionFooter2A() {}

public GlSectionFooter2A(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller153(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller154(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller155(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller156(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller157(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller158(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller159(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller160(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller161(data.Substring(offset, 12).Trim());
    offset += 12;
    SetFiller162(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller163(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller164(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller165(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller166(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller167(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller168(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter2AAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler153.PadRight(0));
    result.Append(_Filler154.PadRight(0));
    result.Append(_Filler155.PadRight(20));
    result.Append(_Filler156.PadRight(0));
    result.Append(_Filler157.PadRight(63));
    result.Append(_Filler158.PadRight(0));
    result.Append(_Filler159.PadRight(0));
    result.Append(_Filler160.PadRight(0));
    result.Append(_Filler161.PadRight(12));
    result.Append(_Filler162.PadRight(0));
    result.Append(_Filler163.PadRight(13));
    result.Append(_Filler164.PadRight(0));
    result.Append(_Filler165.PadRight(13));
    result.Append(_Filler166.PadRight(0));
    result.Append(_Filler167.PadRight(13));
    result.Append(_Filler168.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter2AAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller153(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller154(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller155(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller156(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller157(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller158(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller159(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller160(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller161(extracted);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller162(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller163(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller164(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller165(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller166(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller167(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller168(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller153()
{
    return _Filler153;
}

// Standard Setter
public void SetFiller153(string value)
{
    _Filler153 = value;
}

// Get<>AsString()
public string GetFiller153AsString()
{
    return _Filler153.PadRight(0);
}

// Set<>AsString()
public void SetFiller153AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler153 = value;
}

// Standard Getter
public string GetFiller154()
{
    return _Filler154;
}

// Standard Setter
public void SetFiller154(string value)
{
    _Filler154 = value;
}

// Get<>AsString()
public string GetFiller154AsString()
{
    return _Filler154.PadRight(0);
}

// Set<>AsString()
public void SetFiller154AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler154 = value;
}

// Standard Getter
public string GetFiller155()
{
    return _Filler155;
}

// Standard Setter
public void SetFiller155(string value)
{
    _Filler155 = value;
}

// Get<>AsString()
public string GetFiller155AsString()
{
    return _Filler155.PadRight(20);
}

// Set<>AsString()
public void SetFiller155AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler155 = value;
}

// Standard Getter
public string GetFiller156()
{
    return _Filler156;
}

// Standard Setter
public void SetFiller156(string value)
{
    _Filler156 = value;
}

// Get<>AsString()
public string GetFiller156AsString()
{
    return _Filler156.PadRight(0);
}

// Set<>AsString()
public void SetFiller156AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler156 = value;
}

// Standard Getter
public string GetFiller157()
{
    return _Filler157;
}

// Standard Setter
public void SetFiller157(string value)
{
    _Filler157 = value;
}

// Get<>AsString()
public string GetFiller157AsString()
{
    return _Filler157.PadRight(63);
}

// Set<>AsString()
public void SetFiller157AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler157 = value;
}

// Standard Getter
public string GetFiller158()
{
    return _Filler158;
}

// Standard Setter
public void SetFiller158(string value)
{
    _Filler158 = value;
}

// Get<>AsString()
public string GetFiller158AsString()
{
    return _Filler158.PadRight(0);
}

// Set<>AsString()
public void SetFiller158AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler158 = value;
}

// Standard Getter
public string GetFiller159()
{
    return _Filler159;
}

// Standard Setter
public void SetFiller159(string value)
{
    _Filler159 = value;
}

// Get<>AsString()
public string GetFiller159AsString()
{
    return _Filler159.PadRight(0);
}

// Set<>AsString()
public void SetFiller159AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler159 = value;
}

// Standard Getter
public string GetFiller160()
{
    return _Filler160;
}

// Standard Setter
public void SetFiller160(string value)
{
    _Filler160 = value;
}

// Get<>AsString()
public string GetFiller160AsString()
{
    return _Filler160.PadRight(0);
}

// Set<>AsString()
public void SetFiller160AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler160 = value;
}

// Standard Getter
public string GetFiller161()
{
    return _Filler161;
}

// Standard Setter
public void SetFiller161(string value)
{
    _Filler161 = value;
}

// Get<>AsString()
public string GetFiller161AsString()
{
    return _Filler161.PadRight(12);
}

// Set<>AsString()
public void SetFiller161AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler161 = value;
}

// Standard Getter
public string GetFiller162()
{
    return _Filler162;
}

// Standard Setter
public void SetFiller162(string value)
{
    _Filler162 = value;
}

// Get<>AsString()
public string GetFiller162AsString()
{
    return _Filler162.PadRight(0);
}

// Set<>AsString()
public void SetFiller162AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler162 = value;
}

// Standard Getter
public string GetFiller163()
{
    return _Filler163;
}

// Standard Setter
public void SetFiller163(string value)
{
    _Filler163 = value;
}

// Get<>AsString()
public string GetFiller163AsString()
{
    return _Filler163.PadRight(13);
}

// Set<>AsString()
public void SetFiller163AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler163 = value;
}

// Standard Getter
public string GetFiller164()
{
    return _Filler164;
}

// Standard Setter
public void SetFiller164(string value)
{
    _Filler164 = value;
}

// Get<>AsString()
public string GetFiller164AsString()
{
    return _Filler164.PadRight(0);
}

// Set<>AsString()
public void SetFiller164AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler164 = value;
}

// Standard Getter
public string GetFiller165()
{
    return _Filler165;
}

// Standard Setter
public void SetFiller165(string value)
{
    _Filler165 = value;
}

// Get<>AsString()
public string GetFiller165AsString()
{
    return _Filler165.PadRight(13);
}

// Set<>AsString()
public void SetFiller165AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler165 = value;
}

// Standard Getter
public string GetFiller166()
{
    return _Filler166;
}

// Standard Setter
public void SetFiller166(string value)
{
    _Filler166 = value;
}

// Get<>AsString()
public string GetFiller166AsString()
{
    return _Filler166.PadRight(0);
}

// Set<>AsString()
public void SetFiller166AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler166 = value;
}

// Standard Getter
public string GetFiller167()
{
    return _Filler167;
}

// Standard Setter
public void SetFiller167(string value)
{
    _Filler167 = value;
}

// Get<>AsString()
public string GetFiller167AsString()
{
    return _Filler167.PadRight(13);
}

// Set<>AsString()
public void SetFiller167AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler167 = value;
}

// Standard Getter
public string GetFiller168()
{
    return _Filler168;
}

// Standard Setter
public void SetFiller168(string value)
{
    _Filler168 = value;
}

// Get<>AsString()
public string GetFiller168AsString()
{
    return _Filler168.PadRight(0);
}

// Set<>AsString()
public void SetFiller168AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler168 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter2B(string value)
{
    _GlSectionFooter2B.SetGlSectionFooter2BAsString(value);
}
// Nested Class: GlSectionFooter2B
public class GlSectionFooter2B
{
    private static int _size = 132;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler169, is_external=, is_static_class=False, static_prefix=
    private string _Filler169 ="";
    
    
    
    
    // [DEBUG] Field: Filler170, is_external=, is_static_class=False, static_prefix=
    private string _Filler170 ="";
    
    
    
    
    // [DEBUG] Field: Filler171, is_external=, is_static_class=False, static_prefix=
    private string _Filler171 ="";
    
    
    
    
    // [DEBUG] Field: Filler172, is_external=, is_static_class=False, static_prefix=
    private string _Filler172 ="";
    
    
    
    
    // [DEBUG] Field: Filler173, is_external=, is_static_class=False, static_prefix=
    private string _Filler173 ="";
    
    
    
    
    // [DEBUG] Field: Filler174, is_external=, is_static_class=False, static_prefix=
    private string _Filler174 ="Portfolio Total";
    
    
    
    
    // [DEBUG] Field: GlFooter2Holding, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter2Holding =0;
    
    
    
    
    // [DEBUG] Field: Filler175, is_external=, is_static_class=False, static_prefix=
    private string _Filler175 ="";
    
    
    
    
    // [DEBUG] Field: GlFooter2Proceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter2Proceeds =0;
    
    
    
    
    // [DEBUG] Field: Filler176, is_external=, is_static_class=False, static_prefix=
    private string _Filler176 ="";
    
    
    
    
    // [DEBUG] Field: Filler177, is_external=, is_static_class=False, static_prefix=
    private string _Filler177 ="";
    
    
    
    
    // [DEBUG] Field: Filler178, is_external=, is_static_class=False, static_prefix=
    private string _Filler178 ="";
    
    
    
    
    // [DEBUG] Field: GlFooter2Gain, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter2Gain =0;
    
    
    
    
    // [DEBUG] Field: Filler179, is_external=, is_static_class=False, static_prefix=
    private string _Filler179 ="";
    
    
    
    
public GlSectionFooter2B() {}

public GlSectionFooter2B(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller169(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller170(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller171(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller172(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller173(data.Substring(offset, 53).Trim());
    offset += 53;
    SetFiller174(data.Substring(offset, 17).Trim());
    offset += 17;
    SetGlFooter2Holding(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetFiller175(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlFooter2Proceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller176(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller177(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller178(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlFooter2Gain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller179(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter2BAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler169.PadRight(0));
    result.Append(_Filler170.PadRight(0));
    result.Append(_Filler171.PadRight(20));
    result.Append(_Filler172.PadRight(0));
    result.Append(_Filler173.PadRight(53));
    result.Append(_Filler174.PadRight(17));
    result.Append(_GlFooter2Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler175.PadRight(0));
    result.Append(_GlFooter2Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler176.PadRight(0));
    result.Append(_Filler177.PadRight(13));
    result.Append(_Filler178.PadRight(0));
    result.Append(_GlFooter2Gain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler179.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter2BAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller169(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller170(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller171(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller172(extracted);
    }
    offset += 0;
    if (offset + 53 <= data.Length)
    {
        string extracted = data.Substring(offset, 53).Trim();
        SetFiller173(extracted);
    }
    offset += 53;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        SetFiller174(extracted);
    }
    offset += 17;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter2Holding(parsedDec);
    }
    offset += 9;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller175(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter2Proceeds(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller176(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller177(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller178(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter2Gain(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller179(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller169()
{
    return _Filler169;
}

// Standard Setter
public void SetFiller169(string value)
{
    _Filler169 = value;
}

// Get<>AsString()
public string GetFiller169AsString()
{
    return _Filler169.PadRight(0);
}

// Set<>AsString()
public void SetFiller169AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler169 = value;
}

// Standard Getter
public string GetFiller170()
{
    return _Filler170;
}

// Standard Setter
public void SetFiller170(string value)
{
    _Filler170 = value;
}

// Get<>AsString()
public string GetFiller170AsString()
{
    return _Filler170.PadRight(0);
}

// Set<>AsString()
public void SetFiller170AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler170 = value;
}

// Standard Getter
public string GetFiller171()
{
    return _Filler171;
}

// Standard Setter
public void SetFiller171(string value)
{
    _Filler171 = value;
}

// Get<>AsString()
public string GetFiller171AsString()
{
    return _Filler171.PadRight(20);
}

// Set<>AsString()
public void SetFiller171AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler171 = value;
}

// Standard Getter
public string GetFiller172()
{
    return _Filler172;
}

// Standard Setter
public void SetFiller172(string value)
{
    _Filler172 = value;
}

// Get<>AsString()
public string GetFiller172AsString()
{
    return _Filler172.PadRight(0);
}

// Set<>AsString()
public void SetFiller172AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler172 = value;
}

// Standard Getter
public string GetFiller173()
{
    return _Filler173;
}

// Standard Setter
public void SetFiller173(string value)
{
    _Filler173 = value;
}

// Get<>AsString()
public string GetFiller173AsString()
{
    return _Filler173.PadRight(53);
}

// Set<>AsString()
public void SetFiller173AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler173 = value;
}

// Standard Getter
public string GetFiller174()
{
    return _Filler174;
}

// Standard Setter
public void SetFiller174(string value)
{
    _Filler174 = value;
}

// Get<>AsString()
public string GetFiller174AsString()
{
    return _Filler174.PadRight(17);
}

// Set<>AsString()
public void SetFiller174AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler174 = value;
}

// Standard Getter
public decimal GetGlFooter2Holding()
{
    return _GlFooter2Holding;
}

// Standard Setter
public void SetGlFooter2Holding(decimal value)
{
    _GlFooter2Holding = value;
}

// Get<>AsString()
public string GetGlFooter2HoldingAsString()
{
    return _GlFooter2Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter2HoldingAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter2Holding = parsed;
}

// Standard Getter
public string GetFiller175()
{
    return _Filler175;
}

// Standard Setter
public void SetFiller175(string value)
{
    _Filler175 = value;
}

// Get<>AsString()
public string GetFiller175AsString()
{
    return _Filler175.PadRight(0);
}

// Set<>AsString()
public void SetFiller175AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler175 = value;
}

// Standard Getter
public decimal GetGlFooter2Proceeds()
{
    return _GlFooter2Proceeds;
}

// Standard Setter
public void SetGlFooter2Proceeds(decimal value)
{
    _GlFooter2Proceeds = value;
}

// Get<>AsString()
public string GetGlFooter2ProceedsAsString()
{
    return _GlFooter2Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter2ProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter2Proceeds = parsed;
}

// Standard Getter
public string GetFiller176()
{
    return _Filler176;
}

// Standard Setter
public void SetFiller176(string value)
{
    _Filler176 = value;
}

// Get<>AsString()
public string GetFiller176AsString()
{
    return _Filler176.PadRight(0);
}

// Set<>AsString()
public void SetFiller176AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler176 = value;
}

// Standard Getter
public string GetFiller177()
{
    return _Filler177;
}

// Standard Setter
public void SetFiller177(string value)
{
    _Filler177 = value;
}

// Get<>AsString()
public string GetFiller177AsString()
{
    return _Filler177.PadRight(13);
}

// Set<>AsString()
public void SetFiller177AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler177 = value;
}

// Standard Getter
public string GetFiller178()
{
    return _Filler178;
}

// Standard Setter
public void SetFiller178(string value)
{
    _Filler178 = value;
}

// Get<>AsString()
public string GetFiller178AsString()
{
    return _Filler178.PadRight(0);
}

// Set<>AsString()
public void SetFiller178AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler178 = value;
}

// Standard Getter
public decimal GetGlFooter2Gain()
{
    return _GlFooter2Gain;
}

// Standard Setter
public void SetGlFooter2Gain(decimal value)
{
    _GlFooter2Gain = value;
}

// Get<>AsString()
public string GetGlFooter2GainAsString()
{
    return _GlFooter2Gain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter2GainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter2Gain = parsed;
}

// Standard Getter
public string GetFiller179()
{
    return _Filler179;
}

// Standard Setter
public void SetFiller179(string value)
{
    _Filler179 = value;
}

// Get<>AsString()
public string GetFiller179AsString()
{
    return _Filler179.PadRight(0);
}

// Set<>AsString()
public void SetFiller179AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler179 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter2C(string value)
{
    _GlSectionFooter2C.SetGlSectionFooter2CAsString(value);
}
// Nested Class: GlSectionFooter2C
public class GlSectionFooter2C
{
    private static int _size = 134;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler180, is_external=, is_static_class=False, static_prefix=
    private string _Filler180 ="";
    
    
    
    
    // [DEBUG] Field: Filler181, is_external=, is_static_class=False, static_prefix=
    private string _Filler181 ="";
    
    
    
    
    // [DEBUG] Field: Filler182, is_external=, is_static_class=False, static_prefix=
    private string _Filler182 ="";
    
    
    
    
    // [DEBUG] Field: Filler183, is_external=, is_static_class=False, static_prefix=
    private string _Filler183 ="";
    
    
    
    
    // [DEBUG] Field: Filler184, is_external=, is_static_class=False, static_prefix=
    private string _Filler184 ="";
    
    
    
    
    // [DEBUG] Field: Filler185, is_external=, is_static_class=False, static_prefix=
    private string _Filler185 ="";
    
    
    
    
    // [DEBUG] Field: Filler186, is_external=, is_static_class=False, static_prefix=
    private string _Filler186 ="";
    
    
    
    
    // [DEBUG] Field: Filler187, is_external=, is_static_class=False, static_prefix=
    private string _Filler187 ="";
    
    
    
    
    // [DEBUG] Field: Filler188, is_external=, is_static_class=False, static_prefix=
    private string _Filler188 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler189, is_external=, is_static_class=False, static_prefix=
    private string _Filler189 ="";
    
    
    
    
    // [DEBUG] Field: Filler190, is_external=, is_static_class=False, static_prefix=
    private string _Filler190 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler191, is_external=, is_static_class=False, static_prefix=
    private string _Filler191 ="";
    
    
    
    
    // [DEBUG] Field: Filler192, is_external=, is_static_class=False, static_prefix=
    private string _Filler192 ="";
    
    
    
    
    // [DEBUG] Field: Filler193, is_external=, is_static_class=False, static_prefix=
    private string _Filler193 ="";
    
    
    
    
    // [DEBUG] Field: Filler194, is_external=, is_static_class=False, static_prefix=
    private string _Filler194 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler195, is_external=, is_static_class=False, static_prefix=
    private string _Filler195 ="";
    
    
    
    
public GlSectionFooter2C() {}

public GlSectionFooter2C(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller180(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller181(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller182(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller183(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller184(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller185(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller186(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller187(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller188(data.Substring(offset, 12).Trim());
    offset += 12;
    SetFiller189(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller190(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller191(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller192(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller193(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller194(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller195(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter2CAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler180.PadRight(0));
    result.Append(_Filler181.PadRight(0));
    result.Append(_Filler182.PadRight(20));
    result.Append(_Filler183.PadRight(0));
    result.Append(_Filler184.PadRight(63));
    result.Append(_Filler185.PadRight(0));
    result.Append(_Filler186.PadRight(0));
    result.Append(_Filler187.PadRight(0));
    result.Append(_Filler188.PadRight(12));
    result.Append(_Filler189.PadRight(0));
    result.Append(_Filler190.PadRight(13));
    result.Append(_Filler191.PadRight(0));
    result.Append(_Filler192.PadRight(13));
    result.Append(_Filler193.PadRight(0));
    result.Append(_Filler194.PadRight(13));
    result.Append(_Filler195.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter2CAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller180(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller181(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller182(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller183(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller184(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller185(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller186(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller187(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller188(extracted);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller189(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller190(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller191(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller192(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller193(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller194(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller195(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller180()
{
    return _Filler180;
}

// Standard Setter
public void SetFiller180(string value)
{
    _Filler180 = value;
}

// Get<>AsString()
public string GetFiller180AsString()
{
    return _Filler180.PadRight(0);
}

// Set<>AsString()
public void SetFiller180AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler180 = value;
}

// Standard Getter
public string GetFiller181()
{
    return _Filler181;
}

// Standard Setter
public void SetFiller181(string value)
{
    _Filler181 = value;
}

// Get<>AsString()
public string GetFiller181AsString()
{
    return _Filler181.PadRight(0);
}

// Set<>AsString()
public void SetFiller181AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler181 = value;
}

// Standard Getter
public string GetFiller182()
{
    return _Filler182;
}

// Standard Setter
public void SetFiller182(string value)
{
    _Filler182 = value;
}

// Get<>AsString()
public string GetFiller182AsString()
{
    return _Filler182.PadRight(20);
}

// Set<>AsString()
public void SetFiller182AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler182 = value;
}

// Standard Getter
public string GetFiller183()
{
    return _Filler183;
}

// Standard Setter
public void SetFiller183(string value)
{
    _Filler183 = value;
}

// Get<>AsString()
public string GetFiller183AsString()
{
    return _Filler183.PadRight(0);
}

// Set<>AsString()
public void SetFiller183AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler183 = value;
}

// Standard Getter
public string GetFiller184()
{
    return _Filler184;
}

// Standard Setter
public void SetFiller184(string value)
{
    _Filler184 = value;
}

// Get<>AsString()
public string GetFiller184AsString()
{
    return _Filler184.PadRight(63);
}

// Set<>AsString()
public void SetFiller184AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler184 = value;
}

// Standard Getter
public string GetFiller185()
{
    return _Filler185;
}

// Standard Setter
public void SetFiller185(string value)
{
    _Filler185 = value;
}

// Get<>AsString()
public string GetFiller185AsString()
{
    return _Filler185.PadRight(0);
}

// Set<>AsString()
public void SetFiller185AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler185 = value;
}

// Standard Getter
public string GetFiller186()
{
    return _Filler186;
}

// Standard Setter
public void SetFiller186(string value)
{
    _Filler186 = value;
}

// Get<>AsString()
public string GetFiller186AsString()
{
    return _Filler186.PadRight(0);
}

// Set<>AsString()
public void SetFiller186AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler186 = value;
}

// Standard Getter
public string GetFiller187()
{
    return _Filler187;
}

// Standard Setter
public void SetFiller187(string value)
{
    _Filler187 = value;
}

// Get<>AsString()
public string GetFiller187AsString()
{
    return _Filler187.PadRight(0);
}

// Set<>AsString()
public void SetFiller187AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler187 = value;
}

// Standard Getter
public string GetFiller188()
{
    return _Filler188;
}

// Standard Setter
public void SetFiller188(string value)
{
    _Filler188 = value;
}

// Get<>AsString()
public string GetFiller188AsString()
{
    return _Filler188.PadRight(12);
}

// Set<>AsString()
public void SetFiller188AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler188 = value;
}

// Standard Getter
public string GetFiller189()
{
    return _Filler189;
}

// Standard Setter
public void SetFiller189(string value)
{
    _Filler189 = value;
}

// Get<>AsString()
public string GetFiller189AsString()
{
    return _Filler189.PadRight(0);
}

// Set<>AsString()
public void SetFiller189AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler189 = value;
}

// Standard Getter
public string GetFiller190()
{
    return _Filler190;
}

// Standard Setter
public void SetFiller190(string value)
{
    _Filler190 = value;
}

// Get<>AsString()
public string GetFiller190AsString()
{
    return _Filler190.PadRight(13);
}

// Set<>AsString()
public void SetFiller190AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler190 = value;
}

// Standard Getter
public string GetFiller191()
{
    return _Filler191;
}

// Standard Setter
public void SetFiller191(string value)
{
    _Filler191 = value;
}

// Get<>AsString()
public string GetFiller191AsString()
{
    return _Filler191.PadRight(0);
}

// Set<>AsString()
public void SetFiller191AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler191 = value;
}

// Standard Getter
public string GetFiller192()
{
    return _Filler192;
}

// Standard Setter
public void SetFiller192(string value)
{
    _Filler192 = value;
}

// Get<>AsString()
public string GetFiller192AsString()
{
    return _Filler192.PadRight(13);
}

// Set<>AsString()
public void SetFiller192AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler192 = value;
}

// Standard Getter
public string GetFiller193()
{
    return _Filler193;
}

// Standard Setter
public void SetFiller193(string value)
{
    _Filler193 = value;
}

// Get<>AsString()
public string GetFiller193AsString()
{
    return _Filler193.PadRight(0);
}

// Set<>AsString()
public void SetFiller193AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler193 = value;
}

// Standard Getter
public string GetFiller194()
{
    return _Filler194;
}

// Standard Setter
public void SetFiller194(string value)
{
    _Filler194 = value;
}

// Get<>AsString()
public string GetFiller194AsString()
{
    return _Filler194.PadRight(13);
}

// Set<>AsString()
public void SetFiller194AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler194 = value;
}

// Standard Getter
public string GetFiller195()
{
    return _Filler195;
}

// Standard Setter
public void SetFiller195(string value)
{
    _Filler195 = value;
}

// Get<>AsString()
public string GetFiller195AsString()
{
    return _Filler195.PadRight(0);
}

// Set<>AsString()
public void SetFiller195AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler195 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter3A(string value)
{
    _GlSectionFooter3A.SetGlSectionFooter3AAsString(value);
}
// Nested Class: GlSectionFooter3A
public class GlSectionFooter3A
{
    private static int _size = 135;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler196, is_external=, is_static_class=False, static_prefix=
    private string _Filler196 ="";
    
    
    
    
    // [DEBUG] Field: Filler197, is_external=, is_static_class=False, static_prefix=
    private string _Filler197 ="";
    
    
    
    
    // [DEBUG] Field: Filler198, is_external=, is_static_class=False, static_prefix=
    private string _Filler198 ="";
    
    
    
    
    // [DEBUG] Field: Filler199, is_external=, is_static_class=False, static_prefix=
    private string _Filler199 ="";
    
    
    
    
    // [DEBUG] Field: Filler200, is_external=, is_static_class=False, static_prefix=
    private string _Filler200 ="";
    
    
    
    
    // [DEBUG] Field: Filler201, is_external=, is_static_class=False, static_prefix=
    private string _Filler201 ="";
    
    
    
    
    // [DEBUG] Field: Filler202, is_external=, is_static_class=False, static_prefix=
    private string _Filler202 ="";
    
    
    
    
    // [DEBUG] Field: Filler203, is_external=, is_static_class=False, static_prefix=
    private string _Filler203 ="";
    
    
    
    
    // [DEBUG] Field: Filler204, is_external=, is_static_class=False, static_prefix=
    private string _Filler204 ="";
    
    
    
    
    // [DEBUG] Field: Filler205, is_external=, is_static_class=False, static_prefix=
    private string _Filler205 ="";
    
    
    
    
    // [DEBUG] Field: Filler206, is_external=, is_static_class=False, static_prefix=
    private string _Filler206 ="";
    
    
    
    
    // [DEBUG] Field: Filler207, is_external=, is_static_class=False, static_prefix=
    private string _Filler207 ="";
    
    
    
    
    // [DEBUG] Field: Filler208, is_external=, is_static_class=False, static_prefix=
    private string _Filler208 ="";
    
    
    
    
    // [DEBUG] Field: Filler209, is_external=, is_static_class=False, static_prefix=
    private string _Filler209 ="";
    
    
    
    
    // [DEBUG] Field: Filler210, is_external=, is_static_class=False, static_prefix=
    private string _Filler210 ="ALL'='";
    
    
    
    
    // [DEBUG] Field: Filler211, is_external=, is_static_class=False, static_prefix=
    private string _Filler211 ="";
    
    
    
    
public GlSectionFooter3A() {}

public GlSectionFooter3A(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller196(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller197(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller198(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller199(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller200(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller201(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller202(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller203(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller204(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller205(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller206(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller207(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller208(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller209(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller210(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller211(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter3AAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler196.PadRight(0));
    result.Append(_Filler197.PadRight(0));
    result.Append(_Filler198.PadRight(20));
    result.Append(_Filler199.PadRight(0));
    result.Append(_Filler200.PadRight(63));
    result.Append(_Filler201.PadRight(0));
    result.Append(_Filler202.PadRight(0));
    result.Append(_Filler203.PadRight(0));
    result.Append(_Filler204.PadRight(13));
    result.Append(_Filler205.PadRight(0));
    result.Append(_Filler206.PadRight(13));
    result.Append(_Filler207.PadRight(0));
    result.Append(_Filler208.PadRight(13));
    result.Append(_Filler209.PadRight(0));
    result.Append(_Filler210.PadRight(13));
    result.Append(_Filler211.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter3AAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller196(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller197(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller198(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller199(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller200(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller201(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller202(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller203(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller204(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller205(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller206(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller207(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller208(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller209(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller210(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller211(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller196()
{
    return _Filler196;
}

// Standard Setter
public void SetFiller196(string value)
{
    _Filler196 = value;
}

// Get<>AsString()
public string GetFiller196AsString()
{
    return _Filler196.PadRight(0);
}

// Set<>AsString()
public void SetFiller196AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler196 = value;
}

// Standard Getter
public string GetFiller197()
{
    return _Filler197;
}

// Standard Setter
public void SetFiller197(string value)
{
    _Filler197 = value;
}

// Get<>AsString()
public string GetFiller197AsString()
{
    return _Filler197.PadRight(0);
}

// Set<>AsString()
public void SetFiller197AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler197 = value;
}

// Standard Getter
public string GetFiller198()
{
    return _Filler198;
}

// Standard Setter
public void SetFiller198(string value)
{
    _Filler198 = value;
}

// Get<>AsString()
public string GetFiller198AsString()
{
    return _Filler198.PadRight(20);
}

// Set<>AsString()
public void SetFiller198AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler198 = value;
}

// Standard Getter
public string GetFiller199()
{
    return _Filler199;
}

// Standard Setter
public void SetFiller199(string value)
{
    _Filler199 = value;
}

// Get<>AsString()
public string GetFiller199AsString()
{
    return _Filler199.PadRight(0);
}

// Set<>AsString()
public void SetFiller199AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler199 = value;
}

// Standard Getter
public string GetFiller200()
{
    return _Filler200;
}

// Standard Setter
public void SetFiller200(string value)
{
    _Filler200 = value;
}

// Get<>AsString()
public string GetFiller200AsString()
{
    return _Filler200.PadRight(63);
}

// Set<>AsString()
public void SetFiller200AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler200 = value;
}

// Standard Getter
public string GetFiller201()
{
    return _Filler201;
}

// Standard Setter
public void SetFiller201(string value)
{
    _Filler201 = value;
}

// Get<>AsString()
public string GetFiller201AsString()
{
    return _Filler201.PadRight(0);
}

// Set<>AsString()
public void SetFiller201AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler201 = value;
}

// Standard Getter
public string GetFiller202()
{
    return _Filler202;
}

// Standard Setter
public void SetFiller202(string value)
{
    _Filler202 = value;
}

// Get<>AsString()
public string GetFiller202AsString()
{
    return _Filler202.PadRight(0);
}

// Set<>AsString()
public void SetFiller202AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler202 = value;
}

// Standard Getter
public string GetFiller203()
{
    return _Filler203;
}

// Standard Setter
public void SetFiller203(string value)
{
    _Filler203 = value;
}

// Get<>AsString()
public string GetFiller203AsString()
{
    return _Filler203.PadRight(0);
}

// Set<>AsString()
public void SetFiller203AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler203 = value;
}

// Standard Getter
public string GetFiller204()
{
    return _Filler204;
}

// Standard Setter
public void SetFiller204(string value)
{
    _Filler204 = value;
}

// Get<>AsString()
public string GetFiller204AsString()
{
    return _Filler204.PadRight(13);
}

// Set<>AsString()
public void SetFiller204AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler204 = value;
}

// Standard Getter
public string GetFiller205()
{
    return _Filler205;
}

// Standard Setter
public void SetFiller205(string value)
{
    _Filler205 = value;
}

// Get<>AsString()
public string GetFiller205AsString()
{
    return _Filler205.PadRight(0);
}

// Set<>AsString()
public void SetFiller205AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler205 = value;
}

// Standard Getter
public string GetFiller206()
{
    return _Filler206;
}

// Standard Setter
public void SetFiller206(string value)
{
    _Filler206 = value;
}

// Get<>AsString()
public string GetFiller206AsString()
{
    return _Filler206.PadRight(13);
}

// Set<>AsString()
public void SetFiller206AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler206 = value;
}

// Standard Getter
public string GetFiller207()
{
    return _Filler207;
}

// Standard Setter
public void SetFiller207(string value)
{
    _Filler207 = value;
}

// Get<>AsString()
public string GetFiller207AsString()
{
    return _Filler207.PadRight(0);
}

// Set<>AsString()
public void SetFiller207AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler207 = value;
}

// Standard Getter
public string GetFiller208()
{
    return _Filler208;
}

// Standard Setter
public void SetFiller208(string value)
{
    _Filler208 = value;
}

// Get<>AsString()
public string GetFiller208AsString()
{
    return _Filler208.PadRight(13);
}

// Set<>AsString()
public void SetFiller208AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler208 = value;
}

// Standard Getter
public string GetFiller209()
{
    return _Filler209;
}

// Standard Setter
public void SetFiller209(string value)
{
    _Filler209 = value;
}

// Get<>AsString()
public string GetFiller209AsString()
{
    return _Filler209.PadRight(0);
}

// Set<>AsString()
public void SetFiller209AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler209 = value;
}

// Standard Getter
public string GetFiller210()
{
    return _Filler210;
}

// Standard Setter
public void SetFiller210(string value)
{
    _Filler210 = value;
}

// Get<>AsString()
public string GetFiller210AsString()
{
    return _Filler210.PadRight(13);
}

// Set<>AsString()
public void SetFiller210AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler210 = value;
}

// Standard Getter
public string GetFiller211()
{
    return _Filler211;
}

// Standard Setter
public void SetFiller211(string value)
{
    _Filler211 = value;
}

// Get<>AsString()
public string GetFiller211AsString()
{
    return _Filler211.PadRight(0);
}

// Set<>AsString()
public void SetFiller211AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler211 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlSectionFooter3B(string value)
{
    _GlSectionFooter3B.SetGlSectionFooter3BAsString(value);
}
// Nested Class: GlSectionFooter3B
public class GlSectionFooter3B
{
    private static int _size = 132;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler212, is_external=, is_static_class=False, static_prefix=
    private string _Filler212 ="";
    
    
    
    
    // [DEBUG] Field: Filler213, is_external=, is_static_class=False, static_prefix=
    private string _Filler213 ="";
    
    
    
    
    // [DEBUG] Field: Filler214, is_external=, is_static_class=False, static_prefix=
    private string _Filler214 ="";
    
    
    
    
    // [DEBUG] Field: Filler215, is_external=, is_static_class=False, static_prefix=
    private string _Filler215 ="";
    
    
    
    
    // [DEBUG] Field: Filler216, is_external=, is_static_class=False, static_prefix=
    private string _Filler216 ="";
    
    
    
    
    // [DEBUG] Field: Filler217, is_external=, is_static_class=False, static_prefix=
    private string _Filler217 ="";
    
    
    
    
    // [DEBUG] Field: Filler218, is_external=, is_static_class=False, static_prefix=
    private string _Filler218 ="";
    
    
    
    
    // [DEBUG] Field: Filler219, is_external=, is_static_class=False, static_prefix=
    private string _Filler219 ="";
    
    
    
    
    // [DEBUG] Field: Filler220, is_external=, is_static_class=False, static_prefix=
    private string _Filler220 ="";
    
    
    
    
    // [DEBUG] Field: Filler221, is_external=, is_static_class=False, static_prefix=
    private string _Filler221 ="";
    
    
    
    
    // [DEBUG] Field: Filler222, is_external=, is_static_class=False, static_prefix=
    private string _Filler222 ="";
    
    
    
    
    // [DEBUG] Field: Filler223, is_external=, is_static_class=False, static_prefix=
    private string _Filler223 ="";
    
    
    
    
    // [DEBUG] Field: Filler224, is_external=, is_static_class=False, static_prefix=
    private string _Filler224 ="Overall Total";
    
    
    
    
    // [DEBUG] Field: Filler225, is_external=, is_static_class=False, static_prefix=
    private string _Filler225 ="";
    
    
    
    
    // [DEBUG] Field: GlFooter3Gain, is_external=, is_static_class=False, static_prefix=
    private decimal _GlFooter3Gain =0;
    
    
    
    
    // [DEBUG] Field: Filler226, is_external=, is_static_class=False, static_prefix=
    private string _Filler226 ="";
    
    
    
    
public GlSectionFooter3B() {}

public GlSectionFooter3B(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller212(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller213(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller214(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller215(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller216(data.Substring(offset, 63).Trim());
    offset += 63;
    SetFiller217(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller218(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller219(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller220(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller221(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller222(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller223(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller224(data.Substring(offset, 13).Trim());
    offset += 13;
    SetFiller225(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlFooter3Gain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller226(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlSectionFooter3BAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler212.PadRight(0));
    result.Append(_Filler213.PadRight(0));
    result.Append(_Filler214.PadRight(20));
    result.Append(_Filler215.PadRight(0));
    result.Append(_Filler216.PadRight(63));
    result.Append(_Filler217.PadRight(0));
    result.Append(_Filler218.PadRight(0));
    result.Append(_Filler219.PadRight(0));
    result.Append(_Filler220.PadRight(13));
    result.Append(_Filler221.PadRight(0));
    result.Append(_Filler222.PadRight(13));
    result.Append(_Filler223.PadRight(0));
    result.Append(_Filler224.PadRight(13));
    result.Append(_Filler225.PadRight(0));
    result.Append(_GlFooter3Gain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler226.PadRight(0));
    
    return result.ToString();
}

public void SetGlSectionFooter3BAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller212(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller213(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller214(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller215(extracted);
    }
    offset += 0;
    if (offset + 63 <= data.Length)
    {
        string extracted = data.Substring(offset, 63).Trim();
        SetFiller216(extracted);
    }
    offset += 63;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller217(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller218(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller219(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller220(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller221(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller222(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller223(extracted);
    }
    offset += 0;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller224(extracted);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller225(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlFooter3Gain(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller226(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller212()
{
    return _Filler212;
}

// Standard Setter
public void SetFiller212(string value)
{
    _Filler212 = value;
}

// Get<>AsString()
public string GetFiller212AsString()
{
    return _Filler212.PadRight(0);
}

// Set<>AsString()
public void SetFiller212AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler212 = value;
}

// Standard Getter
public string GetFiller213()
{
    return _Filler213;
}

// Standard Setter
public void SetFiller213(string value)
{
    _Filler213 = value;
}

// Get<>AsString()
public string GetFiller213AsString()
{
    return _Filler213.PadRight(0);
}

// Set<>AsString()
public void SetFiller213AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler213 = value;
}

// Standard Getter
public string GetFiller214()
{
    return _Filler214;
}

// Standard Setter
public void SetFiller214(string value)
{
    _Filler214 = value;
}

// Get<>AsString()
public string GetFiller214AsString()
{
    return _Filler214.PadRight(20);
}

// Set<>AsString()
public void SetFiller214AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler214 = value;
}

// Standard Getter
public string GetFiller215()
{
    return _Filler215;
}

// Standard Setter
public void SetFiller215(string value)
{
    _Filler215 = value;
}

// Get<>AsString()
public string GetFiller215AsString()
{
    return _Filler215.PadRight(0);
}

// Set<>AsString()
public void SetFiller215AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler215 = value;
}

// Standard Getter
public string GetFiller216()
{
    return _Filler216;
}

// Standard Setter
public void SetFiller216(string value)
{
    _Filler216 = value;
}

// Get<>AsString()
public string GetFiller216AsString()
{
    return _Filler216.PadRight(63);
}

// Set<>AsString()
public void SetFiller216AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler216 = value;
}

// Standard Getter
public string GetFiller217()
{
    return _Filler217;
}

// Standard Setter
public void SetFiller217(string value)
{
    _Filler217 = value;
}

// Get<>AsString()
public string GetFiller217AsString()
{
    return _Filler217.PadRight(0);
}

// Set<>AsString()
public void SetFiller217AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler217 = value;
}

// Standard Getter
public string GetFiller218()
{
    return _Filler218;
}

// Standard Setter
public void SetFiller218(string value)
{
    _Filler218 = value;
}

// Get<>AsString()
public string GetFiller218AsString()
{
    return _Filler218.PadRight(0);
}

// Set<>AsString()
public void SetFiller218AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler218 = value;
}

// Standard Getter
public string GetFiller219()
{
    return _Filler219;
}

// Standard Setter
public void SetFiller219(string value)
{
    _Filler219 = value;
}

// Get<>AsString()
public string GetFiller219AsString()
{
    return _Filler219.PadRight(0);
}

// Set<>AsString()
public void SetFiller219AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler219 = value;
}

// Standard Getter
public string GetFiller220()
{
    return _Filler220;
}

// Standard Setter
public void SetFiller220(string value)
{
    _Filler220 = value;
}

// Get<>AsString()
public string GetFiller220AsString()
{
    return _Filler220.PadRight(13);
}

// Set<>AsString()
public void SetFiller220AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler220 = value;
}

// Standard Getter
public string GetFiller221()
{
    return _Filler221;
}

// Standard Setter
public void SetFiller221(string value)
{
    _Filler221 = value;
}

// Get<>AsString()
public string GetFiller221AsString()
{
    return _Filler221.PadRight(0);
}

// Set<>AsString()
public void SetFiller221AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler221 = value;
}

// Standard Getter
public string GetFiller222()
{
    return _Filler222;
}

// Standard Setter
public void SetFiller222(string value)
{
    _Filler222 = value;
}

// Get<>AsString()
public string GetFiller222AsString()
{
    return _Filler222.PadRight(13);
}

// Set<>AsString()
public void SetFiller222AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler222 = value;
}

// Standard Getter
public string GetFiller223()
{
    return _Filler223;
}

// Standard Setter
public void SetFiller223(string value)
{
    _Filler223 = value;
}

// Get<>AsString()
public string GetFiller223AsString()
{
    return _Filler223.PadRight(0);
}

// Set<>AsString()
public void SetFiller223AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler223 = value;
}

// Standard Getter
public string GetFiller224()
{
    return _Filler224;
}

// Standard Setter
public void SetFiller224(string value)
{
    _Filler224 = value;
}

// Get<>AsString()
public string GetFiller224AsString()
{
    return _Filler224.PadRight(13);
}

// Set<>AsString()
public void SetFiller224AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler224 = value;
}

// Standard Getter
public string GetFiller225()
{
    return _Filler225;
}

// Standard Setter
public void SetFiller225(string value)
{
    _Filler225 = value;
}

// Get<>AsString()
public string GetFiller225AsString()
{
    return _Filler225.PadRight(0);
}

// Set<>AsString()
public void SetFiller225AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler225 = value;
}

// Standard Getter
public decimal GetGlFooter3Gain()
{
    return _GlFooter3Gain;
}

// Standard Setter
public void SetGlFooter3Gain(decimal value)
{
    _GlFooter3Gain = value;
}

// Get<>AsString()
public string GetGlFooter3GainAsString()
{
    return _GlFooter3Gain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlFooter3GainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlFooter3Gain = parsed;
}

// Standard Getter
public string GetFiller226()
{
    return _Filler226;
}

// Standard Setter
public void SetFiller226(string value)
{
    _Filler226 = value;
}

// Get<>AsString()
public string GetFiller226AsString()
{
    return _Filler226.PadRight(0);
}

// Set<>AsString()
public void SetFiller226AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler226 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetGlDetailLine(string value)
{
    _GlDetailLine.SetGlDetailLineAsString(value);
}
// Nested Class: GlDetailLine
public class GlDetailLine
{
    private static int _size = 66;
    
    // Fields in the class
    
    
    // [DEBUG] Field: GlDate, is_external=, is_static_class=False, static_prefix=
    private string _GlDate ="";
    
    
    
    
    // [DEBUG] Field: Filler227, is_external=, is_static_class=False, static_prefix=
    private string _Filler227 ="";
    
    
    
    
    // [DEBUG] Field: GlTransactionType, is_external=, is_static_class=False, static_prefix=
    private string _GlTransactionType ="";
    
    
    
    
    // [DEBUG] Field: Filler228, is_external=, is_static_class=False, static_prefix=
    private string _Filler228 ="";
    
    
    
    
    // [DEBUG] Field: GlStockDetails, is_external=, is_static_class=False, static_prefix=
    private GlDetailLine.GlStockDetails _GlStockDetails = new GlDetailLine.GlStockDetails();
    
    
    
    
    // [DEBUG] Field: Filler229, is_external=, is_static_class=False, static_prefix=
    private string _Filler229 ="";
    
    
    
    
    // [DEBUG] Field: GlRpiBasis, is_external=, is_static_class=False, static_prefix=
    private string _GlRpiBasis ="";
    
    
    
    
    // [DEBUG] Field: Filler230, is_external=, is_static_class=False, static_prefix=
    private string _Filler230 ="";
    
    
    
    
    // [DEBUG] Field: GlHolding, is_external=, is_static_class=False, static_prefix=
    private decimal _GlHolding =0;
    
    
    
    
    // [DEBUG] Field: Filler231, is_external=, is_static_class=False, static_prefix=
    private string _Filler231 ="";
    
    
    
    
    // [DEBUG] Field: GlProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _GlProceeds =0;
    
    
    
    
    // [DEBUG] Field: Filler232, is_external=, is_static_class=False, static_prefix=
    private string _Filler232 ="";
    
    
    
    
    // [DEBUG] Field: GlCost, is_external=, is_static_class=False, static_prefix=
    private decimal _GlCost =0;
    
    
    
    
    // [DEBUG] Field: GlPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _GlPrice =0;
    
    
    
    
    // [DEBUG] Field: Filler233, is_external=, is_static_class=False, static_prefix=
    private string _Filler233 ="";
    
    
    
    
    // [DEBUG] Field: GlGain, is_external=, is_static_class=False, static_prefix=
    private decimal _GlGain =0;
    
    
    
    
    // [DEBUG] Field: Filler234, is_external=, is_static_class=False, static_prefix=
    private string _Filler234 ="";
    
    
    
    
public GlDetailLine() {}

public GlDetailLine(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetGlDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller227(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlTransactionType(data.Substring(offset, 20).Trim());
    offset += 20;
    SetFiller228(data.Substring(offset, 0).Trim());
    offset += 0;
    _GlStockDetails.SetGlStockDetailsAsString(data.Substring(offset, GlStockDetails.GetSize()));
    offset += 1;
    SetFiller229(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlRpiBasis(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller230(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlHolding(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetFiller231(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller232(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetGlPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetFiller233(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGlGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetFiller234(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGlDetailLineAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_GlDate.PadRight(0));
    result.Append(_Filler227.PadRight(0));
    result.Append(_GlTransactionType.PadRight(20));
    result.Append(_Filler228.PadRight(0));
    result.Append(_GlStockDetails.GetGlStockDetailsAsString());
    result.Append(_Filler229.PadRight(0));
    result.Append(_GlRpiBasis.PadRight(0));
    result.Append(_Filler230.PadRight(0));
    result.Append(_GlHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler231.PadRight(0));
    result.Append(_GlProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler232.PadRight(0));
    result.Append(_GlCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_GlPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler233.PadRight(0));
    result.Append(_GlGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler234.PadRight(0));
    
    return result.ToString();
}

public void SetGlDetailLineAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGlDate(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller227(extracted);
    }
    offset += 0;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetGlTransactionType(extracted);
    }
    offset += 20;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller228(extracted);
    }
    offset += 0;
    if (offset + 1 <= data.Length)
    {
        _GlStockDetails.SetGlStockDetailsAsString(data.Substring(offset, 1));
    }
    else
    {
        _GlStockDetails.SetGlStockDetailsAsString(data.Substring(offset));
    }
    offset += 1;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller229(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGlRpiBasis(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller230(extracted);
    }
    offset += 0;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlHolding(parsedDec);
    }
    offset += 9;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller231(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlProceeds(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller232(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlCost(parsedDec);
    }
    offset += 10;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlPrice(parsedDec);
    }
    offset += 6;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller233(extracted);
    }
    offset += 0;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetGlGain(parsedDec);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller234(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetGlDate()
{
    return _GlDate;
}

// Standard Setter
public void SetGlDate(string value)
{
    _GlDate = value;
}

// Get<>AsString()
public string GetGlDateAsString()
{
    return _GlDate.PadRight(0);
}

// Set<>AsString()
public void SetGlDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GlDate = value;
}

// Standard Getter
public string GetFiller227()
{
    return _Filler227;
}

// Standard Setter
public void SetFiller227(string value)
{
    _Filler227 = value;
}

// Get<>AsString()
public string GetFiller227AsString()
{
    return _Filler227.PadRight(0);
}

// Set<>AsString()
public void SetFiller227AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler227 = value;
}

// Standard Getter
public string GetGlTransactionType()
{
    return _GlTransactionType;
}

// Standard Setter
public void SetGlTransactionType(string value)
{
    _GlTransactionType = value;
}

// Get<>AsString()
public string GetGlTransactionTypeAsString()
{
    return _GlTransactionType.PadRight(20);
}

// Set<>AsString()
public void SetGlTransactionTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GlTransactionType = value;
}

// Standard Getter
public string GetFiller228()
{
    return _Filler228;
}

// Standard Setter
public void SetFiller228(string value)
{
    _Filler228 = value;
}

// Get<>AsString()
public string GetFiller228AsString()
{
    return _Filler228.PadRight(0);
}

// Set<>AsString()
public void SetFiller228AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler228 = value;
}

// Standard Getter
public GlStockDetails GetGlStockDetails()
{
    return _GlStockDetails;
}

// Standard Setter
public void SetGlStockDetails(GlStockDetails value)
{
    _GlStockDetails = value;
}

// Get<>AsString()
public string GetGlStockDetailsAsString()
{
    return _GlStockDetails != null ? _GlStockDetails.GetGlStockDetailsAsString() : "";
}

// Set<>AsString()
public void SetGlStockDetailsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_GlStockDetails == null)
    {
        _GlStockDetails = new GlStockDetails();
    }
    _GlStockDetails.SetGlStockDetailsAsString(value);
}

// Standard Getter
public string GetFiller229()
{
    return _Filler229;
}

// Standard Setter
public void SetFiller229(string value)
{
    _Filler229 = value;
}

// Get<>AsString()
public string GetFiller229AsString()
{
    return _Filler229.PadRight(0);
}

// Set<>AsString()
public void SetFiller229AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler229 = value;
}

// Standard Getter
public string GetGlRpiBasis()
{
    return _GlRpiBasis;
}

// Standard Setter
public void SetGlRpiBasis(string value)
{
    _GlRpiBasis = value;
}

// Get<>AsString()
public string GetGlRpiBasisAsString()
{
    return _GlRpiBasis.PadRight(0);
}

// Set<>AsString()
public void SetGlRpiBasisAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GlRpiBasis = value;
}

// Standard Getter
public string GetFiller230()
{
    return _Filler230;
}

// Standard Setter
public void SetFiller230(string value)
{
    _Filler230 = value;
}

// Get<>AsString()
public string GetFiller230AsString()
{
    return _Filler230.PadRight(0);
}

// Set<>AsString()
public void SetFiller230AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler230 = value;
}

// Standard Getter
public decimal GetGlHolding()
{
    return _GlHolding;
}

// Standard Setter
public void SetGlHolding(decimal value)
{
    _GlHolding = value;
}

// Get<>AsString()
public string GetGlHoldingAsString()
{
    return _GlHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlHoldingAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlHolding = parsed;
}

// Standard Getter
public string GetFiller231()
{
    return _Filler231;
}

// Standard Setter
public void SetFiller231(string value)
{
    _Filler231 = value;
}

// Get<>AsString()
public string GetFiller231AsString()
{
    return _Filler231.PadRight(0);
}

// Set<>AsString()
public void SetFiller231AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler231 = value;
}

// Standard Getter
public decimal GetGlProceeds()
{
    return _GlProceeds;
}

// Standard Setter
public void SetGlProceeds(decimal value)
{
    _GlProceeds = value;
}

// Get<>AsString()
public string GetGlProceedsAsString()
{
    return _GlProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlProceeds = parsed;
}

// Standard Getter
public string GetFiller232()
{
    return _Filler232;
}

// Standard Setter
public void SetFiller232(string value)
{
    _Filler232 = value;
}

// Get<>AsString()
public string GetFiller232AsString()
{
    return _Filler232.PadRight(0);
}

// Set<>AsString()
public void SetFiller232AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler232 = value;
}

// Standard Getter
public decimal GetGlCost()
{
    return _GlCost;
}

// Standard Setter
public void SetGlCost(decimal value)
{
    _GlCost = value;
}

// Get<>AsString()
public string GetGlCostAsString()
{
    return _GlCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlCost = parsed;
}

// Standard Getter
public decimal GetGlPrice()
{
    return _GlPrice;
}

// Standard Setter
public void SetGlPrice(decimal value)
{
    _GlPrice = value;
}

// Get<>AsString()
public string GetGlPriceAsString()
{
    return _GlPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlPrice = parsed;
}

// Standard Getter
public string GetFiller233()
{
    return _Filler233;
}

// Standard Setter
public void SetFiller233(string value)
{
    _Filler233 = value;
}

// Get<>AsString()
public string GetFiller233AsString()
{
    return _Filler233.PadRight(0);
}

// Set<>AsString()
public void SetFiller233AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler233 = value;
}

// Standard Getter
public decimal GetGlGain()
{
    return _GlGain;
}

// Standard Setter
public void SetGlGain(decimal value)
{
    _GlGain = value;
}

// Get<>AsString()
public string GetGlGainAsString()
{
    return _GlGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetGlGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _GlGain = parsed;
}

// Standard Getter
public string GetFiller234()
{
    return _Filler234;
}

// Standard Setter
public void SetFiller234(string value)
{
    _Filler234 = value;
}

// Get<>AsString()
public string GetFiller234AsString()
{
    return _Filler234.PadRight(0);
}

// Set<>AsString()
public void SetFiller234AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler234 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: GlStockDetails
public class GlStockDetails
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: GlStockChar, is_external=, is_static_class=False, static_prefix=
    private string[] _GlStockChar = new string[63];
    
    
    
    
public GlStockDetails() {}

public GlStockDetails(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 63; i++)
    {
        string value = data.Substring(offset, 1);
        _GlStockChar[i] = value.Trim();
        offset += 1;
    }
    
}

// Serialization methods
public string GetGlStockDetailsAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 63; i++)
    {
        result.Append(_GlStockChar[i].PadRight(1));
    }
    
    return result.ToString();
}

public void SetGlStockDetailsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 63; i++)
    {
        if (offset + 1 > data.Length) break;
        string val = data.Substring(offset, 1);
        
        _GlStockChar[i] = val.Trim();
        offset += 1;
    }
}

// Getter and Setter methods

// Array Accessors for GlStockChar
public string GetGlStockCharAt(int index)
{
    return _GlStockChar[index];
}

public void SetGlStockCharAt(int index, string value)
{
    _GlStockChar[index] = value;
}

public string GetGlStockCharAsStringAt(int index)
{
    return _GlStockChar[index].PadRight(1);
}

public void SetGlStockCharAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _GlStockChar[index] = value;
}

// Flattened accessors (index 0)
public string GetGlStockChar()
{
    return _GlStockChar != null && _GlStockChar.Length > 0
    ? _GlStockChar[0]
    : default(string);
}

public void SetGlStockChar(string value)
{
    if (_GlStockChar == null || _GlStockChar.Length == 0)
    _GlStockChar = new string[1];
    _GlStockChar[0] = value;
}

public string GetGlStockCharAsString()
{
    return _GlStockChar != null && _GlStockChar.Length > 0
    ? _GlStockChar[0].ToString()
    : string.Empty;
}

public void SetGlStockCharAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_GlStockChar == null || _GlStockChar.Length == 0)
    _GlStockChar = new string[1];
    
    _GlStockChar[0] = value;
}




public static int GetSize()
{
    return _size;
}

}
}

}}