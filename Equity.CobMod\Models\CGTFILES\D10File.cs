using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D10File Data Structure

public class D10File
{
    private static int _size = 12;
    // [DEBUG] Class: D10File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler163, is_external=, is_static_class=False, static_prefix=
    private string _Filler163 ="$";
    
    
    
    
    // [DEBUG] Field: D10UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D10UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler164, is_external=, is_static_class=False, static_prefix=
    private string _Filler164 ="EXS.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD10FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler163.PadRight(1));
        result.Append(_D10UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler164.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD10FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller163(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD10UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller164(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD10FileAsString();
    }
    // Set<>String Override function
    public void SetD10File(string value)
    {
        SetD10FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller163()
    {
        return _Filler163;
    }
    
    // Standard Setter
    public void SetFiller163(string value)
    {
        _Filler163 = value;
    }
    
    // Get<>AsString()
    public string GetFiller163AsString()
    {
        return _Filler163.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller163AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler163 = value;
    }
    
    // Standard Getter
    public int GetD10UserNo()
    {
        return _D10UserNo;
    }
    
    // Standard Setter
    public void SetD10UserNo(int value)
    {
        _D10UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD10UserNoAsString()
    {
        return _D10UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD10UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D10UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller164()
    {
        return _Filler164;
    }
    
    // Standard Setter
    public void SetFiller164(string value)
    {
        _Filler164 = value;
    }
    
    // Get<>AsString()
    public string GetFiller164AsString()
    {
        return _Filler164.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller164AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler164 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}