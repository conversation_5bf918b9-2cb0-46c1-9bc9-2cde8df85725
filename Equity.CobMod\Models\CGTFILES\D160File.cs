using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D160File Data Structure

public class D160File
{
    private static int _size = 12;
    // [DEBUG] Class: D160File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler323, is_external=, is_static_class=False, static_prefix=
    private string _Filler323 ="$";
    
    
    
    
    // [DEBUG] Field: D160UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D160UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler324, is_external=, is_static_class=False, static_prefix=
    private string _Filler324 ="UFN.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD160FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler323.PadRight(1));
        result.Append(_D160UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler324.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD160FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller323(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD160UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller324(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD160FileAsString();
    }
    // Set<>String Override function
    public void SetD160File(string value)
    {
        SetD160FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller323()
    {
        return _Filler323;
    }
    
    // Standard Setter
    public void SetFiller323(string value)
    {
        _Filler323 = value;
    }
    
    // Get<>AsString()
    public string GetFiller323AsString()
    {
        return _Filler323.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller323AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler323 = value;
    }
    
    // Standard Getter
    public int GetD160UserNo()
    {
        return _D160UserNo;
    }
    
    // Standard Setter
    public void SetD160UserNo(int value)
    {
        _D160UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD160UserNoAsString()
    {
        return _D160UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD160UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D160UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller324()
    {
        return _Filler324;
    }
    
    // Standard Setter
    public void SetFiller324(string value)
    {
        _Filler324 = value;
    }
    
    // Get<>AsString()
    public string GetFiller324AsString()
    {
        return _Filler324.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller324AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler324 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}