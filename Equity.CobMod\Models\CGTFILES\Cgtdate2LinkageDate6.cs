using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing Cgtdate2LinkageDate6 Data Structure

public class Cgtdate2LinkageDate6
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate6, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd6 =0;
    
    
    
    
    // [DEBUG] Field: Filler383, is_external=, is_static_class=False, static_prefix=
    private Filler383 _Filler383 = new Filler383();
    
    
    
    
    // [DEBUG] Field: Filler384, is_external=, is_static_class=False, static_prefix=
    private Filler384 _Filler384 = new Filler384();
    
    
    
    
    // [DEBUG] Field: Filler385, is_external=, is_static_class=False, static_prefix=
    private Filler385 _Filler385 = new Filler385();
    
    
    
    
    // [DEBUG] Field: Filler386, is_external=, is_static_class=False, static_prefix=
    private Filler386 _Filler386 = new Filler386();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0'));
        result.Append(_Filler383.GetFiller383AsString());
        result.Append(_Filler384.GetFiller384AsString());
        result.Append(_Filler385.GetFiller385AsString());
        result.Append(_Filler386.GetFiller386AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd6(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler383.SetFiller383AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler383.SetFiller383AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler384.SetFiller384AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler384.SetFiller384AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler385.SetFiller385AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler385.SetFiller385AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler386.SetFiller386AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler386.SetFiller386AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate6AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate6(string value)
    {
        SetCgtdate2LinkageDate6AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd6()
    {
        return _Cgtdate2Ccyymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd6(int value)
    {
        _Cgtdate2Ccyymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd6AsString()
    {
        return _Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd6 = parsed;
    }
    
    // Standard Getter
    public Filler383 GetFiller383()
    {
        return _Filler383;
    }
    
    // Standard Setter
    public void SetFiller383(Filler383 value)
    {
        _Filler383 = value;
    }
    
    // Get<>AsString()
    public string GetFiller383AsString()
    {
        return _Filler383 != null ? _Filler383.GetFiller383AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller383AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler383 == null)
        {
            _Filler383 = new Filler383();
        }
        _Filler383.SetFiller383AsString(value);
    }
    
    // Standard Getter
    public Filler384 GetFiller384()
    {
        return _Filler384;
    }
    
    // Standard Setter
    public void SetFiller384(Filler384 value)
    {
        _Filler384 = value;
    }
    
    // Get<>AsString()
    public string GetFiller384AsString()
    {
        return _Filler384 != null ? _Filler384.GetFiller384AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller384AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler384 == null)
        {
            _Filler384 = new Filler384();
        }
        _Filler384.SetFiller384AsString(value);
    }
    
    // Standard Getter
    public Filler385 GetFiller385()
    {
        return _Filler385;
    }
    
    // Standard Setter
    public void SetFiller385(Filler385 value)
    {
        _Filler385 = value;
    }
    
    // Get<>AsString()
    public string GetFiller385AsString()
    {
        return _Filler385 != null ? _Filler385.GetFiller385AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller385AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler385 == null)
        {
            _Filler385 = new Filler385();
        }
        _Filler385.SetFiller385AsString(value);
    }
    
    // Standard Getter
    public Filler386 GetFiller386()
    {
        return _Filler386;
    }
    
    // Standard Setter
    public void SetFiller386(Filler386 value)
    {
        _Filler386 = value;
    }
    
    // Get<>AsString()
    public string GetFiller386AsString()
    {
        return _Filler386 != null ? _Filler386.GetFiller386AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller386AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler386 == null)
        {
            _Filler386 = new Filler386();
        }
        _Filler386.SetFiller386AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller383(string value)
    {
        _Filler383.SetFiller383AsString(value);
    }
    // Nested Class: Filler383
    public class Filler383
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd6, is_external=, is_static_class=False, static_prefix=
        private Filler383.Cgtdate2Yymmdd6 _Cgtdate2Yymmdd6 = new Filler383.Cgtdate2Yymmdd6();
        
        
        
        
    public Filler383() {}
    
    public Filler383(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, Cgtdate2Yymmdd6.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller383AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc6.PadRight(2));
        result.Append(_Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetFiller383AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc6(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc6()
    {
        return _Cgtdate2Cc6;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc6(string value)
    {
        _Cgtdate2Cc6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc6AsString()
    {
        return _Cgtdate2Cc6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd6 GetCgtdate2Yymmdd6()
    {
        return _Cgtdate2Yymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd6(Cgtdate2Yymmdd6 value)
    {
        _Cgtdate2Yymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd6AsString()
    {
        return _Cgtdate2Yymmdd6 != null ? _Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd6 == null)
        {
            _Cgtdate2Yymmdd6 = new Cgtdate2Yymmdd6();
        }
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd6
    public class Cgtdate2Yymmdd6
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd6, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd6.Cgtdate2Mmdd6 _Cgtdate2Mmdd6 = new Cgtdate2Yymmdd6.Cgtdate2Mmdd6();
        
        
        
        
    public Cgtdate2Yymmdd6() {}
    
    public Cgtdate2Yymmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, Cgtdate2Mmdd6.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy6.PadRight(2));
        result.Append(_Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy6(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy6()
    {
        return _Cgtdate2Yy6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy6(string value)
    {
        _Cgtdate2Yy6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy6AsString()
    {
        return _Cgtdate2Yy6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd6 GetCgtdate2Mmdd6()
    {
        return _Cgtdate2Mmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd6(Cgtdate2Mmdd6 value)
    {
        _Cgtdate2Mmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd6AsString()
    {
        return _Cgtdate2Mmdd6 != null ? _Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd6 == null)
        {
            _Cgtdate2Mmdd6 = new Cgtdate2Mmdd6();
        }
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd6
    public class Cgtdate2Mmdd6
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd6 ="";
        
        
        
        
    public Cgtdate2Mmdd6() {}
    
    public Cgtdate2Mmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm6(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd6(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm6.PadRight(2));
        result.Append(_Cgtdate2Dd6.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm6(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd6(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm6()
    {
        return _Cgtdate2Mm6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm6(string value)
    {
        _Cgtdate2Mm6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm6AsString()
    {
        return _Cgtdate2Mm6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm6 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd6()
    {
        return _Cgtdate2Dd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd6(string value)
    {
        _Cgtdate2Dd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd6AsString()
    {
        return _Cgtdate2Dd6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd6 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller384(string value)
{
    _Filler384.SetFiller384AsString(value);
}
// Nested Class: Filler384
public class Filler384
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd6 =0;
    
    
    
    
public Filler384() {}

public Filler384(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller384AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy6.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd6.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller384AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy6(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd6(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy6()
{
    return _Cgtdate2CCcyy6;
}

// Standard Setter
public void SetCgtdate2CCcyy6(int value)
{
    _Cgtdate2CCcyy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy6AsString()
{
    return _Cgtdate2CCcyy6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd6()
{
    return _Cgtdate2CMmdd6;
}

// Standard Setter
public void SetCgtdate2CMmdd6(int value)
{
    _Cgtdate2CMmdd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd6AsString()
{
    return _Cgtdate2CMmdd6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller385(string value)
{
    _Filler385.SetFiller385AsString(value);
}
// Nested Class: Filler385
public class Filler385
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd6 =0;
    
    
    
    
public Filler385() {}

public Filler385(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller385AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd6.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller385AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd6(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc6()
{
    return _Cgtdate2CCc6;
}

// Standard Setter
public void SetCgtdate2CCc6(int value)
{
    _Cgtdate2CCc6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc6AsString()
{
    return _Cgtdate2CCc6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc6 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy6()
{
    return _Cgtdate2CYy6;
}

// Standard Setter
public void SetCgtdate2CYy6(int value)
{
    _Cgtdate2CYy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy6AsString()
{
    return _Cgtdate2CYy6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm6()
{
    return _Cgtdate2CMm6;
}

// Standard Setter
public void SetCgtdate2CMm6(int value)
{
    _Cgtdate2CMm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm6AsString()
{
    return _Cgtdate2CMm6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm6 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd6()
{
    return _Cgtdate2CDd6;
}

// Standard Setter
public void SetCgtdate2CDd6(int value)
{
    _Cgtdate2CDd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd6AsString()
{
    return _Cgtdate2CDd6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller386(string value)
{
    _Filler386.SetFiller386AsString(value);
}
// Nested Class: Filler386
public class Filler386
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm6 =0;
    
    
    
    
    // [DEBUG] Field: Filler387, is_external=, is_static_class=False, static_prefix=
    private string _Filler387 ="";
    
    
    
    
public Filler386() {}

public Filler386(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm6(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller387(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller386AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm6.ToString().PadLeft(6, '0'));
    result.Append(_Filler387.PadRight(2));
    
    return result.ToString();
}

public void SetFiller386AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm6(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller387(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm6()
{
    return _Cgtdate2CCcyymm6;
}

// Standard Setter
public void SetCgtdate2CCcyymm6(int value)
{
    _Cgtdate2CCcyymm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm6AsString()
{
    return _Cgtdate2CCcyymm6.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm6 = parsed;
}

// Standard Getter
public string GetFiller387()
{
    return _Filler387;
}

// Standard Setter
public void SetFiller387(string value)
{
    _Filler387 = value;
}

// Get<>AsString()
public string GetFiller387AsString()
{
    return _Filler387.PadRight(2);
}

// Set<>AsString()
public void SetFiller387AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler387 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}