using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing CgtlogLinkageArea2 Data Structure

public class CgtlogLinkageArea2
{
    private static int _size = 75;
    // [DEBUG] Class: CgtlogLinkageArea2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LLogMessageNo, is_external=, is_static_class=False, static_prefix=
    private string _LLogMessageNo ="";
    
    
    
    
    // [DEBUG] Field: LLogMessageType, is_external=, is_static_class=False, static_prefix=
    private string _LLogMessageType ="";
    
    
    // 88-level condition checks for LLogMessageType
    public bool IsQuitRun()
    {
        if (this._LLogMessageType == "Q") return true;
        return false;
    }
    
    
    // [DEBUG] Field: LLogMessage, is_external=, is_static_class=False, static_prefix=
    private string _LLogMessage ="";
    
    
    
    
    
    // Serialization methods
    public string GetCgtlogLinkageArea2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LLogMessageNo.PadRight(5));
        result.Append(_LLogMessageType.PadRight(1));
        result.Append(_LLogMessage.PadRight(69));
        
        return result.ToString();
    }
    
    public void SetCgtlogLinkageArea2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetLLogMessageNo(extracted);
        }
        offset += 5;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLLogMessageType(extracted);
        }
        offset += 1;
        if (offset + 69 <= data.Length)
        {
            string extracted = data.Substring(offset, 69).Trim();
            SetLLogMessage(extracted);
        }
        offset += 69;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtlogLinkageArea2AsString();
    }
    // Set<>String Override function
    public void SetCgtlogLinkageArea2(string value)
    {
        SetCgtlogLinkageArea2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLLogMessageNo()
    {
        return _LLogMessageNo;
    }
    
    // Standard Setter
    public void SetLLogMessageNo(string value)
    {
        _LLogMessageNo = value;
    }
    
    // Get<>AsString()
    public string GetLLogMessageNoAsString()
    {
        return _LLogMessageNo.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetLLogMessageNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LLogMessageNo = value;
    }
    
    // Standard Getter
    public string GetLLogMessageType()
    {
        return _LLogMessageType;
    }
    
    // Standard Setter
    public void SetLLogMessageType(string value)
    {
        _LLogMessageType = value;
    }
    
    // Get<>AsString()
    public string GetLLogMessageTypeAsString()
    {
        return _LLogMessageType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLLogMessageTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LLogMessageType = value;
    }
    
    // Standard Getter
    public string GetLLogMessage()
    {
        return _LLogMessage;
    }
    
    // Standard Setter
    public void SetLLogMessage(string value)
    {
        _LLogMessage = value;
    }
    
    // Get<>AsString()
    public string GetLLogMessageAsString()
    {
        return _LLogMessage.PadRight(69);
    }
    
    // Set<>AsString()
    public void SetLLogMessageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LLogMessage = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}