using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D32Record Data Structure

public class D32Record
{
    private static int _size = 41;
    // [DEBUG] Class: D32Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D32Key, is_external=, is_static_class=False, static_prefix=
    private D32Key _D32Key = new D32Key();
    
    
    
    
    // [DEBUG] Field: D32Description, is_external=, is_static_class=False, static_prefix=
    private string _D32Description ="";
    
    
    
    
    
    // Serialization methods
    public string GetD32RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D32Key.GetD32KeyAsString());
        result.Append(_D32Description.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetD32RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            _D32Key.SetD32KeyAsString(data.Substring(offset, 1));
        }
        else
        {
            _D32Key.SetD32KeyAsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD32Description(extracted);
        }
        offset += 40;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD32RecordAsString();
    }
    // Set<>String Override function
    public void SetD32Record(string value)
    {
        SetD32RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D32Key GetD32Key()
    {
        return _D32Key;
    }
    
    // Standard Setter
    public void SetD32Key(D32Key value)
    {
        _D32Key = value;
    }
    
    // Get<>AsString()
    public string GetD32KeyAsString()
    {
        return _D32Key != null ? _D32Key.GetD32KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD32KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D32Key == null)
        {
            _D32Key = new D32Key();
        }
        _D32Key.SetD32KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD32Description()
    {
        return _D32Description;
    }
    
    // Standard Setter
    public void SetD32Description(string value)
    {
        _D32Description = value;
    }
    
    // Get<>AsString()
    public string GetD32DescriptionAsString()
    {
        return _D32Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD32DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D32Description = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD32Key(string value)
    {
        _D32Key.SetD32KeyAsString(value);
    }
    // Nested Class: D32Key
    public class D32Key
    {
        private static int _size = 1;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D32SecurityType, is_external=, is_static_class=False, static_prefix=
        private string _D32SecurityType ="";
        
        
        
        
    public D32Key() {}
    
    public D32Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD32SecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetD32KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D32SecurityType.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD32KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD32SecurityType(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD32SecurityType()
    {
        return _D32SecurityType;
    }
    
    // Standard Setter
    public void SetD32SecurityType(string value)
    {
        _D32SecurityType = value;
    }
    
    // Get<>AsString()
    public string GetD32SecurityTypeAsString()
    {
        return _D32SecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD32SecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D32SecurityType = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}