using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing Cgtdate2LinkageDate2 Data Structure

public class Cgtdate2LinkageDate2
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd2 =0;
    
    
    
    
    // [DEBUG] Field: Filler363, is_external=, is_static_class=False, static_prefix=
    private Filler363 _Filler363 = new Filler363();
    
    
    
    
    // [DEBUG] Field: Filler364, is_external=, is_static_class=False, static_prefix=
    private Filler364 _Filler364 = new Filler364();
    
    
    
    
    // [DEBUG] Field: Filler365, is_external=, is_static_class=False, static_prefix=
    private Filler365 _Filler365 = new Filler365();
    
    
    
    
    // [DEBUG] Field: Filler366, is_external=, is_static_class=False, static_prefix=
    private Filler366 _Filler366 = new Filler366();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd2.ToString().PadLeft(8, '0'));
        result.Append(_Filler363.GetFiller363AsString());
        result.Append(_Filler364.GetFiller364AsString());
        result.Append(_Filler365.GetFiller365AsString());
        result.Append(_Filler366.GetFiller366AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd2(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler363.SetFiller363AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler363.SetFiller363AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler364.SetFiller364AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler364.SetFiller364AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler365.SetFiller365AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler365.SetFiller365AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler366.SetFiller366AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler366.SetFiller366AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate2AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate2(string value)
    {
        SetCgtdate2LinkageDate2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd2()
    {
        return _Cgtdate2Ccyymmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd2(int value)
    {
        _Cgtdate2Ccyymmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd2AsString()
    {
        return _Cgtdate2Ccyymmdd2.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd2 = parsed;
    }
    
    // Standard Getter
    public Filler363 GetFiller363()
    {
        return _Filler363;
    }
    
    // Standard Setter
    public void SetFiller363(Filler363 value)
    {
        _Filler363 = value;
    }
    
    // Get<>AsString()
    public string GetFiller363AsString()
    {
        return _Filler363 != null ? _Filler363.GetFiller363AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller363AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler363 == null)
        {
            _Filler363 = new Filler363();
        }
        _Filler363.SetFiller363AsString(value);
    }
    
    // Standard Getter
    public Filler364 GetFiller364()
    {
        return _Filler364;
    }
    
    // Standard Setter
    public void SetFiller364(Filler364 value)
    {
        _Filler364 = value;
    }
    
    // Get<>AsString()
    public string GetFiller364AsString()
    {
        return _Filler364 != null ? _Filler364.GetFiller364AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller364AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler364 == null)
        {
            _Filler364 = new Filler364();
        }
        _Filler364.SetFiller364AsString(value);
    }
    
    // Standard Getter
    public Filler365 GetFiller365()
    {
        return _Filler365;
    }
    
    // Standard Setter
    public void SetFiller365(Filler365 value)
    {
        _Filler365 = value;
    }
    
    // Get<>AsString()
    public string GetFiller365AsString()
    {
        return _Filler365 != null ? _Filler365.GetFiller365AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller365AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler365 == null)
        {
            _Filler365 = new Filler365();
        }
        _Filler365.SetFiller365AsString(value);
    }
    
    // Standard Getter
    public Filler366 GetFiller366()
    {
        return _Filler366;
    }
    
    // Standard Setter
    public void SetFiller366(Filler366 value)
    {
        _Filler366 = value;
    }
    
    // Get<>AsString()
    public string GetFiller366AsString()
    {
        return _Filler366 != null ? _Filler366.GetFiller366AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller366AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler366 == null)
        {
            _Filler366 = new Filler366();
        }
        _Filler366.SetFiller366AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller363(string value)
    {
        _Filler363.SetFiller363AsString(value);
    }
    // Nested Class: Filler363
    public class Filler363
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd2, is_external=, is_static_class=False, static_prefix=
        private Filler363.Cgtdate2Yymmdd2 _Cgtdate2Yymmdd2 = new Filler363.Cgtdate2Yymmdd2();
        
        
        
        
    public Filler363() {}
    
    public Filler363(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc2(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset, Cgtdate2Yymmdd2.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller363AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc2.PadRight(2));
        result.Append(_Cgtdate2Yymmdd2.GetCgtdate2Yymmdd2AsString());
        
        return result.ToString();
    }
    
    public void SetFiller363AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc2(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc2()
    {
        return _Cgtdate2Cc2;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc2(string value)
    {
        _Cgtdate2Cc2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc2AsString()
    {
        return _Cgtdate2Cc2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc2 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd2 GetCgtdate2Yymmdd2()
    {
        return _Cgtdate2Yymmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd2(Cgtdate2Yymmdd2 value)
    {
        _Cgtdate2Yymmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd2AsString()
    {
        return _Cgtdate2Yymmdd2 != null ? _Cgtdate2Yymmdd2.GetCgtdate2Yymmdd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd2 == null)
        {
            _Cgtdate2Yymmdd2 = new Cgtdate2Yymmdd2();
        }
        _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd2
    public class Cgtdate2Yymmdd2
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd2, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd2.Cgtdate2Mmdd2 _Cgtdate2Mmdd2 = new Cgtdate2Yymmdd2.Cgtdate2Mmdd2();
        
        
        
        
    public Cgtdate2Yymmdd2() {}
    
    public Cgtdate2Yymmdd2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy2(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset, Cgtdate2Mmdd2.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy2.PadRight(2));
        result.Append(_Cgtdate2Mmdd2.GetCgtdate2Mmdd2AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy2(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy2()
    {
        return _Cgtdate2Yy2;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy2(string value)
    {
        _Cgtdate2Yy2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy2AsString()
    {
        return _Cgtdate2Yy2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy2 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd2 GetCgtdate2Mmdd2()
    {
        return _Cgtdate2Mmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd2(Cgtdate2Mmdd2 value)
    {
        _Cgtdate2Mmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd2AsString()
    {
        return _Cgtdate2Mmdd2 != null ? _Cgtdate2Mmdd2.GetCgtdate2Mmdd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd2 == null)
        {
            _Cgtdate2Mmdd2 = new Cgtdate2Mmdd2();
        }
        _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd2
    public class Cgtdate2Mmdd2
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd2 ="";
        
        
        
        
    public Cgtdate2Mmdd2() {}
    
    public Cgtdate2Mmdd2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm2(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd2(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm2.PadRight(2));
        result.Append(_Cgtdate2Dd2.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm2(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd2(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm2()
    {
        return _Cgtdate2Mm2;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm2(string value)
    {
        _Cgtdate2Mm2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm2AsString()
    {
        return _Cgtdate2Mm2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm2 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd2()
    {
        return _Cgtdate2Dd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd2(string value)
    {
        _Cgtdate2Dd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd2AsString()
    {
        return _Cgtdate2Dd2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd2 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller364(string value)
{
    _Filler364.SetFiller364AsString(value);
}
// Nested Class: Filler364
public class Filler364
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd2 =0;
    
    
    
    
public Filler364() {}

public Filler364(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy2(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd2(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller364AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy2.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd2.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller364AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy2(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd2(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy2()
{
    return _Cgtdate2CCcyy2;
}

// Standard Setter
public void SetCgtdate2CCcyy2(int value)
{
    _Cgtdate2CCcyy2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy2AsString()
{
    return _Cgtdate2CCcyy2.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy2 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd2()
{
    return _Cgtdate2CMmdd2;
}

// Standard Setter
public void SetCgtdate2CMmdd2(int value)
{
    _Cgtdate2CMmdd2 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd2AsString()
{
    return _Cgtdate2CMmdd2.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd2 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller365(string value)
{
    _Filler365.SetFiller365AsString(value);
}
// Nested Class: Filler365
public class Filler365
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd2 =0;
    
    
    
    
public Filler365() {}

public Filler365(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller365AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd2.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller365AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd2(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc2()
{
    return _Cgtdate2CCc2;
}

// Standard Setter
public void SetCgtdate2CCc2(int value)
{
    _Cgtdate2CCc2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc2AsString()
{
    return _Cgtdate2CCc2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc2 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy2()
{
    return _Cgtdate2CYy2;
}

// Standard Setter
public void SetCgtdate2CYy2(int value)
{
    _Cgtdate2CYy2 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy2AsString()
{
    return _Cgtdate2CYy2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy2 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm2()
{
    return _Cgtdate2CMm2;
}

// Standard Setter
public void SetCgtdate2CMm2(int value)
{
    _Cgtdate2CMm2 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm2AsString()
{
    return _Cgtdate2CMm2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm2 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd2()
{
    return _Cgtdate2CDd2;
}

// Standard Setter
public void SetCgtdate2CDd2(int value)
{
    _Cgtdate2CDd2 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd2AsString()
{
    return _Cgtdate2CDd2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd2 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller366(string value)
{
    _Filler366.SetFiller366AsString(value);
}
// Nested Class: Filler366
public class Filler366
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm2 =0;
    
    
    
    
    // [DEBUG] Field: Filler367, is_external=, is_static_class=False, static_prefix=
    private string _Filler367 ="";
    
    
    
    
public Filler366() {}

public Filler366(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm2(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller367(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller366AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm2.ToString().PadLeft(6, '0'));
    result.Append(_Filler367.PadRight(2));
    
    return result.ToString();
}

public void SetFiller366AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm2(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller367(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm2()
{
    return _Cgtdate2CCcyymm2;
}

// Standard Setter
public void SetCgtdate2CCcyymm2(int value)
{
    _Cgtdate2CCcyymm2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm2AsString()
{
    return _Cgtdate2CCcyymm2.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm2 = parsed;
}

// Standard Getter
public string GetFiller367()
{
    return _Filler367;
}

// Standard Setter
public void SetFiller367(string value)
{
    _Filler367 = value;
}

// Get<>AsString()
public string GetFiller367AsString()
{
    return _Filler367.PadRight(2);
}

// Set<>AsString()
public void SetFiller367AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler367 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}