using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// <Section> Class for Ivar
public class Ivar
{
public Ivar() {}

// Fields in the class


// [DEBUG] Field: CgtglrepLinkage, is_external=, is_static_class=False, static_prefix=
private CgtglrepLinkage _CgtglrepLinkage = new CgtglrepLinkage();




// Getter and Setter methods

// Standard Getter
public CgtglrepLinkage GetCgtglrepLinkage()
{
    return _CgtglrepLinkage;
}

// Standard Setter
public void SetCgtglrepLinkage(CgtglrepLinkage value)
{
    _CgtglrepLinkage = value;
}

// Get<>AsString()
public string GetCgtglrepLinkageAsString()
{
    return _CgtglrepLinkage != null ? _CgtglrepLinkage.GetCgtglrepLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtglrepLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtglrepLinkage == null)
    {
        _CgtglrepLinkage = new CgtglrepLinkage();
    }
    _CgtglrepLinkage.SetCgtglrepLinkageAsString(value);
}


public override string ToString()
{
    return string.Join("; ", new string[]
    {        "CgtglrepLinkage: " + GetCgtglrepLinkageAsString()    });
}}}