using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtresDTO
{// DTO class representing D3Record Data Structure

public class D3Record
{
    private static int _size = 276;
    // [DEBUG] Class: D3Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D3Key, is_external=, is_static_class=False, static_prefix=
    private D3Key _D3Key = new D3Key();
    
    
    
    
    // [DEBUG] Field: D3Issuer, is_external=, is_static_class=False, static_prefix=
    private string _D3Issuer ="";
    
    
    
    
    // [DEBUG] Field: D3Description, is_external=, is_static_class=False, static_prefix=
    private string _D3Description ="";
    
    
    
    
    // [DEBUG] Field: D3SecurityShortName, is_external=, is_static_class=False, static_prefix=
    private string _D3SecurityShortName ="";
    
    
    
    
    // [DEBUG] Field: D3SortCode, is_external=, is_static_class=False, static_prefix=
    private string _D3SortCode ="";
    
    
    
    
    // [DEBUG] Field: D3Comments, is_external=, is_static_class=False, static_prefix=
    private string _D3Comments ="";
    
    
    
    
    // [DEBUG] Field: D3SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D3SecurityType ="";
    
    
    
    
    // [DEBUG] Field: D3QuotedUnquoted, is_external=, is_static_class=False, static_prefix=
    private string _D3QuotedUnquoted ="";
    
    
    
    
    // [DEBUG] Field: D3CountryCode, is_external=, is_static_class=False, static_prefix=
    private string _D3CountryCode ="";
    
    
    
    
    // [DEBUG] Field: D3GroupCode, is_external=, is_static_class=False, static_prefix=
    private string _D3GroupCode ="";
    
    
    
    
    // [DEBUG] Field: D3IndustrialClass, is_external=, is_static_class=False, static_prefix=
    private int _D3IndustrialClass =0;
    
    
    
    
    // [DEBUG] Field: D3IssuedCapital, is_external=, is_static_class=False, static_prefix=
    private decimal _D3IssuedCapital =0;
    
    
    
    
    // [DEBUG] Field: D3UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D3UpdateCount =0;
    
    
    
    
    // [DEBUG] Field: D3InverseKey, is_external=, is_static_class=False, static_prefix=
    private D3InverseKey _D3InverseKey = new D3InverseKey();
    
    
    
    
    // [DEBUG] Field: D3PricePercentInd, is_external=, is_static_class=False, static_prefix=
    private string _D3PricePercentInd ="";
    
    
    
    
    // [DEBUG] Field: D3QualifyingScheme, is_external=, is_static_class=False, static_prefix=
    private string _D3QualifyingScheme ="";
    
    
    
    
    // [DEBUG] Field: D3S54Asset, is_external=, is_static_class=False, static_prefix=
    private string _D3S54Asset ="";
    
    
    
    
    // [DEBUG] Field: D3DateOfIssueR, is_external=, is_static_class=False, static_prefix=
    private D3DateOfIssueR _D3DateOfIssueR = new D3DateOfIssueR();
    
    
    
    
    // [DEBUG] Field: D3DateOfIssue, is_external=, is_static_class=False, static_prefix=
    private int _D3DateOfIssue =0;
    
    
    
    
    // [DEBUG] Field: D3IndexFromIssue, is_external=, is_static_class=False, static_prefix=
    private string _D3IndexFromIssue ="";
    
    
    
    
    // [DEBUG] Field: D3LrBasis, is_external=, is_static_class=False, static_prefix=
    private string _D3LrBasis ="";
    
    
    
    
    // [DEBUG] Field: D3MaturityDate, is_external=, is_static_class=False, static_prefix=
    private D3MaturityDate _D3MaturityDate = new D3MaturityDate();
    
    
    
    
    // [DEBUG] Field: D3ParValue, is_external=, is_static_class=False, static_prefix=
    private decimal _D3ParValue =0;
    
    
    
    
    // [DEBUG] Field: Filler244, is_external=, is_static_class=False, static_prefix=
    private string _Filler244 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD3RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D3Key.GetD3KeyAsString());
        result.Append(_D3Issuer.PadRight(35));
        result.Append(_D3Description.PadRight(40));
        result.Append(_D3SecurityShortName.PadRight(18));
        result.Append(_D3SortCode.PadRight(15));
        result.Append(_D3Comments.PadRight(40));
        result.Append(_D3SecurityType.PadRight(1));
        result.Append(_D3QuotedUnquoted.PadRight(1));
        result.Append(_D3CountryCode.PadRight(3));
        result.Append(_D3GroupCode.PadRight(3));
        result.Append(_D3IndustrialClass.ToString().PadLeft(2, '0'));
        result.Append(_D3IssuedCapital.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D3UpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_D3InverseKey.GetD3InverseKeyAsString());
        result.Append(_D3PricePercentInd.PadRight(1));
        result.Append(_D3QualifyingScheme.PadRight(1));
        result.Append(_D3S54Asset.PadRight(1));
        result.Append(_D3DateOfIssueR.GetD3DateOfIssueRAsString());
        result.Append(_D3DateOfIssue.ToString().PadLeft(6, '0'));
        result.Append(_D3IndexFromIssue.PadRight(1));
        result.Append(_D3LrBasis.PadRight(1));
        result.Append(_D3MaturityDate.GetD3MaturityDateAsString());
        result.Append(_D3ParValue.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler244.PadRight(50));
        
        return result.ToString();
    }
    
    public void SetD3RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            _D3Key.SetD3KeyAsString(data.Substring(offset, 7));
        }
        else
        {
            _D3Key.SetD3KeyAsString(data.Substring(offset));
        }
        offset += 7;
        if (offset + 35 <= data.Length)
        {
            string extracted = data.Substring(offset, 35).Trim();
            SetD3Issuer(extracted);
        }
        offset += 35;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD3Description(extracted);
        }
        offset += 40;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetD3SecurityShortName(extracted);
        }
        offset += 18;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetD3SortCode(extracted);
        }
        offset += 15;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD3Comments(extracted);
        }
        offset += 40;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD3SecurityType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD3QuotedUnquoted(extracted);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD3CountryCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD3GroupCode(extracted);
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD3IndustrialClass(parsedInt);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD3IssuedCapital(parsedDec);
        }
        offset += 14;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD3UpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            _D3InverseKey.SetD3InverseKeyAsString(data.Substring(offset, 7));
        }
        else
        {
            _D3InverseKey.SetD3InverseKeyAsString(data.Substring(offset));
        }
        offset += 7;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD3PricePercentInd(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD3QualifyingScheme(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD3S54Asset(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            _D3DateOfIssueR.SetD3DateOfIssueRAsString(data.Substring(offset, 6));
        }
        else
        {
            _D3DateOfIssueR.SetD3DateOfIssueRAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD3DateOfIssue(parsedInt);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD3IndexFromIssue(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD3LrBasis(extracted);
        }
        offset += 1;
        if (offset + 8 <= data.Length)
        {
            _D3MaturityDate.SetD3MaturityDateAsString(data.Substring(offset, 8));
        }
        else
        {
            _D3MaturityDate.SetD3MaturityDateAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD3ParValue(parsedDec);
        }
        offset += 11;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller244(extracted);
        }
        offset += 50;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD3RecordAsString();
    }
    // Set<>String Override function
    public void SetD3Record(string value)
    {
        SetD3RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D3Key GetD3Key()
    {
        return _D3Key;
    }
    
    // Standard Setter
    public void SetD3Key(D3Key value)
    {
        _D3Key = value;
    }
    
    // Get<>AsString()
    public string GetD3KeyAsString()
    {
        return _D3Key != null ? _D3Key.GetD3KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD3KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D3Key == null)
        {
            _D3Key = new D3Key();
        }
        _D3Key.SetD3KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD3Issuer()
    {
        return _D3Issuer;
    }
    
    // Standard Setter
    public void SetD3Issuer(string value)
    {
        _D3Issuer = value;
    }
    
    // Get<>AsString()
    public string GetD3IssuerAsString()
    {
        return _D3Issuer.PadRight(35);
    }
    
    // Set<>AsString()
    public void SetD3IssuerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3Issuer = value;
    }
    
    // Standard Getter
    public string GetD3Description()
    {
        return _D3Description;
    }
    
    // Standard Setter
    public void SetD3Description(string value)
    {
        _D3Description = value;
    }
    
    // Get<>AsString()
    public string GetD3DescriptionAsString()
    {
        return _D3Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD3DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3Description = value;
    }
    
    // Standard Getter
    public string GetD3SecurityShortName()
    {
        return _D3SecurityShortName;
    }
    
    // Standard Setter
    public void SetD3SecurityShortName(string value)
    {
        _D3SecurityShortName = value;
    }
    
    // Get<>AsString()
    public string GetD3SecurityShortNameAsString()
    {
        return _D3SecurityShortName.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetD3SecurityShortNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3SecurityShortName = value;
    }
    
    // Standard Getter
    public string GetD3SortCode()
    {
        return _D3SortCode;
    }
    
    // Standard Setter
    public void SetD3SortCode(string value)
    {
        _D3SortCode = value;
    }
    
    // Get<>AsString()
    public string GetD3SortCodeAsString()
    {
        return _D3SortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetD3SortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3SortCode = value;
    }
    
    // Standard Getter
    public string GetD3Comments()
    {
        return _D3Comments;
    }
    
    // Standard Setter
    public void SetD3Comments(string value)
    {
        _D3Comments = value;
    }
    
    // Get<>AsString()
    public string GetD3CommentsAsString()
    {
        return _D3Comments.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD3CommentsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3Comments = value;
    }
    
    // Standard Getter
    public string GetD3SecurityType()
    {
        return _D3SecurityType;
    }
    
    // Standard Setter
    public void SetD3SecurityType(string value)
    {
        _D3SecurityType = value;
    }
    
    // Get<>AsString()
    public string GetD3SecurityTypeAsString()
    {
        return _D3SecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD3SecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3SecurityType = value;
    }
    
    // Standard Getter
    public string GetD3QuotedUnquoted()
    {
        return _D3QuotedUnquoted;
    }
    
    // Standard Setter
    public void SetD3QuotedUnquoted(string value)
    {
        _D3QuotedUnquoted = value;
    }
    
    // Get<>AsString()
    public string GetD3QuotedUnquotedAsString()
    {
        return _D3QuotedUnquoted.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD3QuotedUnquotedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3QuotedUnquoted = value;
    }
    
    // Standard Getter
    public string GetD3CountryCode()
    {
        return _D3CountryCode;
    }
    
    // Standard Setter
    public void SetD3CountryCode(string value)
    {
        _D3CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD3CountryCodeAsString()
    {
        return _D3CountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD3CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3CountryCode = value;
    }
    
    // Standard Getter
    public string GetD3GroupCode()
    {
        return _D3GroupCode;
    }
    
    // Standard Setter
    public void SetD3GroupCode(string value)
    {
        _D3GroupCode = value;
    }
    
    // Get<>AsString()
    public string GetD3GroupCodeAsString()
    {
        return _D3GroupCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD3GroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3GroupCode = value;
    }
    
    // Standard Getter
    public int GetD3IndustrialClass()
    {
        return _D3IndustrialClass;
    }
    
    // Standard Setter
    public void SetD3IndustrialClass(int value)
    {
        _D3IndustrialClass = value;
    }
    
    // Get<>AsString()
    public string GetD3IndustrialClassAsString()
    {
        return _D3IndustrialClass.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD3IndustrialClassAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D3IndustrialClass = parsed;
    }
    
    // Standard Getter
    public decimal GetD3IssuedCapital()
    {
        return _D3IssuedCapital;
    }
    
    // Standard Setter
    public void SetD3IssuedCapital(decimal value)
    {
        _D3IssuedCapital = value;
    }
    
    // Get<>AsString()
    public string GetD3IssuedCapitalAsString()
    {
        return _D3IssuedCapital.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD3IssuedCapitalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D3IssuedCapital = parsed;
    }
    
    // Standard Getter
    public int GetD3UpdateCount()
    {
        return _D3UpdateCount;
    }
    
    // Standard Setter
    public void SetD3UpdateCount(int value)
    {
        _D3UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD3UpdateCountAsString()
    {
        return _D3UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD3UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D3UpdateCount = parsed;
    }
    
    // Standard Getter
    public D3InverseKey GetD3InverseKey()
    {
        return _D3InverseKey;
    }
    
    // Standard Setter
    public void SetD3InverseKey(D3InverseKey value)
    {
        _D3InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD3InverseKeyAsString()
    {
        return _D3InverseKey != null ? _D3InverseKey.GetD3InverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD3InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D3InverseKey == null)
        {
            _D3InverseKey = new D3InverseKey();
        }
        _D3InverseKey.SetD3InverseKeyAsString(value);
    }
    
    // Standard Getter
    public string GetD3PricePercentInd()
    {
        return _D3PricePercentInd;
    }
    
    // Standard Setter
    public void SetD3PricePercentInd(string value)
    {
        _D3PricePercentInd = value;
    }
    
    // Get<>AsString()
    public string GetD3PricePercentIndAsString()
    {
        return _D3PricePercentInd.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD3PricePercentIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3PricePercentInd = value;
    }
    
    // Standard Getter
    public string GetD3QualifyingScheme()
    {
        return _D3QualifyingScheme;
    }
    
    // Standard Setter
    public void SetD3QualifyingScheme(string value)
    {
        _D3QualifyingScheme = value;
    }
    
    // Get<>AsString()
    public string GetD3QualifyingSchemeAsString()
    {
        return _D3QualifyingScheme.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD3QualifyingSchemeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3QualifyingScheme = value;
    }
    
    // Standard Getter
    public string GetD3S54Asset()
    {
        return _D3S54Asset;
    }
    
    // Standard Setter
    public void SetD3S54Asset(string value)
    {
        _D3S54Asset = value;
    }
    
    // Get<>AsString()
    public string GetD3S54AssetAsString()
    {
        return _D3S54Asset.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD3S54AssetAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3S54Asset = value;
    }
    
    // Standard Getter
    public D3DateOfIssueR GetD3DateOfIssueR()
    {
        return _D3DateOfIssueR;
    }
    
    // Standard Setter
    public void SetD3DateOfIssueR(D3DateOfIssueR value)
    {
        _D3DateOfIssueR = value;
    }
    
    // Get<>AsString()
    public string GetD3DateOfIssueRAsString()
    {
        return _D3DateOfIssueR != null ? _D3DateOfIssueR.GetD3DateOfIssueRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD3DateOfIssueRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D3DateOfIssueR == null)
        {
            _D3DateOfIssueR = new D3DateOfIssueR();
        }
        _D3DateOfIssueR.SetD3DateOfIssueRAsString(value);
    }
    
    // Standard Getter
    public int GetD3DateOfIssue()
    {
        return _D3DateOfIssue;
    }
    
    // Standard Setter
    public void SetD3DateOfIssue(int value)
    {
        _D3DateOfIssue = value;
    }
    
    // Get<>AsString()
    public string GetD3DateOfIssueAsString()
    {
        return _D3DateOfIssue.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD3DateOfIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D3DateOfIssue = parsed;
    }
    
    // Standard Getter
    public string GetD3IndexFromIssue()
    {
        return _D3IndexFromIssue;
    }
    
    // Standard Setter
    public void SetD3IndexFromIssue(string value)
    {
        _D3IndexFromIssue = value;
    }
    
    // Get<>AsString()
    public string GetD3IndexFromIssueAsString()
    {
        return _D3IndexFromIssue.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD3IndexFromIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3IndexFromIssue = value;
    }
    
    // Standard Getter
    public string GetD3LrBasis()
    {
        return _D3LrBasis;
    }
    
    // Standard Setter
    public void SetD3LrBasis(string value)
    {
        _D3LrBasis = value;
    }
    
    // Get<>AsString()
    public string GetD3LrBasisAsString()
    {
        return _D3LrBasis.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD3LrBasisAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3LrBasis = value;
    }
    
    // Standard Getter
    public D3MaturityDate GetD3MaturityDate()
    {
        return _D3MaturityDate;
    }
    
    // Standard Setter
    public void SetD3MaturityDate(D3MaturityDate value)
    {
        _D3MaturityDate = value;
    }
    
    // Get<>AsString()
    public string GetD3MaturityDateAsString()
    {
        return _D3MaturityDate != null ? _D3MaturityDate.GetD3MaturityDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD3MaturityDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D3MaturityDate == null)
        {
            _D3MaturityDate = new D3MaturityDate();
        }
        _D3MaturityDate.SetD3MaturityDateAsString(value);
    }
    
    // Standard Getter
    public decimal GetD3ParValue()
    {
        return _D3ParValue;
    }
    
    // Standard Setter
    public void SetD3ParValue(decimal value)
    {
        _D3ParValue = value;
    }
    
    // Get<>AsString()
    public string GetD3ParValueAsString()
    {
        return _D3ParValue.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD3ParValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D3ParValue = parsed;
    }
    
    // Standard Getter
    public string GetFiller244()
    {
        return _Filler244;
    }
    
    // Standard Setter
    public void SetFiller244(string value)
    {
        _Filler244 = value;
    }
    
    // Get<>AsString()
    public string GetFiller244AsString()
    {
        return _Filler244.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller244AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler244 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD3Key(string value)
    {
        _D3Key.SetD3KeyAsString(value);
    }
    // Nested Class: D3Key
    public class D3Key
    {
        private static int _size = 7;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D3SedolCode, is_external=, is_static_class=False, static_prefix=
        private string _D3SedolCode ="";
        
        
        
        
    public D3Key() {}
    
    public D3Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD3SedolCode(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetD3KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D3SedolCode.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD3KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD3SedolCode(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD3SedolCode()
    {
        return _D3SedolCode;
    }
    
    // Standard Setter
    public void SetD3SedolCode(string value)
    {
        _D3SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD3SedolCodeAsString()
    {
        return _D3SedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD3SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D3SedolCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD3InverseKey(string value)
{
    _D3InverseKey.SetD3InverseKeyAsString(value);
}
// Nested Class: D3InverseKey
public class D3InverseKey
{
    private static int _size = 7;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D3InverseSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D3InverseSedolCode ="";
    
    
    
    
public D3InverseKey() {}

public D3InverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD3InverseSedolCode(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetD3InverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D3InverseSedolCode.PadRight(7));
    
    return result.ToString();
}

public void SetD3InverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD3InverseSedolCode(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetD3InverseSedolCode()
{
    return _D3InverseSedolCode;
}

// Standard Setter
public void SetD3InverseSedolCode(string value)
{
    _D3InverseSedolCode = value;
}

// Get<>AsString()
public string GetD3InverseSedolCodeAsString()
{
    return _D3InverseSedolCode.PadRight(7);
}

// Set<>AsString()
public void SetD3InverseSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D3InverseSedolCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD3DateOfIssueR(string value)
{
    _D3DateOfIssueR.SetD3DateOfIssueRAsString(value);
}
// Nested Class: D3DateOfIssueR
public class D3DateOfIssueR
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D3DateOfIssueYy, is_external=, is_static_class=False, static_prefix=
    private int _D3DateOfIssueYy =0;
    
    
    
    
    // [DEBUG] Field: D3DateOfIssueMm, is_external=, is_static_class=False, static_prefix=
    private int _D3DateOfIssueMm =0;
    
    
    
    
    // [DEBUG] Field: D3DateOfIssueDd, is_external=, is_static_class=False, static_prefix=
    private int _D3DateOfIssueDd =0;
    
    
    
    
public D3DateOfIssueR() {}

public D3DateOfIssueR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD3DateOfIssueYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD3DateOfIssueMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD3DateOfIssueDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD3DateOfIssueRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D3DateOfIssueYy.ToString().PadLeft(2, '0'));
    result.Append(_D3DateOfIssueMm.ToString().PadLeft(2, '0'));
    result.Append(_D3DateOfIssueDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD3DateOfIssueRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD3DateOfIssueYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD3DateOfIssueMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD3DateOfIssueDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD3DateOfIssueYy()
{
    return _D3DateOfIssueYy;
}

// Standard Setter
public void SetD3DateOfIssueYy(int value)
{
    _D3DateOfIssueYy = value;
}

// Get<>AsString()
public string GetD3DateOfIssueYyAsString()
{
    return _D3DateOfIssueYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD3DateOfIssueYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D3DateOfIssueYy = parsed;
}

// Standard Getter
public int GetD3DateOfIssueMm()
{
    return _D3DateOfIssueMm;
}

// Standard Setter
public void SetD3DateOfIssueMm(int value)
{
    _D3DateOfIssueMm = value;
}

// Get<>AsString()
public string GetD3DateOfIssueMmAsString()
{
    return _D3DateOfIssueMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD3DateOfIssueMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D3DateOfIssueMm = parsed;
}

// Standard Getter
public int GetD3DateOfIssueDd()
{
    return _D3DateOfIssueDd;
}

// Standard Setter
public void SetD3DateOfIssueDd(int value)
{
    _D3DateOfIssueDd = value;
}

// Get<>AsString()
public string GetD3DateOfIssueDdAsString()
{
    return _D3DateOfIssueDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD3DateOfIssueDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D3DateOfIssueDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD3MaturityDate(string value)
{
    _D3MaturityDate.SetD3MaturityDateAsString(value);
}
// Nested Class: D3MaturityDate
public class D3MaturityDate
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D3MaturityDateCcyy, is_external=, is_static_class=False, static_prefix=
    private string _D3MaturityDateCcyy ="";
    
    
    
    
    // [DEBUG] Field: D3MaturityDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D3MaturityDateMm ="";
    
    
    
    
    // [DEBUG] Field: D3MaturityDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D3MaturityDateDd ="";
    
    
    
    
public D3MaturityDate() {}

public D3MaturityDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD3MaturityDateCcyy(data.Substring(offset, 4).Trim());
    offset += 4;
    SetD3MaturityDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD3MaturityDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD3MaturityDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D3MaturityDateCcyy.PadRight(4));
    result.Append(_D3MaturityDateMm.PadRight(2));
    result.Append(_D3MaturityDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD3MaturityDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD3MaturityDateCcyy(extracted);
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD3MaturityDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD3MaturityDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD3MaturityDateCcyy()
{
    return _D3MaturityDateCcyy;
}

// Standard Setter
public void SetD3MaturityDateCcyy(string value)
{
    _D3MaturityDateCcyy = value;
}

// Get<>AsString()
public string GetD3MaturityDateCcyyAsString()
{
    return _D3MaturityDateCcyy.PadRight(4);
}

// Set<>AsString()
public void SetD3MaturityDateCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D3MaturityDateCcyy = value;
}

// Standard Getter
public string GetD3MaturityDateMm()
{
    return _D3MaturityDateMm;
}

// Standard Setter
public void SetD3MaturityDateMm(string value)
{
    _D3MaturityDateMm = value;
}

// Get<>AsString()
public string GetD3MaturityDateMmAsString()
{
    return _D3MaturityDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD3MaturityDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D3MaturityDateMm = value;
}

// Standard Getter
public string GetD3MaturityDateDd()
{
    return _D3MaturityDateDd;
}

// Standard Setter
public void SetD3MaturityDateDd(string value)
{
    _D3MaturityDateDd = value;
}

// Get<>AsString()
public string GetD3MaturityDateDdAsString()
{
    return _D3MaturityDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD3MaturityDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D3MaturityDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}

}}