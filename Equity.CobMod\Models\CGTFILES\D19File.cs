using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D19File Data Structure

public class D19File
{
    private static int _size = 12;
    // [DEBUG] Class: D19File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler172, is_external=, is_static_class=False, static_prefix=
    private string _Filler172 ="$";
    
    
    
    
    // [DEBUG] Field: D19UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D19UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler173, is_external=, is_static_class=False, static_prefix=
    private string _Filler173 ="D5";
    
    
    
    
    // [DEBUG] Field: D19ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D19ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler174, is_external=, is_static_class=False, static_prefix=
    private string _Filler174 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD19FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler172.PadRight(1));
        result.Append(_D19UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler173.PadRight(2));
        result.Append(_D19ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler174.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD19FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller172(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD19UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller173(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD19ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller174(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD19FileAsString();
    }
    // Set<>String Override function
    public void SetD19File(string value)
    {
        SetD19FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller172()
    {
        return _Filler172;
    }
    
    // Standard Setter
    public void SetFiller172(string value)
    {
        _Filler172 = value;
    }
    
    // Get<>AsString()
    public string GetFiller172AsString()
    {
        return _Filler172.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller172AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler172 = value;
    }
    
    // Standard Getter
    public int GetD19UserNo()
    {
        return _D19UserNo;
    }
    
    // Standard Setter
    public void SetD19UserNo(int value)
    {
        _D19UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD19UserNoAsString()
    {
        return _D19UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD19UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D19UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller173()
    {
        return _Filler173;
    }
    
    // Standard Setter
    public void SetFiller173(string value)
    {
        _Filler173 = value;
    }
    
    // Get<>AsString()
    public string GetFiller173AsString()
    {
        return _Filler173.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller173AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler173 = value;
    }
    
    // Standard Getter
    public int GetD19ReportNo()
    {
        return _D19ReportNo;
    }
    
    // Standard Setter
    public void SetD19ReportNo(int value)
    {
        _D19ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD19ReportNoAsString()
    {
        return _D19ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD19ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D19ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller174()
    {
        return _Filler174;
    }
    
    // Standard Setter
    public void SetFiller174(string value)
    {
        _Filler174 = value;
    }
    
    // Get<>AsString()
    public string GetFiller174AsString()
    {
        return _Filler174.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller174AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler174 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}