using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D41RecEnd Data Structure

public class D41RecEnd
{
    private static int _size = 130;
    // [DEBUG] Class: D41RecEnd, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D41End, is_external=, is_static_class=False, static_prefix=
    private string _D41End ="";
    
    
    
    
    // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
    private string _Filler28 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD41RecEndAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D41End.PadRight(11));
        result.Append(_Filler28.PadRight(119));
        
        return result.ToString();
    }
    
    public void SetD41RecEndAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD41End(extracted);
        }
        offset += 11;
        if (offset + 119 <= data.Length)
        {
            string extracted = data.Substring(offset, 119).Trim();
            SetFiller28(extracted);
        }
        offset += 119;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD41RecEndAsString();
    }
    // Set<>String Override function
    public void SetD41RecEnd(string value)
    {
        SetD41RecEndAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD41End()
    {
        return _D41End;
    }
    
    // Standard Setter
    public void SetD41End(string value)
    {
        _D41End = value;
    }
    
    // Get<>AsString()
    public string GetD41EndAsString()
    {
        return _D41End.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD41EndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D41End = value;
    }
    
    // Standard Getter
    public string GetFiller28()
    {
        return _Filler28;
    }
    
    // Standard Setter
    public void SetFiller28(string value)
    {
        _Filler28 = value;
    }
    
    // Get<>AsString()
    public string GetFiller28AsString()
    {
        return _Filler28.PadRight(119);
    }
    
    // Set<>AsString()
    public void SetFiller28AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler28 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}