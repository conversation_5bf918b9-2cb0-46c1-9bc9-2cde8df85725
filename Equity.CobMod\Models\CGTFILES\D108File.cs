using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D108File Data Structure

public class D108File
{
    private static int _size = 12;
    // [DEBUG] Class: D108File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler309, is_external=, is_static_class=False, static_prefix=
    private string _Filler309 ="$";
    
    
    
    
    // [DEBUG] Field: D108UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D108UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler310, is_external=, is_static_class=False, static_prefix=
    private string _Filler310 ="TRC";
    
    
    
    
    // [DEBUG] Field: Filler311, is_external=, is_static_class=False, static_prefix=
    private string _Filler311 =".TXT";
    
    
    
    
    
    // Serialization methods
    public string GetD108FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler309.PadRight(1));
        result.Append(_D108UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler310.PadRight(3));
        result.Append(_Filler311.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD108FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller309(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD108UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetFiller310(extracted);
        }
        offset += 3;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller311(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD108FileAsString();
    }
    // Set<>String Override function
    public void SetD108File(string value)
    {
        SetD108FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller309()
    {
        return _Filler309;
    }
    
    // Standard Setter
    public void SetFiller309(string value)
    {
        _Filler309 = value;
    }
    
    // Get<>AsString()
    public string GetFiller309AsString()
    {
        return _Filler309.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller309AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler309 = value;
    }
    
    // Standard Getter
    public int GetD108UserNo()
    {
        return _D108UserNo;
    }
    
    // Standard Setter
    public void SetD108UserNo(int value)
    {
        _D108UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD108UserNoAsString()
    {
        return _D108UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD108UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D108UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller310()
    {
        return _Filler310;
    }
    
    // Standard Setter
    public void SetFiller310(string value)
    {
        _Filler310 = value;
    }
    
    // Get<>AsString()
    public string GetFiller310AsString()
    {
        return _Filler310.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetFiller310AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler310 = value;
    }
    
    // Standard Getter
    public string GetFiller311()
    {
        return _Filler311;
    }
    
    // Standard Setter
    public void SetFiller311(string value)
    {
        _Filler311 = value;
    }
    
    // Get<>AsString()
    public string GetFiller311AsString()
    {
        return _Filler311.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller311AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler311 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}