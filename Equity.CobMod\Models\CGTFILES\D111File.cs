using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D111File Data Structure

public class D111File
{
    private static int _size = 12;
    // [DEBUG] Class: D111File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler318, is_external=, is_static_class=False, static_prefix=
    private string _Filler318 ="$";
    
    
    
    
    // [DEBUG] Field: D111UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D111UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler319, is_external=, is_static_class=False, static_prefix=
    private string _Filler319 ="R5";
    
    
    
    
    // [DEBUG] Field: D111ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D111ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler320, is_external=, is_static_class=False, static_prefix=
    private string _Filler320 =".TAP";
    
    
    
    
    
    // Serialization methods
    public string GetD111FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler318.PadRight(1));
        result.Append(_D111UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler319.PadRight(2));
        result.Append(_D111ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler320.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD111FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller318(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD111UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller319(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD111ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller320(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD111FileAsString();
    }
    // Set<>String Override function
    public void SetD111File(string value)
    {
        SetD111FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller318()
    {
        return _Filler318;
    }
    
    // Standard Setter
    public void SetFiller318(string value)
    {
        _Filler318 = value;
    }
    
    // Get<>AsString()
    public string GetFiller318AsString()
    {
        return _Filler318.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller318AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler318 = value;
    }
    
    // Standard Getter
    public int GetD111UserNo()
    {
        return _D111UserNo;
    }
    
    // Standard Setter
    public void SetD111UserNo(int value)
    {
        _D111UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD111UserNoAsString()
    {
        return _D111UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD111UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D111UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller319()
    {
        return _Filler319;
    }
    
    // Standard Setter
    public void SetFiller319(string value)
    {
        _Filler319 = value;
    }
    
    // Get<>AsString()
    public string GetFiller319AsString()
    {
        return _Filler319.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller319AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler319 = value;
    }
    
    // Standard Getter
    public int GetD111ReportNo()
    {
        return _D111ReportNo;
    }
    
    // Standard Setter
    public void SetD111ReportNo(int value)
    {
        _D111ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD111ReportNoAsString()
    {
        return _D111ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD111ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D111ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller320()
    {
        return _Filler320;
    }
    
    // Standard Setter
    public void SetFiller320(string value)
    {
        _Filler320 = value;
    }
    
    // Get<>AsString()
    public string GetFiller320AsString()
    {
        return _Filler320.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller320AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler320 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}