using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D102File Data Structure

public class D102File
{
    private static int _size = 12;
    // [DEBUG] Class: D102File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler294, is_external=, is_static_class=False, static_prefix=
    private string _Filler294 ="$";
    
    
    
    
    // [DEBUG] Field: D102UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D102UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler295, is_external=, is_static_class=False, static_prefix=
    private string _Filler295 ="RQ";
    
    
    
    
    // [DEBUG] Field: D102ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D102ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler296, is_external=, is_static_class=False, static_prefix=
    private string _Filler296 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD102FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler294.PadRight(1));
        result.Append(_D102UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler295.PadRight(2));
        result.Append(_D102ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler296.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD102FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller294(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD102UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller295(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD102ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller296(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD102FileAsString();
    }
    // Set<>String Override function
    public void SetD102File(string value)
    {
        SetD102FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller294()
    {
        return _Filler294;
    }
    
    // Standard Setter
    public void SetFiller294(string value)
    {
        _Filler294 = value;
    }
    
    // Get<>AsString()
    public string GetFiller294AsString()
    {
        return _Filler294.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller294AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler294 = value;
    }
    
    // Standard Getter
    public int GetD102UserNo()
    {
        return _D102UserNo;
    }
    
    // Standard Setter
    public void SetD102UserNo(int value)
    {
        _D102UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD102UserNoAsString()
    {
        return _D102UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD102UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D102UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller295()
    {
        return _Filler295;
    }
    
    // Standard Setter
    public void SetFiller295(string value)
    {
        _Filler295 = value;
    }
    
    // Get<>AsString()
    public string GetFiller295AsString()
    {
        return _Filler295.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller295AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler295 = value;
    }
    
    // Standard Getter
    public int GetD102ReportNo()
    {
        return _D102ReportNo;
    }
    
    // Standard Setter
    public void SetD102ReportNo(int value)
    {
        _D102ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD102ReportNoAsString()
    {
        return _D102ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD102ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D102ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller296()
    {
        return _Filler296;
    }
    
    // Standard Setter
    public void SetFiller296(string value)
    {
        _Filler296 = value;
    }
    
    // Get<>AsString()
    public string GetFiller296AsString()
    {
        return _Filler296.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller296AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler296 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}