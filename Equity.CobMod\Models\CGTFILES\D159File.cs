using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D159File Data Structure

public class D159File
{
    private static int _size = 12;
    // [DEBUG] Class: D159File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler321, is_external=, is_static_class=False, static_prefix=
    private string _Filler321 ="$";
    
    
    
    
    // [DEBUG] Field: D159UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D159UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler322, is_external=, is_static_class=False, static_prefix=
    private string _Filler322 ="ALW.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD159FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler321.PadRight(1));
        result.Append(_D159UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler322.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD159FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller321(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD159UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller322(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD159FileAsString();
    }
    // Set<>String Override function
    public void SetD159File(string value)
    {
        SetD159FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller321()
    {
        return _Filler321;
    }
    
    // Standard Setter
    public void SetFiller321(string value)
    {
        _Filler321 = value;
    }
    
    // Get<>AsString()
    public string GetFiller321AsString()
    {
        return _Filler321.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller321AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler321 = value;
    }
    
    // Standard Getter
    public int GetD159UserNo()
    {
        return _D159UserNo;
    }
    
    // Standard Setter
    public void SetD159UserNo(int value)
    {
        _D159UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD159UserNoAsString()
    {
        return _D159UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD159UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D159UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller322()
    {
        return _Filler322;
    }
    
    // Standard Setter
    public void SetFiller322(string value)
    {
        _Filler322 = value;
    }
    
    // Get<>AsString()
    public string GetFiller322AsString()
    {
        return _Filler322.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller322AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler322 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}