using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// DTO class representing WsDescriptionTable Data Structure

public class WsDescriptionTable
{
    private static int _size = 40;
    // [DEBUG] Class: WsDescriptionTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WDescChar, is_external=, is_static_class=False, static_prefix=
    private string[] _WDescChar = new string[40];
    
    
    
    
    
    // Serialization methods
    public string GetWsDescriptionTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 40; i++)
        {
            result.Append(_WDescChar[i].PadRight(1));
        }
        
        return result.ToString();
    }
    
    public void SetWsDescriptionTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 40; i++)
        {
            if (offset + 1 > data.Length) break;
            string val = data.Substring(offset, 1);
            
            _WDescChar[i] = val.Trim();
            offset += 1;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsDescriptionTableAsString();
    }
    // Set<>String Override function
    public void SetWsDescriptionTable(string value)
    {
        SetWsDescriptionTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for WDescChar
    public string GetWDescCharAt(int index)
    {
        return _WDescChar[index];
    }
    
    public void SetWDescCharAt(int index, string value)
    {
        _WDescChar[index] = value;
    }
    
    public string GetWDescCharAsStringAt(int index)
    {
        return _WDescChar[index].PadRight(1);
    }
    
    public void SetWDescCharAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WDescChar[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWDescChar()
    {
        return _WDescChar != null && _WDescChar.Length > 0
        ? _WDescChar[0]
        : default(string);
    }
    
    public void SetWDescChar(string value)
    {
        if (_WDescChar == null || _WDescChar.Length == 0)
        _WDescChar = new string[1];
        _WDescChar[0] = value;
    }
    
    public string GetWDescCharAsString()
    {
        return _WDescChar != null && _WDescChar.Length > 0
        ? _WDescChar[0].ToString()
        : string.Empty;
    }
    
    public void SetWDescCharAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WDescChar == null || _WDescChar.Length == 0)
        _WDescChar = new string[1];
        
        _WDescChar[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}