using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D154Record Data Structure

public class D154Record
{
    private static int _size = 4;
    // [DEBUG] Class: D154Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D154Key, is_external=, is_static_class=False, static_prefix=
    private D154Key _D154Key = new D154Key();
    
    
    
    
    
    // Serialization methods
    public string GetD154RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D154Key.GetD154KeyAsString());
        
        return result.ToString();
    }
    
    public void SetD154RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _D154Key.SetD154KeyAsString(data.Substring(offset, 4));
        }
        else
        {
            _D154Key.SetD154KeyAsString(data.Substring(offset));
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD154RecordAsString();
    }
    // Set<>String Override function
    public void SetD154Record(string value)
    {
        SetD154RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D154Key GetD154Key()
    {
        return _D154Key;
    }
    
    // Standard Setter
    public void SetD154Key(D154Key value)
    {
        _D154Key = value;
    }
    
    // Get<>AsString()
    public string GetD154KeyAsString()
    {
        return _D154Key != null ? _D154Key.GetD154KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD154KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D154Key == null)
        {
            _D154Key = new D154Key();
        }
        _D154Key.SetD154KeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD154Key(string value)
    {
        _D154Key.SetD154KeyAsString(value);
    }
    // Nested Class: D154Key
    public class D154Key
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D154CalendarId, is_external=, is_static_class=False, static_prefix=
        private string _D154CalendarId ="";
        
        
        
        
        // [DEBUG] Field: D154MasterFileYear, is_external=, is_static_class=False, static_prefix=
        private int _D154MasterFileYear =0;
        
        
        
        
        // [DEBUG] Field: D154PeriodEndDate, is_external=, is_static_class=False, static_prefix=
        private string _D154PeriodEndDate ="";
        
        
        
        
    public D154Key() {}
    
    public D154Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD154CalendarId(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD154MasterFileYear(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        SetD154PeriodEndDate(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD154KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D154CalendarId.PadRight(0));
        result.Append(_D154MasterFileYear.ToString().PadLeft(4, '0'));
        result.Append(_D154PeriodEndDate.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD154KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD154CalendarId(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD154MasterFileYear(parsedInt);
        }
        offset += 4;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD154PeriodEndDate(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD154CalendarId()
    {
        return _D154CalendarId;
    }
    
    // Standard Setter
    public void SetD154CalendarId(string value)
    {
        _D154CalendarId = value;
    }
    
    // Get<>AsString()
    public string GetD154CalendarIdAsString()
    {
        return _D154CalendarId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD154CalendarIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D154CalendarId = value;
    }
    
    // Standard Getter
    public int GetD154MasterFileYear()
    {
        return _D154MasterFileYear;
    }
    
    // Standard Setter
    public void SetD154MasterFileYear(int value)
    {
        _D154MasterFileYear = value;
    }
    
    // Get<>AsString()
    public string GetD154MasterFileYearAsString()
    {
        return _D154MasterFileYear.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD154MasterFileYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D154MasterFileYear = parsed;
    }
    
    // Standard Getter
    public string GetD154PeriodEndDate()
    {
        return _D154PeriodEndDate;
    }
    
    // Standard Setter
    public void SetD154PeriodEndDate(string value)
    {
        _D154PeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetD154PeriodEndDateAsString()
    {
        return _D154PeriodEndDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD154PeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D154PeriodEndDate = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}