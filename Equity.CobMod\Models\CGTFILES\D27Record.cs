using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D27Record Data Structure

public class D27Record
{
    private static int _size = 133;
    // [DEBUG] Class: D27Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D27PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D27PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D27ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D27ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD27RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D27PrintControl.PadRight(1));
        result.Append(_D27ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD27RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD27PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD27ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD27RecordAsString();
    }
    // Set<>String Override function
    public void SetD27Record(string value)
    {
        SetD27RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD27PrintControl()
    {
        return _D27PrintControl;
    }
    
    // Standard Setter
    public void SetD27PrintControl(string value)
    {
        _D27PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD27PrintControlAsString()
    {
        return _D27PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD27PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D27PrintControl = value;
    }
    
    // Standard Getter
    public string GetD27ReportLine()
    {
        return _D27ReportLine;
    }
    
    // Standard Setter
    public void SetD27ReportLine(string value)
    {
        _D27ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD27ReportLineAsString()
    {
        return _D27ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD27ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D27ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}