using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D2Record Data Structure

public class D2Record
{
    private static int _size = 53;
    // [DEBUG] Class: D2Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D2Key, is_external=, is_static_class=False, static_prefix=
    private D2Key _D2Key = new D2Key();
    
    
    
    
    // [DEBUG] Field: D2GroupDescription, is_external=, is_static_class=False, static_prefix=
    private string _D2GroupDescription ="";
    
    
    
    
    // [DEBUG] Field: D2UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D2UpdateCount =0;
    
    
    
    
    // [DEBUG] Field: D2InverseKey, is_external=, is_static_class=False, static_prefix=
    private D2InverseKey _D2InverseKey = new D2InverseKey();
    
    
    
    
    
    // Serialization methods
    public string GetD2RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D2Key.GetD2KeyAsString());
        result.Append(_D2GroupDescription.PadRight(40));
        result.Append(_D2UpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_D2InverseKey.GetD2InverseKeyAsString());
        
        return result.ToString();
    }
    
    public void SetD2RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _D2Key.SetD2KeyAsString(data.Substring(offset, 6));
        }
        else
        {
            _D2Key.SetD2KeyAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD2GroupDescription(extracted);
        }
        offset += 40;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD2UpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            _D2InverseKey.SetD2InverseKeyAsString(data.Substring(offset, 3));
        }
        else
        {
            _D2InverseKey.SetD2InverseKeyAsString(data.Substring(offset));
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD2RecordAsString();
    }
    // Set<>String Override function
    public void SetD2Record(string value)
    {
        SetD2RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D2Key GetD2Key()
    {
        return _D2Key;
    }
    
    // Standard Setter
    public void SetD2Key(D2Key value)
    {
        _D2Key = value;
    }
    
    // Get<>AsString()
    public string GetD2KeyAsString()
    {
        return _D2Key != null ? _D2Key.GetD2KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD2KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D2Key == null)
        {
            _D2Key = new D2Key();
        }
        _D2Key.SetD2KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD2GroupDescription()
    {
        return _D2GroupDescription;
    }
    
    // Standard Setter
    public void SetD2GroupDescription(string value)
    {
        _D2GroupDescription = value;
    }
    
    // Get<>AsString()
    public string GetD2GroupDescriptionAsString()
    {
        return _D2GroupDescription.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD2GroupDescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D2GroupDescription = value;
    }
    
    // Standard Getter
    public int GetD2UpdateCount()
    {
        return _D2UpdateCount;
    }
    
    // Standard Setter
    public void SetD2UpdateCount(int value)
    {
        _D2UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD2UpdateCountAsString()
    {
        return _D2UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD2UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D2UpdateCount = parsed;
    }
    
    // Standard Getter
    public D2InverseKey GetD2InverseKey()
    {
        return _D2InverseKey;
    }
    
    // Standard Setter
    public void SetD2InverseKey(D2InverseKey value)
    {
        _D2InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD2InverseKeyAsString()
    {
        return _D2InverseKey != null ? _D2InverseKey.GetD2InverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD2InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D2InverseKey == null)
        {
            _D2InverseKey = new D2InverseKey();
        }
        _D2InverseKey.SetD2InverseKeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD2Key(string value)
    {
        _D2Key.SetD2KeyAsString(value);
    }
    // Nested Class: D2Key
    public class D2Key
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D2FullGroupCode, is_external=, is_static_class=False, static_prefix=
        private string _D2FullGroupCode ="";
        
        
        
        
        // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
        private D2Key.Filler1 _Filler1 = new D2Key.Filler1();
        
        
        
        
    public D2Key() {}
    
    public D2Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD2FullGroupCode(data.Substring(offset, 3).Trim());
        offset += 3;
        _Filler1.SetFiller1AsString(data.Substring(offset, Filler1.GetSize()));
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetD2KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D2FullGroupCode.PadRight(3));
        result.Append(_Filler1.GetFiller1AsString());
        
        return result.ToString();
    }
    
    public void SetD2KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD2FullGroupCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            _Filler1.SetFiller1AsString(data.Substring(offset, 3));
        }
        else
        {
            _Filler1.SetFiller1AsString(data.Substring(offset));
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD2FullGroupCode()
    {
        return _D2FullGroupCode;
    }
    
    // Standard Setter
    public void SetD2FullGroupCode(string value)
    {
        _D2FullGroupCode = value;
    }
    
    // Get<>AsString()
    public string GetD2FullGroupCodeAsString()
    {
        return _D2FullGroupCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD2FullGroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D2FullGroupCode = value;
    }
    
    // Standard Getter
    public Filler1 GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(Filler1 value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1 != null ? _Filler1.GetFiller1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler1 == null)
        {
            _Filler1 = new Filler1();
        }
        _Filler1.SetFiller1AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Filler1
    public class Filler1
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
        private string _Filler2 ="";
        
        
        
        
        // [DEBUG] Field: D2GroupCode, is_external=, is_static_class=False, static_prefix=
        private string _D2GroupCode ="";
        
        
        
        
    public Filler1() {}
    
    public Filler1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller2(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD2GroupCode(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetFiller1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler2.PadRight(1));
        result.Append(_D2GroupCode.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetFiller1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller2(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD2GroupCode(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller2()
    {
        return _Filler2;
    }
    
    // Standard Setter
    public void SetFiller2(string value)
    {
        _Filler2 = value;
    }
    
    // Get<>AsString()
    public string GetFiller2AsString()
    {
        return _Filler2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler2 = value;
    }
    
    // Standard Getter
    public string GetD2GroupCode()
    {
        return _D2GroupCode;
    }
    
    // Standard Setter
    public void SetD2GroupCode(string value)
    {
        _D2GroupCode = value;
    }
    
    // Get<>AsString()
    public string GetD2GroupCodeAsString()
    {
        return _D2GroupCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD2GroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D2GroupCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Set<>String Override function (Nested)
public void SetD2InverseKey(string value)
{
    _D2InverseKey.SetD2InverseKeyAsString(value);
}
// Nested Class: D2InverseKey
public class D2InverseKey
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D2InverseGroupCode, is_external=, is_static_class=False, static_prefix=
    private string _D2InverseGroupCode ="";
    
    
    
    
public D2InverseKey() {}

public D2InverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD2InverseGroupCode(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetD2InverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D2InverseGroupCode.PadRight(3));
    
    return result.ToString();
}

public void SetD2InverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetD2InverseGroupCode(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetD2InverseGroupCode()
{
    return _D2InverseGroupCode;
}

// Standard Setter
public void SetD2InverseGroupCode(string value)
{
    _D2InverseGroupCode = value;
}

// Get<>AsString()
public string GetD2InverseGroupCodeAsString()
{
    return _D2InverseGroupCode.PadRight(3);
}

// Set<>AsString()
public void SetD2InverseGroupCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D2InverseGroupCode = value;
}



public static int GetSize()
{
    return _size;
}

}

}}