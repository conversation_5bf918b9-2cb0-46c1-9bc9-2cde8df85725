using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D165File Data Structure

public class D165File
{
    private static int _size = 12;
    // [DEBUG] Class: D165File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler329, is_external=, is_static_class=False, static_prefix=
    private string _Filler329 ="$";
    
    
    
    
    // [DEBUG] Field: D165UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D165UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler330, is_external=, is_static_class=False, static_prefix=
    private string _Filler330 ="ICR.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD165FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler329.PadRight(1));
        result.Append(_D165UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler330.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD165FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller329(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD165UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller330(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD165FileAsString();
    }
    // Set<>String Override function
    public void SetD165File(string value)
    {
        SetD165FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller329()
    {
        return _Filler329;
    }
    
    // Standard Setter
    public void SetFiller329(string value)
    {
        _Filler329 = value;
    }
    
    // Get<>AsString()
    public string GetFiller329AsString()
    {
        return _Filler329.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller329AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler329 = value;
    }
    
    // Standard Getter
    public int GetD165UserNo()
    {
        return _D165UserNo;
    }
    
    // Standard Setter
    public void SetD165UserNo(int value)
    {
        _D165UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD165UserNoAsString()
    {
        return _D165UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD165UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D165UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller330()
    {
        return _Filler330;
    }
    
    // Standard Setter
    public void SetFiller330(string value)
    {
        _Filler330 = value;
    }
    
    // Get<>AsString()
    public string GetFiller330AsString()
    {
        return _Filler330.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller330AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler330 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}