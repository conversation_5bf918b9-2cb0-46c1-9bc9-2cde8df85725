using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D22File Data Structure

public class D22File
{
    private static int _size = 12;
    // [DEBUG] Class: D22File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler181, is_external=, is_static_class=False, static_prefix=
    private string _Filler181 ="$";
    
    
    
    
    // [DEBUG] Field: D22UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D22UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler182, is_external=, is_static_class=False, static_prefix=
    private string _Filler182 ="R6";
    
    
    
    
    // [DEBUG] Field: D22ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D22ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler183, is_external=, is_static_class=False, static_prefix=
    private string _Filler183 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD22FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler181.PadRight(1));
        result.Append(_D22UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler182.PadRight(2));
        result.Append(_D22ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler183.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD22FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller181(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD22UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller182(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD22ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller183(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD22FileAsString();
    }
    // Set<>String Override function
    public void SetD22File(string value)
    {
        SetD22FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller181()
    {
        return _Filler181;
    }
    
    // Standard Setter
    public void SetFiller181(string value)
    {
        _Filler181 = value;
    }
    
    // Get<>AsString()
    public string GetFiller181AsString()
    {
        return _Filler181.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller181AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler181 = value;
    }
    
    // Standard Getter
    public int GetD22UserNo()
    {
        return _D22UserNo;
    }
    
    // Standard Setter
    public void SetD22UserNo(int value)
    {
        _D22UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD22UserNoAsString()
    {
        return _D22UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD22UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D22UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller182()
    {
        return _Filler182;
    }
    
    // Standard Setter
    public void SetFiller182(string value)
    {
        _Filler182 = value;
    }
    
    // Get<>AsString()
    public string GetFiller182AsString()
    {
        return _Filler182.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller182AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler182 = value;
    }
    
    // Standard Getter
    public int GetD22ReportNo()
    {
        return _D22ReportNo;
    }
    
    // Standard Setter
    public void SetD22ReportNo(int value)
    {
        _D22ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD22ReportNoAsString()
    {
        return _D22ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD22ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D22ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller183()
    {
        return _Filler183;
    }
    
    // Standard Setter
    public void SetFiller183(string value)
    {
        _Filler183 = value;
    }
    
    // Get<>AsString()
    public string GetFiller183AsString()
    {
        return _Filler183.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller183AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler183 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}