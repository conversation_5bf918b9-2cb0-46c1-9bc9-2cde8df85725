using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D35Record Data Structure

public class D35Record
{
    private static int _size = 45;
    // [DEBUG] Class: D35Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D35Key, is_external=, is_static_class=False, static_prefix=
    private D35Key _D35Key = new D35Key();
    
    
    
    
    // [DEBUG] Field: D35RunDate, is_external=, is_static_class=False, static_prefix=
    private int _D35RunDate =0;
    
    
    
    
    // [DEBUG] Field: D35RunDesc, is_external=, is_static_class=False, static_prefix=
    private string _D35RunDesc ="";
    
    
    
    
    
    // Serialization methods
    public string GetD35RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D35Key.GetD35KeyAsString());
        result.Append(_D35RunDate.ToString().PadLeft(6, '0'));
        result.Append(_D35RunDesc.PadRight(25));
        
        return result.ToString();
    }
    
    public void SetD35RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 14 <= data.Length)
        {
            _D35Key.SetD35KeyAsString(data.Substring(offset, 14));
        }
        else
        {
            _D35Key.SetD35KeyAsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD35RunDate(parsedInt);
        }
        offset += 6;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetD35RunDesc(extracted);
        }
        offset += 25;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD35RecordAsString();
    }
    // Set<>String Override function
    public void SetD35Record(string value)
    {
        SetD35RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D35Key GetD35Key()
    {
        return _D35Key;
    }
    
    // Standard Setter
    public void SetD35Key(D35Key value)
    {
        _D35Key = value;
    }
    
    // Get<>AsString()
    public string GetD35KeyAsString()
    {
        return _D35Key != null ? _D35Key.GetD35KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD35KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D35Key == null)
        {
            _D35Key = new D35Key();
        }
        _D35Key.SetD35KeyAsString(value);
    }
    
    // Standard Getter
    public int GetD35RunDate()
    {
        return _D35RunDate;
    }
    
    // Standard Setter
    public void SetD35RunDate(int value)
    {
        _D35RunDate = value;
    }
    
    // Get<>AsString()
    public string GetD35RunDateAsString()
    {
        return _D35RunDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD35RunDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D35RunDate = parsed;
    }
    
    // Standard Getter
    public string GetD35RunDesc()
    {
        return _D35RunDesc;
    }
    
    // Standard Setter
    public void SetD35RunDesc(string value)
    {
        _D35RunDesc = value;
    }
    
    // Get<>AsString()
    public string GetD35RunDescAsString()
    {
        return _D35RunDesc.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetD35RunDescAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D35RunDesc = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD35Key(string value)
    {
        _D35Key.SetD35KeyAsString(value);
    }
    // Nested Class: D35Key
    public class D35Key
    {
        private static int _size = 14;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D35RunByUserNo, is_external=, is_static_class=False, static_prefix=
        private int _D35RunByUserNo =0;
        
        
        
        
        // [DEBUG] Field: D35RunType, is_external=, is_static_class=False, static_prefix=
        private string _D35RunType ="";
        
        
        
        
        // [DEBUG] Field: D35RunGenerationNo, is_external=, is_static_class=False, static_prefix=
        private int _D35RunGenerationNo =0;
        
        
        
        
    public D35Key() {}
    
    public D35Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD35RunByUserNo(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        SetD35RunType(data.Substring(offset, 8).Trim());
        offset += 8;
        SetD35RunGenerationNo(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD35KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D35RunByUserNo.ToString().PadLeft(4, '0'));
        result.Append(_D35RunType.PadRight(8));
        result.Append(_D35RunGenerationNo.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetD35KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD35RunByUserNo(parsedInt);
        }
        offset += 4;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD35RunType(extracted);
        }
        offset += 8;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD35RunGenerationNo(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD35RunByUserNo()
    {
        return _D35RunByUserNo;
    }
    
    // Standard Setter
    public void SetD35RunByUserNo(int value)
    {
        _D35RunByUserNo = value;
    }
    
    // Get<>AsString()
    public string GetD35RunByUserNoAsString()
    {
        return _D35RunByUserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD35RunByUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D35RunByUserNo = parsed;
    }
    
    // Standard Getter
    public string GetD35RunType()
    {
        return _D35RunType;
    }
    
    // Standard Setter
    public void SetD35RunType(string value)
    {
        _D35RunType = value;
    }
    
    // Get<>AsString()
    public string GetD35RunTypeAsString()
    {
        return _D35RunType.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD35RunTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D35RunType = value;
    }
    
    // Standard Getter
    public int GetD35RunGenerationNo()
    {
        return _D35RunGenerationNo;
    }
    
    // Standard Setter
    public void SetD35RunGenerationNo(int value)
    {
        _D35RunGenerationNo = value;
    }
    
    // Get<>AsString()
    public string GetD35RunGenerationNoAsString()
    {
        return _D35RunGenerationNo.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD35RunGenerationNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D35RunGenerationNo = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}