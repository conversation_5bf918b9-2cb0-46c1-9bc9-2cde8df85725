using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D20Record Data Structure

public class D20Record
{
    private static int _size = 120;
    // [DEBUG] Class: D20Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D20KeyRecord, is_external=, is_static_class=False, static_prefix=
    private D20KeyRecord _D20KeyRecord = new D20KeyRecord();
    
    
    
    
    
    // Serialization methods
    public string GetD20RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D20KeyRecord.GetD20KeyRecordAsString());
        
        return result.ToString();
    }
    
    public void SetD20RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 120 <= data.Length)
        {
            _D20KeyRecord.SetD20KeyRecordAsString(data.Substring(offset, 120));
        }
        else
        {
            _D20KeyRecord.SetD20KeyRecordAsString(data.Substring(offset));
        }
        offset += 120;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD20RecordAsString();
    }
    // Set<>String Override function
    public void SetD20Record(string value)
    {
        SetD20RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D20KeyRecord GetD20KeyRecord()
    {
        return _D20KeyRecord;
    }
    
    // Standard Setter
    public void SetD20KeyRecord(D20KeyRecord value)
    {
        _D20KeyRecord = value;
    }
    
    // Get<>AsString()
    public string GetD20KeyRecordAsString()
    {
        return _D20KeyRecord != null ? _D20KeyRecord.GetD20KeyRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20KeyRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20KeyRecord == null)
        {
            _D20KeyRecord = new D20KeyRecord();
        }
        _D20KeyRecord.SetD20KeyRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD20KeyRecord(string value)
    {
        _D20KeyRecord.SetD20KeyRecordAsString(value);
    }
    // Nested Class: D20KeyRecord
    public class D20KeyRecord
    {
        private static int _size = 120;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D20SplitKey01, is_external=, is_static_class=False, static_prefix=
        private D20KeyRecord.D20SplitKey01 _D20SplitKey01 = new D20KeyRecord.D20SplitKey01();
        
        
        
        
        // [DEBUG] Field: Filler14, is_external=, is_static_class=False, static_prefix=
        private string _Filler14 ="";
        
        
        
        
        // [DEBUG] Field: D20SplitKey02, is_external=, is_static_class=False, static_prefix=
        private D20KeyRecord.D20SplitKey02 _D20SplitKey02 = new D20KeyRecord.D20SplitKey02();
        
        
        
        
        // [DEBUG] Field: D20SplitKey04, is_external=, is_static_class=False, static_prefix=
        private D20KeyRecord.D20SplitKey04 _D20SplitKey04 = new D20KeyRecord.D20SplitKey04();
        
        
        
        
        // [DEBUG] Field: D20SplitKey06, is_external=, is_static_class=False, static_prefix=
        private D20KeyRecord.D20SplitKey06 _D20SplitKey06 = new D20KeyRecord.D20SplitKey06();
        
        
        
        
        // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
        private string _Filler15 ="";
        
        
        
        
        // [DEBUG] Field: D20SplitKey07, is_external=, is_static_class=False, static_prefix=
        private D20KeyRecord.D20SplitKey07 _D20SplitKey07 = new D20KeyRecord.D20SplitKey07();
        
        
        
        
        // [DEBUG] Field: D20SplitKey03, is_external=, is_static_class=False, static_prefix=
        private D20KeyRecord.D20SplitKey03 _D20SplitKey03 = new D20KeyRecord.D20SplitKey03();
        
        
        
        
        // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
        private string _Filler16 ="";
        
        
        
        
        // [DEBUG] Field: D20TrancheFlag, is_external=, is_static_class=False, static_prefix=
        private string _D20TrancheFlag ="";
        
        
        
        
        // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
        private string _Filler17 ="";
        
        
        
        
        // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
        private string _Filler18 ="";
        
        
        
        
        // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
        private string _Filler19 ="";
        
        
        
        
        // [DEBUG] Field: D20SplitKey05, is_external=, is_static_class=False, static_prefix=
        private D20KeyRecord.D20SplitKey05 _D20SplitKey05 = new D20KeyRecord.D20SplitKey05();
        
        
        
        
        // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
        private string _Filler20 ="";
        
        
        
        
        // [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
        private string _Filler21 ="";
        
        
        
        
        // [DEBUG] Field: Filler22, is_external=, is_static_class=False, static_prefix=
        private string _Filler22 ="";
        
        
        
        
        // [DEBUG] Field: Filler23, is_external=, is_static_class=False, static_prefix=
        private string _Filler23 ="";
        
        
        
        
        // [DEBUG] Field: Filler24, is_external=, is_static_class=False, static_prefix=
        private string _Filler24 ="";
        
        
        
        
    public D20KeyRecord() {}
    
    public D20KeyRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D20SplitKey01.SetD20SplitKey01AsString(data.Substring(offset, D20SplitKey01.GetSize()));
        offset += 1;
        SetFiller14(data.Substring(offset, 0).Trim());
        offset += 0;
        _D20SplitKey02.SetD20SplitKey02AsString(data.Substring(offset, D20SplitKey02.GetSize()));
        offset += 0;
        _D20SplitKey04.SetD20SplitKey04AsString(data.Substring(offset, D20SplitKey04.GetSize()));
        offset += 0;
        _D20SplitKey06.SetD20SplitKey06AsString(data.Substring(offset, D20SplitKey06.GetSize()));
        offset += 0;
        SetFiller15(data.Substring(offset, 110).Trim());
        offset += 110;
        _D20SplitKey07.SetD20SplitKey07AsString(data.Substring(offset, D20SplitKey07.GetSize()));
        offset += 9;
        _D20SplitKey03.SetD20SplitKey03AsString(data.Substring(offset, D20SplitKey03.GetSize()));
        offset += 0;
        SetFiller16(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD20TrancheFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller17(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller18(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller19(data.Substring(offset, 0).Trim());
        offset += 0;
        _D20SplitKey05.SetD20SplitKey05AsString(data.Substring(offset, D20SplitKey05.GetSize()));
        offset += 0;
        SetFiller20(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller21(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller22(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller23(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller24(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD20KeyRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D20SplitKey01.GetD20SplitKey01AsString());
        result.Append(_Filler14.PadRight(0));
        result.Append(_D20SplitKey02.GetD20SplitKey02AsString());
        result.Append(_D20SplitKey04.GetD20SplitKey04AsString());
        result.Append(_D20SplitKey06.GetD20SplitKey06AsString());
        result.Append(_Filler15.PadRight(110));
        result.Append(_D20SplitKey07.GetD20SplitKey07AsString());
        result.Append(_D20SplitKey03.GetD20SplitKey03AsString());
        result.Append(_Filler16.PadRight(0));
        result.Append(_D20TrancheFlag.PadRight(0));
        result.Append(_Filler17.PadRight(0));
        result.Append(_Filler18.PadRight(0));
        result.Append(_Filler19.PadRight(0));
        result.Append(_D20SplitKey05.GetD20SplitKey05AsString());
        result.Append(_Filler20.PadRight(0));
        result.Append(_Filler21.PadRight(0));
        result.Append(_Filler22.PadRight(0));
        result.Append(_Filler23.PadRight(0));
        result.Append(_Filler24.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD20KeyRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            _D20SplitKey01.SetD20SplitKey01AsString(data.Substring(offset, 1));
        }
        else
        {
            _D20SplitKey01.SetD20SplitKey01AsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller14(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D20SplitKey02.SetD20SplitKey02AsString(data.Substring(offset, 0));
        }
        else
        {
            _D20SplitKey02.SetD20SplitKey02AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D20SplitKey04.SetD20SplitKey04AsString(data.Substring(offset, 0));
        }
        else
        {
            _D20SplitKey04.SetD20SplitKey04AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D20SplitKey06.SetD20SplitKey06AsString(data.Substring(offset, 0));
        }
        else
        {
            _D20SplitKey06.SetD20SplitKey06AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 110 <= data.Length)
        {
            string extracted = data.Substring(offset, 110).Trim();
            SetFiller15(extracted);
        }
        offset += 110;
        if (offset + 9 <= data.Length)
        {
            _D20SplitKey07.SetD20SplitKey07AsString(data.Substring(offset, 9));
        }
        else
        {
            _D20SplitKey07.SetD20SplitKey07AsString(data.Substring(offset));
        }
        offset += 9;
        if (offset + 0 <= data.Length)
        {
            _D20SplitKey03.SetD20SplitKey03AsString(data.Substring(offset, 0));
        }
        else
        {
            _D20SplitKey03.SetD20SplitKey03AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller16(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD20TrancheFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller17(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller18(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller19(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D20SplitKey05.SetD20SplitKey05AsString(data.Substring(offset, 0));
        }
        else
        {
            _D20SplitKey05.SetD20SplitKey05AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller20(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller21(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller22(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller23(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller24(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D20SplitKey01 GetD20SplitKey01()
    {
        return _D20SplitKey01;
    }
    
    // Standard Setter
    public void SetD20SplitKey01(D20SplitKey01 value)
    {
        _D20SplitKey01 = value;
    }
    
    // Get<>AsString()
    public string GetD20SplitKey01AsString()
    {
        return _D20SplitKey01 != null ? _D20SplitKey01.GetD20SplitKey01AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20SplitKey01AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20SplitKey01 == null)
        {
            _D20SplitKey01 = new D20SplitKey01();
        }
        _D20SplitKey01.SetD20SplitKey01AsString(value);
    }
    
    // Standard Getter
    public string GetFiller14()
    {
        return _Filler14;
    }
    
    // Standard Setter
    public void SetFiller14(string value)
    {
        _Filler14 = value;
    }
    
    // Get<>AsString()
    public string GetFiller14AsString()
    {
        return _Filler14.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller14AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler14 = value;
    }
    
    // Standard Getter
    public D20SplitKey02 GetD20SplitKey02()
    {
        return _D20SplitKey02;
    }
    
    // Standard Setter
    public void SetD20SplitKey02(D20SplitKey02 value)
    {
        _D20SplitKey02 = value;
    }
    
    // Get<>AsString()
    public string GetD20SplitKey02AsString()
    {
        return _D20SplitKey02 != null ? _D20SplitKey02.GetD20SplitKey02AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20SplitKey02AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20SplitKey02 == null)
        {
            _D20SplitKey02 = new D20SplitKey02();
        }
        _D20SplitKey02.SetD20SplitKey02AsString(value);
    }
    
    // Standard Getter
    public D20SplitKey04 GetD20SplitKey04()
    {
        return _D20SplitKey04;
    }
    
    // Standard Setter
    public void SetD20SplitKey04(D20SplitKey04 value)
    {
        _D20SplitKey04 = value;
    }
    
    // Get<>AsString()
    public string GetD20SplitKey04AsString()
    {
        return _D20SplitKey04 != null ? _D20SplitKey04.GetD20SplitKey04AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20SplitKey04AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20SplitKey04 == null)
        {
            _D20SplitKey04 = new D20SplitKey04();
        }
        _D20SplitKey04.SetD20SplitKey04AsString(value);
    }
    
    // Standard Getter
    public D20SplitKey06 GetD20SplitKey06()
    {
        return _D20SplitKey06;
    }
    
    // Standard Setter
    public void SetD20SplitKey06(D20SplitKey06 value)
    {
        _D20SplitKey06 = value;
    }
    
    // Get<>AsString()
    public string GetD20SplitKey06AsString()
    {
        return _D20SplitKey06 != null ? _D20SplitKey06.GetD20SplitKey06AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20SplitKey06AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20SplitKey06 == null)
        {
            _D20SplitKey06 = new D20SplitKey06();
        }
        _D20SplitKey06.SetD20SplitKey06AsString(value);
    }
    
    // Standard Getter
    public string GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(string value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15.PadRight(110);
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler15 = value;
    }
    
    // Standard Getter
    public D20SplitKey07 GetD20SplitKey07()
    {
        return _D20SplitKey07;
    }
    
    // Standard Setter
    public void SetD20SplitKey07(D20SplitKey07 value)
    {
        _D20SplitKey07 = value;
    }
    
    // Get<>AsString()
    public string GetD20SplitKey07AsString()
    {
        return _D20SplitKey07 != null ? _D20SplitKey07.GetD20SplitKey07AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20SplitKey07AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20SplitKey07 == null)
        {
            _D20SplitKey07 = new D20SplitKey07();
        }
        _D20SplitKey07.SetD20SplitKey07AsString(value);
    }
    
    // Standard Getter
    public D20SplitKey03 GetD20SplitKey03()
    {
        return _D20SplitKey03;
    }
    
    // Standard Setter
    public void SetD20SplitKey03(D20SplitKey03 value)
    {
        _D20SplitKey03 = value;
    }
    
    // Get<>AsString()
    public string GetD20SplitKey03AsString()
    {
        return _D20SplitKey03 != null ? _D20SplitKey03.GetD20SplitKey03AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20SplitKey03AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20SplitKey03 == null)
        {
            _D20SplitKey03 = new D20SplitKey03();
        }
        _D20SplitKey03.SetD20SplitKey03AsString(value);
    }
    
    // Standard Getter
    public string GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(string value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler16 = value;
    }
    
    // Standard Getter
    public string GetD20TrancheFlag()
    {
        return _D20TrancheFlag;
    }
    
    // Standard Setter
    public void SetD20TrancheFlag(string value)
    {
        _D20TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD20TrancheFlagAsString()
    {
        return _D20TrancheFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD20TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D20TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(string value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler17 = value;
    }
    
    // Standard Getter
    public string GetFiller18()
    {
        return _Filler18;
    }
    
    // Standard Setter
    public void SetFiller18(string value)
    {
        _Filler18 = value;
    }
    
    // Get<>AsString()
    public string GetFiller18AsString()
    {
        return _Filler18.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller18AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler18 = value;
    }
    
    // Standard Getter
    public string GetFiller19()
    {
        return _Filler19;
    }
    
    // Standard Setter
    public void SetFiller19(string value)
    {
        _Filler19 = value;
    }
    
    // Get<>AsString()
    public string GetFiller19AsString()
    {
        return _Filler19.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller19AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler19 = value;
    }
    
    // Standard Getter
    public D20SplitKey05 GetD20SplitKey05()
    {
        return _D20SplitKey05;
    }
    
    // Standard Setter
    public void SetD20SplitKey05(D20SplitKey05 value)
    {
        _D20SplitKey05 = value;
    }
    
    // Get<>AsString()
    public string GetD20SplitKey05AsString()
    {
        return _D20SplitKey05 != null ? _D20SplitKey05.GetD20SplitKey05AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD20SplitKey05AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D20SplitKey05 == null)
        {
            _D20SplitKey05 = new D20SplitKey05();
        }
        _D20SplitKey05.SetD20SplitKey05AsString(value);
    }
    
    // Standard Getter
    public string GetFiller20()
    {
        return _Filler20;
    }
    
    // Standard Setter
    public void SetFiller20(string value)
    {
        _Filler20 = value;
    }
    
    // Get<>AsString()
    public string GetFiller20AsString()
    {
        return _Filler20.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller20AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler20 = value;
    }
    
    // Standard Getter
    public string GetFiller21()
    {
        return _Filler21;
    }
    
    // Standard Setter
    public void SetFiller21(string value)
    {
        _Filler21 = value;
    }
    
    // Get<>AsString()
    public string GetFiller21AsString()
    {
        return _Filler21.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller21AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler21 = value;
    }
    
    // Standard Getter
    public string GetFiller22()
    {
        return _Filler22;
    }
    
    // Standard Setter
    public void SetFiller22(string value)
    {
        _Filler22 = value;
    }
    
    // Get<>AsString()
    public string GetFiller22AsString()
    {
        return _Filler22.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller22AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler22 = value;
    }
    
    // Standard Getter
    public string GetFiller23()
    {
        return _Filler23;
    }
    
    // Standard Setter
    public void SetFiller23(string value)
    {
        _Filler23 = value;
    }
    
    // Get<>AsString()
    public string GetFiller23AsString()
    {
        return _Filler23.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller23AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler23 = value;
    }
    
    // Standard Getter
    public string GetFiller24()
    {
        return _Filler24;
    }
    
    // Standard Setter
    public void SetFiller24(string value)
    {
        _Filler24 = value;
    }
    
    // Get<>AsString()
    public string GetFiller24AsString()
    {
        return _Filler24.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller24AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler24 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D20SplitKey01
    public class D20SplitKey01
    {
        private static int _size = 1;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D20CurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _D20CurrencySort =0;
        
        
        // 88-level condition checks for D20CurrencySort
        public bool IsD20CurrencySterling()
        {
            if (this._D20CurrencySort == 0) return true;
            return false;
        }
        public bool IsD20CurrencyEuro()
        {
            if (this._D20CurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: D20CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D20CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D20CountryCode, is_external=, is_static_class=False, static_prefix=
        private string _D20CountryCode ="";
        
        
        
        
    public D20SplitKey01() {}
    
    public D20SplitKey01(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD20CurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetD20CoAcLk(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD20CountryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD20SplitKey01AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D20CurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_D20CoAcLk.PadRight(0));
        result.Append(_D20CountryCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD20SplitKey01AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD20CurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD20CoAcLk(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD20CountryCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD20CurrencySort()
    {
        return _D20CurrencySort;
    }
    
    // Standard Setter
    public void SetD20CurrencySort(int value)
    {
        _D20CurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetD20CurrencySortAsString()
    {
        return _D20CurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD20CurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D20CurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetD20CoAcLk()
    {
        return _D20CoAcLk;
    }
    
    // Standard Setter
    public void SetD20CoAcLk(string value)
    {
        _D20CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD20CoAcLkAsString()
    {
        return _D20CoAcLk.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD20CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D20CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD20CountryCode()
    {
        return _D20CountryCode;
    }
    
    // Standard Setter
    public void SetD20CountryCode(string value)
    {
        _D20CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD20CountryCodeAsString()
    {
        return _D20CountryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD20CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D20CountryCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D20SplitKey02
public class D20SplitKey02
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20MainGroup23, is_external=, is_static_class=False, static_prefix=
    private string _D20MainGroup23 ="";
    
    
    
    
    // [DEBUG] Field: D20SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D20SecurityType ="";
    
    
    
    
    // [DEBUG] Field: D20SecuritySortCode, is_external=, is_static_class=False, static_prefix=
    private string _D20SecuritySortCode ="";
    
    
    
    
    // [DEBUG] Field: D20SedolSort, is_external=, is_static_class=False, static_prefix=
    private string _D20SedolSort ="";
    
    
    
    
    // [DEBUG] Field: D20RecordType, is_external=, is_static_class=False, static_prefix=
    private string _D20RecordType ="";
    
    
    
    
public D20SplitKey02() {}

public D20SplitKey02(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD20MainGroup23(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20SecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20SecuritySortCode(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20SedolSort(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20RecordType(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD20SplitKey02AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20MainGroup23.PadRight(0));
    result.Append(_D20SecurityType.PadRight(0));
    result.Append(_D20SecuritySortCode.PadRight(0));
    result.Append(_D20SedolSort.PadRight(0));
    result.Append(_D20RecordType.PadRight(0));
    
    return result.ToString();
}

public void SetD20SplitKey02AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20MainGroup23(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20SecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20SecuritySortCode(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20SedolSort(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20RecordType(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD20MainGroup23()
{
    return _D20MainGroup23;
}

// Standard Setter
public void SetD20MainGroup23(string value)
{
    _D20MainGroup23 = value;
}

// Get<>AsString()
public string GetD20MainGroup23AsString()
{
    return _D20MainGroup23.PadRight(0);
}

// Set<>AsString()
public void SetD20MainGroup23AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20MainGroup23 = value;
}

// Standard Getter
public string GetD20SecurityType()
{
    return _D20SecurityType;
}

// Standard Setter
public void SetD20SecurityType(string value)
{
    _D20SecurityType = value;
}

// Get<>AsString()
public string GetD20SecurityTypeAsString()
{
    return _D20SecurityType.PadRight(0);
}

// Set<>AsString()
public void SetD20SecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20SecurityType = value;
}

// Standard Getter
public string GetD20SecuritySortCode()
{
    return _D20SecuritySortCode;
}

// Standard Setter
public void SetD20SecuritySortCode(string value)
{
    _D20SecuritySortCode = value;
}

// Get<>AsString()
public string GetD20SecuritySortCodeAsString()
{
    return _D20SecuritySortCode.PadRight(0);
}

// Set<>AsString()
public void SetD20SecuritySortCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20SecuritySortCode = value;
}

// Standard Getter
public string GetD20SedolSort()
{
    return _D20SedolSort;
}

// Standard Setter
public void SetD20SedolSort(string value)
{
    _D20SedolSort = value;
}

// Get<>AsString()
public string GetD20SedolSortAsString()
{
    return _D20SedolSort.PadRight(0);
}

// Set<>AsString()
public void SetD20SedolSortAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20SedolSort = value;
}

// Standard Getter
public string GetD20RecordType()
{
    return _D20RecordType;
}

// Standard Setter
public void SetD20RecordType(string value)
{
    _D20RecordType = value;
}

// Get<>AsString()
public string GetD20RecordTypeAsString()
{
    return _D20RecordType.PadRight(0);
}

// Set<>AsString()
public void SetD20RecordTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20RecordType = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D20SplitKey04
public class D20SplitKey04
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20AcquisitionDate, is_external=, is_static_class=False, static_prefix=
    private D20SplitKey04.D20AcquisitionDate _D20AcquisitionDate = new D20SplitKey04.D20AcquisitionDate();
    
    
    
    
public D20SplitKey04() {}

public D20SplitKey04(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D20AcquisitionDate.SetD20AcquisitionDateAsString(data.Substring(offset, D20AcquisitionDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD20SplitKey04AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20AcquisitionDate.GetD20AcquisitionDateAsString());
    
    return result.ToString();
}

public void SetD20SplitKey04AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        _D20AcquisitionDate.SetD20AcquisitionDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D20AcquisitionDate.SetD20AcquisitionDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public D20AcquisitionDate GetD20AcquisitionDate()
{
    return _D20AcquisitionDate;
}

// Standard Setter
public void SetD20AcquisitionDate(D20AcquisitionDate value)
{
    _D20AcquisitionDate = value;
}

// Get<>AsString()
public string GetD20AcquisitionDateAsString()
{
    return _D20AcquisitionDate != null ? _D20AcquisitionDate.GetD20AcquisitionDateAsString() : "";
}

// Set<>AsString()
public void SetD20AcquisitionDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D20AcquisitionDate == null)
    {
        _D20AcquisitionDate = new D20AcquisitionDate();
    }
    _D20AcquisitionDate.SetD20AcquisitionDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D20AcquisitionDate
public class D20AcquisitionDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20AcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D20AcquisitionDateYy ="";
    
    
    
    
    // [DEBUG] Field: D20AcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D20AcquisitionDateMm ="";
    
    
    
    
    // [DEBUG] Field: D20AcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D20AcquisitionDateDd ="";
    
    
    
    
public D20AcquisitionDate() {}

public D20AcquisitionDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD20AcquisitionDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20AcquisitionDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20AcquisitionDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD20AcquisitionDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20AcquisitionDateYy.PadRight(0));
    result.Append(_D20AcquisitionDateMm.PadRight(0));
    result.Append(_D20AcquisitionDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD20AcquisitionDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20AcquisitionDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20AcquisitionDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20AcquisitionDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD20AcquisitionDateYy()
{
    return _D20AcquisitionDateYy;
}

// Standard Setter
public void SetD20AcquisitionDateYy(string value)
{
    _D20AcquisitionDateYy = value;
}

// Get<>AsString()
public string GetD20AcquisitionDateYyAsString()
{
    return _D20AcquisitionDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD20AcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20AcquisitionDateYy = value;
}

// Standard Getter
public string GetD20AcquisitionDateMm()
{
    return _D20AcquisitionDateMm;
}

// Standard Setter
public void SetD20AcquisitionDateMm(string value)
{
    _D20AcquisitionDateMm = value;
}

// Get<>AsString()
public string GetD20AcquisitionDateMmAsString()
{
    return _D20AcquisitionDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD20AcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20AcquisitionDateMm = value;
}

// Standard Getter
public string GetD20AcquisitionDateDd()
{
    return _D20AcquisitionDateDd;
}

// Standard Setter
public void SetD20AcquisitionDateDd(string value)
{
    _D20AcquisitionDateDd = value;
}

// Get<>AsString()
public string GetD20AcquisitionDateDdAsString()
{
    return _D20AcquisitionDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD20AcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20AcquisitionDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D20SplitKey06
public class D20SplitKey06
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20TrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _D20TrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: D20LineNumber, is_external=, is_static_class=False, static_prefix=
    private string _D20LineNumber ="";
    
    
    
    
    // [DEBUG] Field: D20BdvIndicator, is_external=, is_static_class=False, static_prefix=
    private string _D20BdvIndicator ="";
    
    
    
    
public D20SplitKey06() {}

public D20SplitKey06(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD20TrancheContractNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20LineNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20BdvIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD20SplitKey06AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20TrancheContractNumber.PadRight(0));
    result.Append(_D20LineNumber.PadRight(0));
    result.Append(_D20BdvIndicator.PadRight(0));
    
    return result.ToString();
}

public void SetD20SplitKey06AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20TrancheContractNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20LineNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20BdvIndicator(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD20TrancheContractNumber()
{
    return _D20TrancheContractNumber;
}

// Standard Setter
public void SetD20TrancheContractNumber(string value)
{
    _D20TrancheContractNumber = value;
}

// Get<>AsString()
public string GetD20TrancheContractNumberAsString()
{
    return _D20TrancheContractNumber.PadRight(0);
}

// Set<>AsString()
public void SetD20TrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20TrancheContractNumber = value;
}

// Standard Getter
public string GetD20LineNumber()
{
    return _D20LineNumber;
}

// Standard Setter
public void SetD20LineNumber(string value)
{
    _D20LineNumber = value;
}

// Get<>AsString()
public string GetD20LineNumberAsString()
{
    return _D20LineNumber.PadRight(0);
}

// Set<>AsString()
public void SetD20LineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20LineNumber = value;
}

// Standard Getter
public string GetD20BdvIndicator()
{
    return _D20BdvIndicator;
}

// Standard Setter
public void SetD20BdvIndicator(string value)
{
    _D20BdvIndicator = value;
}

// Get<>AsString()
public string GetD20BdvIndicatorAsString()
{
    return _D20BdvIndicator.PadRight(0);
}

// Set<>AsString()
public void SetD20BdvIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20BdvIndicator = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D20SplitKey07
public class D20SplitKey07
{
    private static int _size = 9;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20SequenceNumber, is_external=, is_static_class=False, static_prefix=
    private int _D20SequenceNumber =0;
    
    
    
    
public D20SplitKey07() {}

public D20SplitKey07(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD20SequenceNumber(int.Parse(data.Substring(offset, 9).Trim()));
    offset += 9;
    
}

// Serialization methods
public string GetD20SplitKey07AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20SequenceNumber.ToString().PadLeft(9, '0'));
    
    return result.ToString();
}

public void SetD20SplitKey07AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD20SequenceNumber(parsedInt);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public int GetD20SequenceNumber()
{
    return _D20SequenceNumber;
}

// Standard Setter
public void SetD20SequenceNumber(int value)
{
    _D20SequenceNumber = value;
}

// Get<>AsString()
public string GetD20SequenceNumberAsString()
{
    return _D20SequenceNumber.ToString().PadLeft(9, '0');
}

// Set<>AsString()
public void SetD20SequenceNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D20SequenceNumber = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D20SplitKey03
public class D20SplitKey03
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20AcquisitionDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D20AcquisitionDateCc ="";
    
    
    
    
public D20SplitKey03() {}

public D20SplitKey03(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD20AcquisitionDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD20SplitKey03AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20AcquisitionDateCc.PadRight(0));
    
    return result.ToString();
}

public void SetD20SplitKey03AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20AcquisitionDateCc(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD20AcquisitionDateCc()
{
    return _D20AcquisitionDateCc;
}

// Standard Setter
public void SetD20AcquisitionDateCc(string value)
{
    _D20AcquisitionDateCc = value;
}

// Get<>AsString()
public string GetD20AcquisitionDateCcAsString()
{
    return _D20AcquisitionDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD20AcquisitionDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20AcquisitionDateCc = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D20SplitKey05
public class D20SplitKey05
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20TaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D20TaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: D20TaperDate, is_external=, is_static_class=False, static_prefix=
    private D20SplitKey05.D20TaperDate _D20TaperDate = new D20SplitKey05.D20TaperDate();
    
    
    
    
public D20SplitKey05() {}

public D20SplitKey05(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD20TaperDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    _D20TaperDate.SetD20TaperDateAsString(data.Substring(offset, D20TaperDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD20SplitKey05AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20TaperDateCc.PadRight(0));
    result.Append(_D20TaperDate.GetD20TaperDateAsString());
    
    return result.ToString();
}

public void SetD20SplitKey05AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20TaperDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _D20TaperDate.SetD20TaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D20TaperDate.SetD20TaperDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD20TaperDateCc()
{
    return _D20TaperDateCc;
}

// Standard Setter
public void SetD20TaperDateCc(string value)
{
    _D20TaperDateCc = value;
}

// Get<>AsString()
public string GetD20TaperDateCcAsString()
{
    return _D20TaperDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD20TaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20TaperDateCc = value;
}

// Standard Getter
public D20TaperDate GetD20TaperDate()
{
    return _D20TaperDate;
}

// Standard Setter
public void SetD20TaperDate(D20TaperDate value)
{
    _D20TaperDate = value;
}

// Get<>AsString()
public string GetD20TaperDateAsString()
{
    return _D20TaperDate != null ? _D20TaperDate.GetD20TaperDateAsString() : "";
}

// Set<>AsString()
public void SetD20TaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D20TaperDate == null)
    {
        _D20TaperDate = new D20TaperDate();
    }
    _D20TaperDate.SetD20TaperDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D20TaperDate
public class D20TaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D20TaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D20TaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: D20TaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D20TaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: D20TaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D20TaperDateDd ="";
    
    
    
    
public D20TaperDate() {}

public D20TaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD20TaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20TaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD20TaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD20TaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D20TaperDateYy.PadRight(0));
    result.Append(_D20TaperDateMm.PadRight(0));
    result.Append(_D20TaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD20TaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20TaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20TaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD20TaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD20TaperDateYy()
{
    return _D20TaperDateYy;
}

// Standard Setter
public void SetD20TaperDateYy(string value)
{
    _D20TaperDateYy = value;
}

// Get<>AsString()
public string GetD20TaperDateYyAsString()
{
    return _D20TaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD20TaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20TaperDateYy = value;
}

// Standard Getter
public string GetD20TaperDateMm()
{
    return _D20TaperDateMm;
}

// Standard Setter
public void SetD20TaperDateMm(string value)
{
    _D20TaperDateMm = value;
}

// Get<>AsString()
public string GetD20TaperDateMmAsString()
{
    return _D20TaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD20TaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20TaperDateMm = value;
}

// Standard Getter
public string GetD20TaperDateDd()
{
    return _D20TaperDateDd;
}

// Standard Setter
public void SetD20TaperDateDd(string value)
{
    _D20TaperDateDd = value;
}

// Get<>AsString()
public string GetD20TaperDateDdAsString()
{
    return _D20TaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD20TaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20TaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}