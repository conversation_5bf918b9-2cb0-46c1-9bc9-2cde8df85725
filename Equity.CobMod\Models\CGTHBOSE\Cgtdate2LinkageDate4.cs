using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgthboseDTO
{// DTO class representing Cgtdate2LinkageDate4 Data Structure

public class Cgtdate2LinkageDate4
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate4, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd4 =0;
    
    
    
    
    // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
    private Filler41 _Filler41 = new Filler41();
    
    
    
    
    // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
    private Filler42 _Filler42 = new Filler42();
    
    
    
    
    // [DEBUG] Field: Filler43, is_external=, is_static_class=False, static_prefix=
    private Filler43 _Filler43 = new Filler43();
    
    
    
    
    // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
    private Filler44 _Filler44 = new Filler44();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0'));
        result.Append(_Filler41.GetFiller41AsString());
        result.Append(_Filler42.GetFiller42AsString());
        result.Append(_Filler43.GetFiller43AsString());
        result.Append(_Filler44.GetFiller44AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd4(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler41.SetFiller41AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler41.SetFiller41AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler42.SetFiller42AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler42.SetFiller42AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler43.SetFiller43AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler43.SetFiller43AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler44.SetFiller44AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler44.SetFiller44AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate4AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate4(string value)
    {
        SetCgtdate2LinkageDate4AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd4()
    {
        return _Cgtdate2Ccyymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd4(int value)
    {
        _Cgtdate2Ccyymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd4AsString()
    {
        return _Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd4 = parsed;
    }
    
    // Standard Getter
    public Filler41 GetFiller41()
    {
        return _Filler41;
    }
    
    // Standard Setter
    public void SetFiller41(Filler41 value)
    {
        _Filler41 = value;
    }
    
    // Get<>AsString()
    public string GetFiller41AsString()
    {
        return _Filler41 != null ? _Filler41.GetFiller41AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller41AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler41 == null)
        {
            _Filler41 = new Filler41();
        }
        _Filler41.SetFiller41AsString(value);
    }
    
    // Standard Getter
    public Filler42 GetFiller42()
    {
        return _Filler42;
    }
    
    // Standard Setter
    public void SetFiller42(Filler42 value)
    {
        _Filler42 = value;
    }
    
    // Get<>AsString()
    public string GetFiller42AsString()
    {
        return _Filler42 != null ? _Filler42.GetFiller42AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller42AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler42 == null)
        {
            _Filler42 = new Filler42();
        }
        _Filler42.SetFiller42AsString(value);
    }
    
    // Standard Getter
    public Filler43 GetFiller43()
    {
        return _Filler43;
    }
    
    // Standard Setter
    public void SetFiller43(Filler43 value)
    {
        _Filler43 = value;
    }
    
    // Get<>AsString()
    public string GetFiller43AsString()
    {
        return _Filler43 != null ? _Filler43.GetFiller43AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller43AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler43 == null)
        {
            _Filler43 = new Filler43();
        }
        _Filler43.SetFiller43AsString(value);
    }
    
    // Standard Getter
    public Filler44 GetFiller44()
    {
        return _Filler44;
    }
    
    // Standard Setter
    public void SetFiller44(Filler44 value)
    {
        _Filler44 = value;
    }
    
    // Get<>AsString()
    public string GetFiller44AsString()
    {
        return _Filler44 != null ? _Filler44.GetFiller44AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller44AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler44 == null)
        {
            _Filler44 = new Filler44();
        }
        _Filler44.SetFiller44AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller41(string value)
    {
        _Filler41.SetFiller41AsString(value);
    }
    // Nested Class: Filler41
    public class Filler41
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd4, is_external=, is_static_class=False, static_prefix=
        private Filler41.Cgtdate2Yymmdd4 _Cgtdate2Yymmdd4 = new Filler41.Cgtdate2Yymmdd4();
        
        
        
        
    public Filler41() {}
    
    public Filler41(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, Cgtdate2Yymmdd4.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller41AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc4.PadRight(2));
        result.Append(_Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetFiller41AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc4(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc4()
    {
        return _Cgtdate2Cc4;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc4(string value)
    {
        _Cgtdate2Cc4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc4AsString()
    {
        return _Cgtdate2Cc4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd4 GetCgtdate2Yymmdd4()
    {
        return _Cgtdate2Yymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd4(Cgtdate2Yymmdd4 value)
    {
        _Cgtdate2Yymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd4AsString()
    {
        return _Cgtdate2Yymmdd4 != null ? _Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd4 == null)
        {
            _Cgtdate2Yymmdd4 = new Cgtdate2Yymmdd4();
        }
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd4
    public class Cgtdate2Yymmdd4
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd4, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd4.Cgtdate2Mmdd4 _Cgtdate2Mmdd4 = new Cgtdate2Yymmdd4.Cgtdate2Mmdd4();
        
        
        
        
    public Cgtdate2Yymmdd4() {}
    
    public Cgtdate2Yymmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, Cgtdate2Mmdd4.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy4.PadRight(2));
        result.Append(_Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy4(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy4()
    {
        return _Cgtdate2Yy4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy4(string value)
    {
        _Cgtdate2Yy4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy4AsString()
    {
        return _Cgtdate2Yy4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd4 GetCgtdate2Mmdd4()
    {
        return _Cgtdate2Mmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd4(Cgtdate2Mmdd4 value)
    {
        _Cgtdate2Mmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd4AsString()
    {
        return _Cgtdate2Mmdd4 != null ? _Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd4 == null)
        {
            _Cgtdate2Mmdd4 = new Cgtdate2Mmdd4();
        }
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd4
    public class Cgtdate2Mmdd4
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd4 ="";
        
        
        
        
    public Cgtdate2Mmdd4() {}
    
    public Cgtdate2Mmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm4(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd4(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm4.PadRight(2));
        result.Append(_Cgtdate2Dd4.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm4(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd4(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm4()
    {
        return _Cgtdate2Mm4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm4(string value)
    {
        _Cgtdate2Mm4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm4AsString()
    {
        return _Cgtdate2Mm4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm4 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd4()
    {
        return _Cgtdate2Dd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd4(string value)
    {
        _Cgtdate2Dd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd4AsString()
    {
        return _Cgtdate2Dd4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd4 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller42(string value)
{
    _Filler42.SetFiller42AsString(value);
}
// Nested Class: Filler42
public class Filler42
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd4 =0;
    
    
    
    
public Filler42() {}

public Filler42(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller42AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy4.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd4.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller42AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy4(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd4(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy4()
{
    return _Cgtdate2CCcyy4;
}

// Standard Setter
public void SetCgtdate2CCcyy4(int value)
{
    _Cgtdate2CCcyy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy4AsString()
{
    return _Cgtdate2CCcyy4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd4()
{
    return _Cgtdate2CMmdd4;
}

// Standard Setter
public void SetCgtdate2CMmdd4(int value)
{
    _Cgtdate2CMmdd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd4AsString()
{
    return _Cgtdate2CMmdd4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller43(string value)
{
    _Filler43.SetFiller43AsString(value);
}
// Nested Class: Filler43
public class Filler43
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd4 =0;
    
    
    
    
public Filler43() {}

public Filler43(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller43AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd4.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller43AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd4(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc4()
{
    return _Cgtdate2CCc4;
}

// Standard Setter
public void SetCgtdate2CCc4(int value)
{
    _Cgtdate2CCc4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc4AsString()
{
    return _Cgtdate2CCc4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc4 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy4()
{
    return _Cgtdate2CYy4;
}

// Standard Setter
public void SetCgtdate2CYy4(int value)
{
    _Cgtdate2CYy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy4AsString()
{
    return _Cgtdate2CYy4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm4()
{
    return _Cgtdate2CMm4;
}

// Standard Setter
public void SetCgtdate2CMm4(int value)
{
    _Cgtdate2CMm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm4AsString()
{
    return _Cgtdate2CMm4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm4 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd4()
{
    return _Cgtdate2CDd4;
}

// Standard Setter
public void SetCgtdate2CDd4(int value)
{
    _Cgtdate2CDd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd4AsString()
{
    return _Cgtdate2CDd4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller44(string value)
{
    _Filler44.SetFiller44AsString(value);
}
// Nested Class: Filler44
public class Filler44
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm4 =0;
    
    
    
    
    // [DEBUG] Field: Filler45, is_external=, is_static_class=False, static_prefix=
    private string _Filler45 ="";
    
    
    
    
public Filler44() {}

public Filler44(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm4(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller45(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller44AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm4.ToString().PadLeft(6, '0'));
    result.Append(_Filler45.PadRight(2));
    
    return result.ToString();
}

public void SetFiller44AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm4(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller45(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm4()
{
    return _Cgtdate2CCcyymm4;
}

// Standard Setter
public void SetCgtdate2CCcyymm4(int value)
{
    _Cgtdate2CCcyymm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm4AsString()
{
    return _Cgtdate2CCcyymm4.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm4 = parsed;
}

// Standard Getter
public string GetFiller45()
{
    return _Filler45;
}

// Standard Setter
public void SetFiller45(string value)
{
    _Filler45 = value;
}

// Get<>AsString()
public string GetFiller45AsString()
{
    return _Filler45.PadRight(2);
}

// Set<>AsString()
public void SetFiller45AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler45 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}