using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D164File Data Structure

public class D164File
{
    private static int _size = 12;
    // [DEBUG] Class: D164File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler327, is_external=, is_static_class=False, static_prefix=
    private string _Filler327 ="$";
    
    
    
    
    // [DEBUG] Field: D164UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D164UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler328, is_external=, is_static_class=False, static_prefix=
    private string _Filler328 ="ICE.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD164FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler327.PadRight(1));
        result.Append(_D164UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler328.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD164FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller327(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD164UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller328(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD164FileAsString();
    }
    // Set<>String Override function
    public void SetD164File(string value)
    {
        SetD164FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller327()
    {
        return _Filler327;
    }
    
    // Standard Setter
    public void SetFiller327(string value)
    {
        _Filler327 = value;
    }
    
    // Get<>AsString()
    public string GetFiller327AsString()
    {
        return _Filler327.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller327AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler327 = value;
    }
    
    // Standard Getter
    public int GetD164UserNo()
    {
        return _D164UserNo;
    }
    
    // Standard Setter
    public void SetD164UserNo(int value)
    {
        _D164UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD164UserNoAsString()
    {
        return _D164UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD164UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D164UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller328()
    {
        return _Filler328;
    }
    
    // Standard Setter
    public void SetFiller328(string value)
    {
        _Filler328 = value;
    }
    
    // Get<>AsString()
    public string GetFiller328AsString()
    {
        return _Filler328.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller328AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler328 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}