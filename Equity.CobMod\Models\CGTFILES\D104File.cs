using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D104File Data Structure

public class D104File
{
    private static int _size = 12;
    // [DEBUG] Class: D104File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler300, is_external=, is_static_class=False, static_prefix=
    private string _Filler300 ="$";
    
    
    
    
    // [DEBUG] Field: D104UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D104UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler301, is_external=, is_static_class=False, static_prefix=
    private string _Filler301 ="RS";
    
    
    
    
    // [DEBUG] Field: D104ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D104ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler302, is_external=, is_static_class=False, static_prefix=
    private string _Filler302 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD104FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler300.PadRight(1));
        result.Append(_D104UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler301.PadRight(2));
        result.Append(_D104ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler302.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD104FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller300(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD104UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller301(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD104ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller302(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD104FileAsString();
    }
    // Set<>String Override function
    public void SetD104File(string value)
    {
        SetD104FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller300()
    {
        return _Filler300;
    }
    
    // Standard Setter
    public void SetFiller300(string value)
    {
        _Filler300 = value;
    }
    
    // Get<>AsString()
    public string GetFiller300AsString()
    {
        return _Filler300.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller300AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler300 = value;
    }
    
    // Standard Getter
    public int GetD104UserNo()
    {
        return _D104UserNo;
    }
    
    // Standard Setter
    public void SetD104UserNo(int value)
    {
        _D104UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD104UserNoAsString()
    {
        return _D104UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD104UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D104UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller301()
    {
        return _Filler301;
    }
    
    // Standard Setter
    public void SetFiller301(string value)
    {
        _Filler301 = value;
    }
    
    // Get<>AsString()
    public string GetFiller301AsString()
    {
        return _Filler301.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller301AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler301 = value;
    }
    
    // Standard Getter
    public int GetD104ReportNo()
    {
        return _D104ReportNo;
    }
    
    // Standard Setter
    public void SetD104ReportNo(int value)
    {
        _D104ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD104ReportNoAsString()
    {
        return _D104ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD104ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D104ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller302()
    {
        return _Filler302;
    }
    
    // Standard Setter
    public void SetFiller302(string value)
    {
        _Filler302 = value;
    }
    
    // Get<>AsString()
    public string GetFiller302AsString()
    {
        return _Filler302.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller302AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler302 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}