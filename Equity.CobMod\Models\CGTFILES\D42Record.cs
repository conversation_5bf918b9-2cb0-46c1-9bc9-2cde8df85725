using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D42Record Data Structure

public class D42Record
{
    private static int _size = 51;
    // [DEBUG] Class: D42Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D42Key, is_external=, is_static_class=False, static_prefix=
    private D42Key _D42Key = new D42Key();
    
    
    
    
    // [DEBUG] Field: D42SwiftCode, is_external=, is_static_class=False, static_prefix=
    private string _D42SwiftCode ="";
    
    
    
    
    // [DEBUG] Field: D42XshareCurrencyCode, is_external=, is_static_class=False, static_prefix=
    private string _D42XshareCurrencyCode ="";
    
    
    
    
    // [DEBUG] Field: D42XshareFormat, is_external=, is_static_class=False, static_prefix=
    private int _D42XshareFormat =0;
    
    
    
    
    // [DEBUG] Field: D42Scaler, is_external=, is_static_class=False, static_prefix=
    private int _D42Scaler =0;
    
    
    
    
    // [DEBUG] Field: D42Description, is_external=, is_static_class=False, static_prefix=
    private string _D42Description ="";
    
    
    
    
    
    // Serialization methods
    public string GetD42RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D42Key.GetD42KeyAsString());
        result.Append(_D42SwiftCode.PadRight(3));
        result.Append(_D42XshareCurrencyCode.PadRight(3));
        result.Append(_D42XshareFormat.ToString().PadLeft(1, '0'));
        result.Append(_D42Scaler.ToString().PadLeft(1, '0'));
        result.Append(_D42Description.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetD42RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            _D42Key.SetD42KeyAsString(data.Substring(offset, 3));
        }
        else
        {
            _D42Key.SetD42KeyAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD42SwiftCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD42XshareCurrencyCode(extracted);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD42XshareFormat(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD42Scaler(parsedInt);
        }
        offset += 1;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD42Description(extracted);
        }
        offset += 40;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD42RecordAsString();
    }
    // Set<>String Override function
    public void SetD42Record(string value)
    {
        SetD42RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D42Key GetD42Key()
    {
        return _D42Key;
    }
    
    // Standard Setter
    public void SetD42Key(D42Key value)
    {
        _D42Key = value;
    }
    
    // Get<>AsString()
    public string GetD42KeyAsString()
    {
        return _D42Key != null ? _D42Key.GetD42KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD42KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D42Key == null)
        {
            _D42Key = new D42Key();
        }
        _D42Key.SetD42KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD42SwiftCode()
    {
        return _D42SwiftCode;
    }
    
    // Standard Setter
    public void SetD42SwiftCode(string value)
    {
        _D42SwiftCode = value;
    }
    
    // Get<>AsString()
    public string GetD42SwiftCodeAsString()
    {
        return _D42SwiftCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD42SwiftCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D42SwiftCode = value;
    }
    
    // Standard Getter
    public string GetD42XshareCurrencyCode()
    {
        return _D42XshareCurrencyCode;
    }
    
    // Standard Setter
    public void SetD42XshareCurrencyCode(string value)
    {
        _D42XshareCurrencyCode = value;
    }
    
    // Get<>AsString()
    public string GetD42XshareCurrencyCodeAsString()
    {
        return _D42XshareCurrencyCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD42XshareCurrencyCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D42XshareCurrencyCode = value;
    }
    
    // Standard Getter
    public int GetD42XshareFormat()
    {
        return _D42XshareFormat;
    }
    
    // Standard Setter
    public void SetD42XshareFormat(int value)
    {
        _D42XshareFormat = value;
    }
    
    // Get<>AsString()
    public string GetD42XshareFormatAsString()
    {
        return _D42XshareFormat.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD42XshareFormatAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D42XshareFormat = parsed;
    }
    
    // Standard Getter
    public int GetD42Scaler()
    {
        return _D42Scaler;
    }
    
    // Standard Setter
    public void SetD42Scaler(int value)
    {
        _D42Scaler = value;
    }
    
    // Get<>AsString()
    public string GetD42ScalerAsString()
    {
        return _D42Scaler.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD42ScalerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D42Scaler = parsed;
    }
    
    // Standard Getter
    public string GetD42Description()
    {
        return _D42Description;
    }
    
    // Standard Setter
    public void SetD42Description(string value)
    {
        _D42Description = value;
    }
    
    // Get<>AsString()
    public string GetD42DescriptionAsString()
    {
        return _D42Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD42DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D42Description = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD42Key(string value)
    {
        _D42Key.SetD42KeyAsString(value);
    }
    // Nested Class: D42Key
    public class D42Key
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D42XshareCcy, is_external=, is_static_class=False, static_prefix=
        private string _D42XshareCcy ="";
        
        
        
        
    public D42Key() {}
    
    public D42Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD42XshareCcy(data.Substring(offset, 3).Trim());
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetD42KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D42XshareCcy.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetD42KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD42XshareCcy(extracted);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD42XshareCcy()
    {
        return _D42XshareCcy;
    }
    
    // Standard Setter
    public void SetD42XshareCcy(string value)
    {
        _D42XshareCcy = value;
    }
    
    // Get<>AsString()
    public string GetD42XshareCcyAsString()
    {
        return _D42XshareCcy.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD42XshareCcyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D42XshareCcy = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}