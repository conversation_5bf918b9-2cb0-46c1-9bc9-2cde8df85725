using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgthboseDTO
{// DTO class representing Cgtdate2LinkageDate9 Data Structure

public class Cgtdate2LinkageDate9
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate9, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd9 =0;
    
    
    
    
    // [DEBUG] Field: Filler66, is_external=, is_static_class=False, static_prefix=
    private Filler66 _Filler66 = new Filler66();
    
    
    
    
    // [DEBUG] Field: Filler67, is_external=, is_static_class=False, static_prefix=
    private Filler67 _Filler67 = new Filler67();
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private Filler68 _Filler68 = new Filler68();
    
    
    
    
    // [DEBUG] Field: Filler69, is_external=, is_static_class=False, static_prefix=
    private Filler69 _Filler69 = new Filler69();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate9AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd9.ToString().PadLeft(8, '0'));
        result.Append(_Filler66.GetFiller66AsString());
        result.Append(_Filler67.GetFiller67AsString());
        result.Append(_Filler68.GetFiller68AsString());
        result.Append(_Filler69.GetFiller69AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate9AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd9(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler66.SetFiller66AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler66.SetFiller66AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler67.SetFiller67AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler67.SetFiller67AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler68.SetFiller68AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler68.SetFiller68AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler69.SetFiller69AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler69.SetFiller69AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate9AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate9(string value)
    {
        SetCgtdate2LinkageDate9AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd9()
    {
        return _Cgtdate2Ccyymmdd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd9(int value)
    {
        _Cgtdate2Ccyymmdd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd9AsString()
    {
        return _Cgtdate2Ccyymmdd9.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd9 = parsed;
    }
    
    // Standard Getter
    public Filler66 GetFiller66()
    {
        return _Filler66;
    }
    
    // Standard Setter
    public void SetFiller66(Filler66 value)
    {
        _Filler66 = value;
    }
    
    // Get<>AsString()
    public string GetFiller66AsString()
    {
        return _Filler66 != null ? _Filler66.GetFiller66AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller66AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler66 == null)
        {
            _Filler66 = new Filler66();
        }
        _Filler66.SetFiller66AsString(value);
    }
    
    // Standard Getter
    public Filler67 GetFiller67()
    {
        return _Filler67;
    }
    
    // Standard Setter
    public void SetFiller67(Filler67 value)
    {
        _Filler67 = value;
    }
    
    // Get<>AsString()
    public string GetFiller67AsString()
    {
        return _Filler67 != null ? _Filler67.GetFiller67AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller67AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler67 == null)
        {
            _Filler67 = new Filler67();
        }
        _Filler67.SetFiller67AsString(value);
    }
    
    // Standard Getter
    public Filler68 GetFiller68()
    {
        return _Filler68;
    }
    
    // Standard Setter
    public void SetFiller68(Filler68 value)
    {
        _Filler68 = value;
    }
    
    // Get<>AsString()
    public string GetFiller68AsString()
    {
        return _Filler68 != null ? _Filler68.GetFiller68AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller68AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler68 == null)
        {
            _Filler68 = new Filler68();
        }
        _Filler68.SetFiller68AsString(value);
    }
    
    // Standard Getter
    public Filler69 GetFiller69()
    {
        return _Filler69;
    }
    
    // Standard Setter
    public void SetFiller69(Filler69 value)
    {
        _Filler69 = value;
    }
    
    // Get<>AsString()
    public string GetFiller69AsString()
    {
        return _Filler69 != null ? _Filler69.GetFiller69AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller69AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler69 == null)
        {
            _Filler69 = new Filler69();
        }
        _Filler69.SetFiller69AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller66(string value)
    {
        _Filler66.SetFiller66AsString(value);
    }
    // Nested Class: Filler66
    public class Filler66
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc9 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd9, is_external=, is_static_class=False, static_prefix=
        private Filler66.Cgtdate2Yymmdd9 _Cgtdate2Yymmdd9 = new Filler66.Cgtdate2Yymmdd9();
        
        
        
        
    public Filler66() {}
    
    public Filler66(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc9(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(data.Substring(offset, Cgtdate2Yymmdd9.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller66AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc9.PadRight(2));
        result.Append(_Cgtdate2Yymmdd9.GetCgtdate2Yymmdd9AsString());
        
        return result.ToString();
    }
    
    public void SetFiller66AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc9(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc9()
    {
        return _Cgtdate2Cc9;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc9(string value)
    {
        _Cgtdate2Cc9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc9AsString()
    {
        return _Cgtdate2Cc9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc9 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd9 GetCgtdate2Yymmdd9()
    {
        return _Cgtdate2Yymmdd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd9(Cgtdate2Yymmdd9 value)
    {
        _Cgtdate2Yymmdd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd9AsString()
    {
        return _Cgtdate2Yymmdd9 != null ? _Cgtdate2Yymmdd9.GetCgtdate2Yymmdd9AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd9 == null)
        {
            _Cgtdate2Yymmdd9 = new Cgtdate2Yymmdd9();
        }
        _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd9
    public class Cgtdate2Yymmdd9
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy9 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd9, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd9.Cgtdate2Mmdd9 _Cgtdate2Mmdd9 = new Cgtdate2Yymmdd9.Cgtdate2Mmdd9();
        
        
        
        
    public Cgtdate2Yymmdd9() {}
    
    public Cgtdate2Yymmdd9(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy9(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(data.Substring(offset, Cgtdate2Mmdd9.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd9AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy9.PadRight(2));
        result.Append(_Cgtdate2Mmdd9.GetCgtdate2Mmdd9AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd9AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy9(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy9()
    {
        return _Cgtdate2Yy9;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy9(string value)
    {
        _Cgtdate2Yy9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy9AsString()
    {
        return _Cgtdate2Yy9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy9 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd9 GetCgtdate2Mmdd9()
    {
        return _Cgtdate2Mmdd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd9(Cgtdate2Mmdd9 value)
    {
        _Cgtdate2Mmdd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd9AsString()
    {
        return _Cgtdate2Mmdd9 != null ? _Cgtdate2Mmdd9.GetCgtdate2Mmdd9AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd9 == null)
        {
            _Cgtdate2Mmdd9 = new Cgtdate2Mmdd9();
        }
        _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd9
    public class Cgtdate2Mmdd9
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm9 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd9 ="";
        
        
        
        
    public Cgtdate2Mmdd9() {}
    
    public Cgtdate2Mmdd9(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm9(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd9(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd9AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm9.PadRight(2));
        result.Append(_Cgtdate2Dd9.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd9AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm9(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd9(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm9()
    {
        return _Cgtdate2Mm9;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm9(string value)
    {
        _Cgtdate2Mm9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm9AsString()
    {
        return _Cgtdate2Mm9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm9 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd9()
    {
        return _Cgtdate2Dd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd9(string value)
    {
        _Cgtdate2Dd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd9AsString()
    {
        return _Cgtdate2Dd9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd9 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller67(string value)
{
    _Filler67.SetFiller67AsString(value);
}
// Nested Class: Filler67
public class Filler67
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd9 =0;
    
    
    
    
public Filler67() {}

public Filler67(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy9(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd9(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller67AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy9.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd9.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller67AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy9(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd9(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy9()
{
    return _Cgtdate2CCcyy9;
}

// Standard Setter
public void SetCgtdate2CCcyy9(int value)
{
    _Cgtdate2CCcyy9 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy9AsString()
{
    return _Cgtdate2CCcyy9.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy9 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd9()
{
    return _Cgtdate2CMmdd9;
}

// Standard Setter
public void SetCgtdate2CMmdd9(int value)
{
    _Cgtdate2CMmdd9 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd9AsString()
{
    return _Cgtdate2CMmdd9.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd9 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller68(string value)
{
    _Filler68.SetFiller68AsString(value);
}
// Nested Class: Filler68
public class Filler68
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd9 =0;
    
    
    
    
public Filler68() {}

public Filler68(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller68AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc9.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy9.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm9.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd9.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller68AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc9(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy9(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm9(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd9(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc9()
{
    return _Cgtdate2CCc9;
}

// Standard Setter
public void SetCgtdate2CCc9(int value)
{
    _Cgtdate2CCc9 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc9AsString()
{
    return _Cgtdate2CCc9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc9 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy9()
{
    return _Cgtdate2CYy9;
}

// Standard Setter
public void SetCgtdate2CYy9(int value)
{
    _Cgtdate2CYy9 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy9AsString()
{
    return _Cgtdate2CYy9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy9 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm9()
{
    return _Cgtdate2CMm9;
}

// Standard Setter
public void SetCgtdate2CMm9(int value)
{
    _Cgtdate2CMm9 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm9AsString()
{
    return _Cgtdate2CMm9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm9 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd9()
{
    return _Cgtdate2CDd9;
}

// Standard Setter
public void SetCgtdate2CDd9(int value)
{
    _Cgtdate2CDd9 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd9AsString()
{
    return _Cgtdate2CDd9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd9 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller69(string value)
{
    _Filler69.SetFiller69AsString(value);
}
// Nested Class: Filler69
public class Filler69
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm9 =0;
    
    
    
    
    // [DEBUG] Field: Filler70, is_external=, is_static_class=False, static_prefix=
    private string _Filler70 ="";
    
    
    
    
public Filler69() {}

public Filler69(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm9(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller70(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller69AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm9.ToString().PadLeft(6, '0'));
    result.Append(_Filler70.PadRight(2));
    
    return result.ToString();
}

public void SetFiller69AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm9(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller70(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm9()
{
    return _Cgtdate2CCcyymm9;
}

// Standard Setter
public void SetCgtdate2CCcyymm9(int value)
{
    _Cgtdate2CCcyymm9 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm9AsString()
{
    return _Cgtdate2CCcyymm9.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm9 = parsed;
}

// Standard Getter
public string GetFiller70()
{
    return _Filler70;
}

// Standard Setter
public void SetFiller70(string value)
{
    _Filler70 = value;
}

// Get<>AsString()
public string GetFiller70AsString()
{
    return _Filler70.PadRight(2);
}

// Set<>AsString()
public void SetFiller70AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler70 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}