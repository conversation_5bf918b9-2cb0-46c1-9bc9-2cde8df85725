using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D103File Data Structure

public class D103File
{
    private static int _size = 12;
    // [DEBUG] Class: D103File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler297, is_external=, is_static_class=False, static_prefix=
    private string _Filler297 ="$";
    
    
    
    
    // [DEBUG] Field: D103UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D103UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler298, is_external=, is_static_class=False, static_prefix=
    private string _Filler298 ="RR";
    
    
    
    
    // [DEBUG] Field: D103ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D103ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler299, is_external=, is_static_class=False, static_prefix=
    private string _Filler299 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD103FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler297.PadRight(1));
        result.Append(_D103UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler298.PadRight(2));
        result.Append(_D103ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler299.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD103FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller297(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD103UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller298(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD103ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller299(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD103FileAsString();
    }
    // Set<>String Override function
    public void SetD103File(string value)
    {
        SetD103FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller297()
    {
        return _Filler297;
    }
    
    // Standard Setter
    public void SetFiller297(string value)
    {
        _Filler297 = value;
    }
    
    // Get<>AsString()
    public string GetFiller297AsString()
    {
        return _Filler297.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller297AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler297 = value;
    }
    
    // Standard Getter
    public int GetD103UserNo()
    {
        return _D103UserNo;
    }
    
    // Standard Setter
    public void SetD103UserNo(int value)
    {
        _D103UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD103UserNoAsString()
    {
        return _D103UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD103UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D103UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller298()
    {
        return _Filler298;
    }
    
    // Standard Setter
    public void SetFiller298(string value)
    {
        _Filler298 = value;
    }
    
    // Get<>AsString()
    public string GetFiller298AsString()
    {
        return _Filler298.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller298AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler298 = value;
    }
    
    // Standard Getter
    public int GetD103ReportNo()
    {
        return _D103ReportNo;
    }
    
    // Standard Setter
    public void SetD103ReportNo(int value)
    {
        _D103ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD103ReportNoAsString()
    {
        return _D103ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD103ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D103ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller299()
    {
        return _Filler299;
    }
    
    // Standard Setter
    public void SetFiller299(string value)
    {
        _Filler299 = value;
    }
    
    // Get<>AsString()
    public string GetFiller299AsString()
    {
        return _Filler299.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller299AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler299 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}