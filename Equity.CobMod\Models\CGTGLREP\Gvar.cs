using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// <Section> Class for Gvar
public class Gvar
{
public Gvar() {}

// Fields in the class


// [DEBUG] Field: ProgramName, is_external=, is_static_class=False, static_prefix=
private string _ProgramName ="CGTGLREP.CBL";




// [DEBUG] Field: VersionNumber, is_external=, is_static_class=False, static_prefix=
private string _VersionNumber =" 1.0";




// [DEBUG] Field: WsProcessFlag, is_external=, is_static_class=False, static_prefix=
private string _WsProcessFlag =" ";


// 88-level condition checks for WsProcessFlag
public bool IsQuitProcess()
{
    if (this._WsProcessFlag == "'Q'") return true;
    return false;
}


// [DEBUG] Field: WsMasterFileAlreadyOpen, is_external=, is_static_class=False, static_prefix=
private string _WsMasterFileAlreadyOpen =" ";


// 88-level condition checks for WsMasterFileAlreadyOpen
public bool IsMasterFileAlreadyOpen()
{
    if (this._WsMasterFileAlreadyOpen == "'Y'") return true;
    return false;
}


// [DEBUG] Field: WsCompanyFileAlreadyOpen, is_external=, is_static_class=False, static_prefix=
private string _WsCompanyFileAlreadyOpen =" ";


// 88-level condition checks for WsCompanyFileAlreadyOpen
public bool IsCompanyFileAlreadyOpen()
{
    if (this._WsCompanyFileAlreadyOpen == "'Y'") return true;
    return false;
}


// [DEBUG] Field: WsTrcodesFileAlreadyOpen, is_external=, is_static_class=False, static_prefix=
private string _WsTrcodesFileAlreadyOpen =" ";


// 88-level condition checks for WsTrcodesFileAlreadyOpen
public bool IsTrcodesFileAlreadyOpen()
{
    if (this._WsTrcodesFileAlreadyOpen == "'Y'") return true;
    return false;
}


// [DEBUG] Field: WsSearchTrCode, is_external=, is_static_class=False, static_prefix=
private string _WsSearchTrCode ="";




// [DEBUG] Field: WPrevCurrencySort, is_external=, is_static_class=False, static_prefix=
private string _WPrevCurrencySort =" ";




// [DEBUG] Field: WCurrencyChanged, is_external=, is_static_class=False, static_prefix=
private string _WCurrencyChanged =" ";


// 88-level condition checks for WCurrencyChanged
public bool IsCurrencyChanged()
{
    if (this._WCurrencyChanged == "'Y'") return true;
    return false;
}
public bool IsCurrencyNotChanged()
{
    if (this._WCurrencyChanged == "'Y'") return true;
    return false;
}


// [DEBUG] Field: D94Record, is_external=, is_static_class=False, static_prefix=
private D94Record _D94Record = new D94Record();




// [DEBUG] Field: ElcggioLink1, is_external=, is_static_class=False, static_prefix=
private ElcggioLink1 _ElcggioLink1 = new ElcggioLink1();




// [DEBUG] Field: ElcggioLink2, is_external=, is_static_class=False, static_prefix=
private ElcggioLink2 _ElcggioLink2 = new ElcggioLink2();




// [DEBUG] Field: CommonLinkage, is_external=, is_static_class=False, static_prefix=
private CommonLinkage _CommonLinkage = new CommonLinkage();




// [DEBUG] Field: COUNTRY_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_FILE = "CGTCTRY ";




// [DEBUG] Field: GROUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_FILE = "CGTGRP  ";




// [DEBUG] Field: STOCK_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_FILE = "CGTSTK  ";




// [DEBUG] Field: FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_FILE = "M-U-FUND";




// [DEBUG] Field: RPI_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_FILE = "CGTRPI  ";




// [DEBUG] Field: PARAMETER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_FILE = "M-PARAM ";




// [DEBUG] Field: USER_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FILE = "M-USER  ";




// [DEBUG] Field: OUTPUT_LISTING, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LISTING = "M-OUTLST";




// [DEBUG] Field: MASTER_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_LOG_FILE = "M-LOG   ";




// [DEBUG] Field: REALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_DATA_FILE = "CGTDR   ";




// [DEBUG] Field: UNREALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_DATA_FILE = "CGTDU   ";




// [DEBUG] Field: NOTIONAL_SALE_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_DATA_FILE = "CGTDN   ";




// [DEBUG] Field: REALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_DATA_FILE = "CGTDT   ";




// [DEBUG] Field: UNREALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_DATA_FILE = "CGTDX   ";




// [DEBUG] Field: ERROR_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_DATA_FILE = "CGTERR  ";




// [DEBUG] Field: PRINTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_FILE = "M-PRINT ";




// [DEBUG] Field: STOCK_TYPE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_TYPE_FILE = "M-STOCK ";




// [DEBUG] Field: YE_REC2_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC2_DATA_FILE = "CGTYERR ";




// [DEBUG] Field: TRANSACTION_CODE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_CODE_FILE = "M-TRANS ";




// [DEBUG] Field: OUTPUT_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LOG_FILE = "M-OUTLOG";




// [DEBUG] Field: MESSAGE_FILE, is_external=, is_static_class=False, static_prefix=
public const string MESSAGE_FILE = "M-MESS  ";




// [DEBUG] Field: USER_FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUND_FILE = "CGTFUNDX";




// [DEBUG] Field: HELP_TEXT_FILE, is_external=, is_static_class=False, static_prefix=
public const string HELP_TEXT_FILE = "M-HELP  ";




// [DEBUG] Field: DEFAULT_ACCESS_FILE, is_external=, is_static_class=False, static_prefix=
public const string DEFAULT_ACCESS_FILE = "M-DEF-AC";




// [DEBUG] Field: ACCESS_PROFILE_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_PROFILE_FILE = "M-ACCESS";




// [DEBUG] Field: EXTEL_PRICES_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_PRICES_FILE = "M-EPRICE";




// [DEBUG] Field: EXTEL_CURRENCY_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_CURRENCY_FILE = "M-CURR  ";




// [DEBUG] Field: STOCK_PRICE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_PRICE_FILE = "M-SPRICE";




// [DEBUG] Field: EXTEL_TRANSMISSION_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_TRANSMISSION_FILE = "M-ETRANS";




// [DEBUG] Field: SEQ_BALANCE_FILE, is_external=, is_static_class=False, static_prefix=
public const string SEQ_BALANCE_FILE = "CGTMFO  ";




// [DEBUG] Field: TRANSACTION_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_FILE = "CGTTRANS";




// [DEBUG] Field: BACKUP_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_FILE = "M-BK-LOG";




// [DEBUG] Field: BACKUP_DETAILS_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_FILE = "M-BK-DET";




// [DEBUG] Field: STOCK_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_DATA_FILE = "M-ST-DAT";




// [DEBUG] Field: FUNDS_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_DATA_FILE = "M-FN-DAT";




// [DEBUG] Field: PRICE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_DATA_FILE = "M-PR-DAT";




// [DEBUG] Field: BALANCE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string BALANCE_LOAD_DATA_FILE = "M-BL-DAT";




// [DEBUG] Field: REPLACEMENT_ACQ_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_ACQ_FILE = "M-RP-ACQ";




// [DEBUG] Field: REPLACEMENT_DIS_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_DIS_FILE = "M-RP-DIS";




// [DEBUG] Field: REPORT_RUN_MSG_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORT_RUN_MSG_FILE = "CGTMSG  ";




// [DEBUG] Field: RCF_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_FILE = "CGTRCF  ";




// [DEBUG] Field: RPI_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_DATA_FILE = "M-RPIDAT";




// [DEBUG] Field: COUNTRY_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_DATA_FILE = "M-CY-DAT";




// [DEBUG] Field: GROUP_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_DATA_FILE = "M-GR-DAT";




// [DEBUG] Field: GAINLOSS_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE = "CGTGLDAT";




// [DEBUG] Field: ACQUISITION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACQUISITION_EXPORT_FILE = "MAEXPORT";




// [DEBUG] Field: TAPER_RATE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_RATE_FILE = "TAPERATE";




// [DEBUG] Field: ASSET_USAGE_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_USAGE_CALENDAR_FILE = "ASSETUC ";




// [DEBUG] Field: PERIOD_END_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_FILE = "PENDCAL ";




// [DEBUG] Field: PERIOD_END_CALENDAR_DATES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_DATES_FILE = "PENDCALD";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_FILE, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_FILE = "ICFUNDS ";




// [DEBUG] Field: PRICE_TYPES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_TYPES_FILE = "PRICETYP";




// [DEBUG] Field: PENDING_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_LOG_FILE = "PENDLOG ";




// [DEBUG] Field: PENDING_ITEMS_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_ITEMS_FILE = "PENDITEM";




// [DEBUG] Field: GAINLOSS_DATA_FILE_LR, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE_LR = "CGTGLDLR";




// [DEBUG] Field: CG01_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG01_REPORT_FILE = "M-CG01  ";




// [DEBUG] Field: CG02_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG02_REPORT_FILE = "M-CG02  ";




// [DEBUG] Field: CG03_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG03_REPORT_FILE = "M-CG03  ";




// [DEBUG] Field: ERROR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_REPORT_FILE = "M-ERR-RP";




// [DEBUG] Field: REALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_SCHEDULE_FILE = "CGTPR   ";




// [DEBUG] Field: UNREALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_SCHEDULE_FILE = "CGTPU   ";




// [DEBUG] Field: NOTIONAL_SALE_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_SCHEDULE_FILE = "CGTPN   ";




// [DEBUG] Field: REALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_SCHEDULE_FILE = "CGTPT   ";




// [DEBUG] Field: UNREALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_SCHEDULE_FILE = "CGTPX   ";




// [DEBUG] Field: OFFSHORE_INCOME_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OFFSHORE_INCOME_REPORT = "M-OF-REP";




// [DEBUG] Field: YE_REC_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC_REPORT_FILE = "M-YE-REC";




// [DEBUG] Field: YE_DEL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_DEL_REPORT_FILE = "M-YE-DEL";




// [DEBUG] Field: YE_CON_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_CON_REPORT_FILE = "M-YE-CON";




// [DEBUG] Field: YE_ERR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_ERR_REPORT_FILE = "M-YE-ERR";




// [DEBUG] Field: YE_BAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_BAL_REPORT_FILE = "M-YE-BAL";




// [DEBUG] Field: FOREIGN_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FOREIGN_EXTEL_REPORT = "M-EX-FOR";




// [DEBUG] Field: STERLING_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STERLING_EXTEL_REPORT = "M-EX-STL";




// [DEBUG] Field: CGTR04_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR04_REPORT_FILE = "M-CGTR04";




// [DEBUG] Field: CGTR05_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR05_REPORT_FILE = "M-CGTR05";




// [DEBUG] Field: STOCK_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_REPORT = "M-ST-REP";




// [DEBUG] Field: FUNDS_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_REPORT = "M-FN-REP";




// [DEBUG] Field: PRICE_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_REPORT = "M-PR-REP";




// [DEBUG] Field: SKAN1_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN1_REPORT = "M-SK1REP";




// [DEBUG] Field: SKAN2_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN2_REPORT = "M-SK2REP";




// [DEBUG] Field: NEW_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_REALISED_REPORT = "M-NRGREP";




// [DEBUG] Field: NEW_UNREALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_UNREALISED_REPORT = "M-NUGREP";




// [DEBUG] Field: MGM1_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string MGM1_REPORT_FILE = "M-MGMREP";




// [DEBUG] Field: CAPITAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CAPITAL_REPORT_FILE = "M-CAPREP";




// [DEBUG] Field: BALANCES_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string BALANCES_LOAD_REPORT = "M-BALREP";




// [DEBUG] Field: REPLACEMENT_RELIEF_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_RELIEF_REPORT = "M-RP-REP";




// [DEBUG] Field: RPI_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_REPORT = "M-RPIREP";




// [DEBUG] Field: COUNTRY_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_REPORT = "M-CY-REP";




// [DEBUG] Field: GROUP_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_REPORT = "M-GR-REP";




// [DEBUG] Field: GAINLOSS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_REPORT = "CGTGLREP";




// [DEBUG] Field: LOST_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOST_INDEXATION_REPORT = "M-ILOST ";




// [DEBUG] Field: LOSU_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOSU_INDEXATION_REPORT = "M-ILOSU ";




// [DEBUG] Field: REAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REAL_H_O_GAINS_REPORT = "M-R-HO-G";




// [DEBUG] Field: UNREAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string UNREAL_H_O_GAINS_REPORT = "M-U-HO-G";




// [DEBUG] Field: BATCH_RUN_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_RUN_LOG_FILE = "B-RUNLOG";




// [DEBUG] Field: BATCH_QUIT_RUN_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_QUIT_RUN_FILE = "BQUITRUN";




// [DEBUG] Field: TRACE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRACE_FILE = "M-TRACE ";




// [DEBUG] Field: ERROR_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_LOG_FILE = "M-ERRLOG";




// [DEBUG] Field: TAPER_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_REPORT_FILE = "TAPERREP";




// [DEBUG] Field: TAPER_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_EXPORT_FILE = "TAPEREXP";




// [DEBUG] Field: ALLOWANCES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string ALLOWANCES_FROM_DB_FILE = "ALLOWANC";




// [DEBUG] Field: LOSSES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string LOSSES_FROM_DB_FILE = "LOSSFMDB";




// [DEBUG] Field: DISPOSALS_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string DISPOSALS_FROM_DB_FILE = "OTHERDIS";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_EXPORT = "ICFUNDSE";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_REPORT = "ICFUNDSR";




// [DEBUG] Field: CGTR06_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR06_REPORT_FILE = "M-CGTR06";




// [DEBUG] Field: DAILY_TRANSACTION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string DAILY_TRANSACTION_EXPORT_FILE = "DAILYTRN";




// [DEBUG] Field: COUNTRY_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_BACKUP_FILE = "M-D01-BK";




// [DEBUG] Field: GROUP_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_BACKUP_FILE = "M-D02-BK";




// [DEBUG] Field: STOCK_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_BACKUP_FILE = "M-D03-BK";




// [DEBUG] Field: FUND_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_BACKUP_FILE = "M-D04-BK";




// [DEBUG] Field: RPI_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_BACKUP_FILE = "M-D07-BK";




// [DEBUG] Field: PARAMETER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_BACKUP_FILE = "M-D08-BK";




// [DEBUG] Field: USER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_BACKUP_FILE = "M-D09-BK";




// [DEBUG] Field: MASTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_BACKUP_FILE = "M-D13-BK";




// [DEBUG] Field: PRINTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_BACKUP_FILE = "M-D31-BK";




// [DEBUG] Field: OUT_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUT_LOG_BACKUP_FILE = "M-D35-BK";




// [DEBUG] Field: USER_FUNDS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUNDS_BACKUP_FILE = "M-D37-BK";




// [DEBUG] Field: ACCESS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_BACKUP_FILE = "M-D40-BK";




// [DEBUG] Field: PRICE_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_BACKUP_FILE = "M-D43-BK";




// [DEBUG] Field: BACKUP_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_BACKUP_FILE = "M-D47-BK";




// [DEBUG] Field: BACKUP_DETAILS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_BACKUP_FILE = "M-D48-BK";




// [DEBUG] Field: REPORTS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORTS_BACKUP_FILE = "M-REP-BK";




// [DEBUG] Field: RCF_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_BACKUP_FILE = "M-D86-BK";




// [DEBUG] Field: OPEN_OP, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OP = "OP ";




// [DEBUG] Field: OPEN_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT = "OI ";




// [DEBUG] Field: OPEN_INPUT_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT_REPORT = "OIR";




// [DEBUG] Field: OPEN_OUTPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OUTPUT = "OO ";




// [DEBUG] Field: OPEN_I_O, is_external=, is_static_class=False, static_prefix=
public const string OPEN_I_O = "IO ";




// [DEBUG] Field: OPEN_EXTEND, is_external=, is_static_class=False, static_prefix=
public const string OPEN_EXTEND = "OE ";




// [DEBUG] Field: OPEN_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_SINGLE_MASTER_FILE_INPUT = "OS ";




// [DEBUG] Field: CLOSE_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_SINGLE_MASTER_FILE_INPUT = "CS ";




// [DEBUG] Field: SET_MASTER_FILE_NAMES, is_external=, is_static_class=False, static_prefix=
public const string SET_MASTER_FILE_NAMES = "FN ";




// [DEBUG] Field: START_EQUAL, is_external=, is_static_class=False, static_prefix=
public const string START_EQUAL = "SE ";




// [DEBUG] Field: START_NOT_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN = "SN ";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY2, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY2 = "SN2";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY3, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY3 = "SN3";




// [DEBUG] Field: START_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_GREATER_THAN = "SG ";




// [DEBUG] Field: START_GT_INVERSE_KEY, is_external=, is_static_class=False, static_prefix=
public const string START_GT_INVERSE_KEY = "SGI";




// [DEBUG] Field: START_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_LESS_THAN = "SL ";




// [DEBUG] Field: START_NOT_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_GREATER_THAN = "SNG";




// [DEBUG] Field: READ_NEXT, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT = "RN ";




// [DEBUG] Field: READ_NEXT_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_WITH_LOCK = "RNL";




// [DEBUG] Field: READ_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_RECORD = "RD ";




// [DEBUG] Field: READ_REPORT, is_external=, is_static_class=False, static_prefix=
public const string READ_REPORT = "RDR";




// [DEBUG] Field: READ_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_WITH_LOCK = "RDL";




// [DEBUG] Field: READ_ON_KEY2, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY2 = "RD2";




// [DEBUG] Field: READ_ON_KEY3, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY3 = "RD3";




// [DEBUG] Field: READ_PREVIOUS_MASTER, is_external=, is_static_class=False, static_prefix=
public const string READ_PREVIOUS_MASTER = "RP ";




// [DEBUG] Field: WRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string WRITE_RECORD = "WR ";




// [DEBUG] Field: REWRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string REWRITE_RECORD = "RW ";




// [DEBUG] Field: DELETE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string DELETE_RECORD = "DE ";




// [DEBUG] Field: CLOSE_FILE, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_FILE = "CL ";




// [DEBUG] Field: CLOSE_REPORT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_REPORT = "CLR";




// [DEBUG] Field: CLOSE_ALL_FILES, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_ALL_FILES = "CA ";




// [DEBUG] Field: UNLOCK_RECORD, is_external=, is_static_class=False, static_prefix=
public const string UNLOCK_RECORD = "UN ";




// [DEBUG] Field: BUILD_MASTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string BUILD_MASTER_FILE = "BL ";




// [DEBUG] Field: GET_CALENDAR_DATES, is_external=, is_static_class=False, static_prefix=
public const string GET_CALENDAR_DATES = "GC ";




// [DEBUG] Field: SET_TRANS_EXPORTED_FLAGS, is_external=, is_static_class=False, static_prefix=
public const string SET_TRANS_EXPORTED_FLAGS = "TX ";




// [DEBUG] Field: READ_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_USER_FUND_RECORD = "RU ";




// [DEBUG] Field: READ_NEXT_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_USER_FUND_RECORD = "NU ";




// [DEBUG] Field: GET_CONFIG_VALUE, is_external=, is_static_class=False, static_prefix=
public const string GET_CONFIG_VALUE = "CV ";




// [DEBUG] Field: GET_REQUEST_OPTIONS, is_external=, is_static_class=False, static_prefix=
public const string GET_REQUEST_OPTIONS = "RO ";




// [DEBUG] Field: REMAP_C_F_TRANSACTIONS, is_external=, is_static_class=False, static_prefix=
public const string REMAP_C_F_TRANSACTIONS = "RT ";




// [DEBUG] Field: RETURN_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] RETURN_KEY = new byte[] { 0x30, 0x30 };




// [DEBUG] Field: ESC_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ESC_KEY = new byte[] { 0x31, 0x00 };




// [DEBUG] Field: F1_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F1_KEY = new byte[] { 0x31, 0x01 };




// [DEBUG] Field: F2_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F2_KEY = new byte[] { 0x31, 0x02 };




// [DEBUG] Field: F3_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F3_KEY = new byte[] { 0x31, 0x03 };




// [DEBUG] Field: F4_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F4_KEY = new byte[] { 0x31, 0x04 };




// [DEBUG] Field: F5_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F5_KEY = new byte[] { 0x31, 0x05 };




// [DEBUG] Field: F6_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F6_KEY = new byte[] { 0x31, 0x06 };




// [DEBUG] Field: F7_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F7_KEY = new byte[] { 0x31, 0x07 };




// [DEBUG] Field: F8_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F8_KEY = new byte[] { 0x31, 0x08 };




// [DEBUG] Field: F9_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F9_KEY = new byte[] { 0x31, 0x09 };




// [DEBUG] Field: F10_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F10_KEY = new byte[] { 0x31, 0x0A };




// [DEBUG] Field: CURSOR_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_UP = new byte[] { 0x31, 0x18 };




// [DEBUG] Field: CURSOR_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_DOWN = new byte[] { 0x31, 0x19 };




// [DEBUG] Field: CURSOR_LEFT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_LEFT = new byte[] { 0x31, 0x1B };




// [DEBUG] Field: CURSOR_RIGHT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_RIGHT = new byte[] { 0x31, 0x1A };




// [DEBUG] Field: PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_UP = new byte[] { 0x31, 0x1C };




// [DEBUG] Field: PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_DOWN = new byte[] { 0x31, 0x1D };




// [DEBUG] Field: ACCEPT_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_UP = new byte[] { 0x31, 0x35 };




// [DEBUG] Field: ACCEPT_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_DOWN = new byte[] { 0x31, 0x36 };




// [DEBUG] Field: INSERT_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] INSERT_KEY = new byte[] { 0x31, 0x1E };




// [DEBUG] Field: DELETE_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] DELETE_KEY = new byte[] { 0x31, 0x1F };




// [DEBUG] Field: F11_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F11_KEY = new byte[] { 0x31, 0x20 };




// [DEBUG] Field: F12_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F12_KEY = new byte[] { 0x31, 0x21 };




// [DEBUG] Field: HOME_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] HOME_KEY = new byte[] { 0x31, 0x22 };




// [DEBUG] Field: END_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] END_KEY = new byte[] { 0x31, 0x23 };




// [DEBUG] Field: CONTROL_HOME, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_HOME = new byte[] { 0x31, 0x24 };




// [DEBUG] Field: CONTROL_END, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_END = new byte[] { 0x31, 0x25 };




// [DEBUG] Field: CONTROL_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_UP = new byte[] { 0x31, 0x26 };




// [DEBUG] Field: CONTROL_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_DOWN = new byte[] { 0x31, 0x27 };




// [DEBUG] Field: FIRST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR = 1982;




// [DEBUG] Field: LAST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR = 2044;




// [DEBUG] Field: FIRST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR_YY = 82;




// [DEBUG] Field: LAST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR_YY = 44;




// [DEBUG] Field: FIRST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE = 19820101;




// [DEBUG] Field: LAST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE = 20441231;




// [DEBUG] Field: FIRST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE_YYMMDD = 820101;




// [DEBUG] Field: LAST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE_YYMMDD = 441231;




// [DEBUG] Field: CLIENT_PERIOD_END_DATE_97_98, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_END_DATE_97_98 = 19980405;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_98_99, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_98_99 = 19980406;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_00_01, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_00_01 = 20000406;




// [DEBUG] Field: REIT_PROCESSING_START_DATE, is_external=, is_static_class=False, static_prefix=
public const int REIT_PROCESSING_START_DATE = 20070101;




// [DEBUG] Field: CREATE_2008_POOL_DATE, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE = 20080406;




// [DEBUG] Field: CREATE_2008_POOL_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE_YYMMDD = 080406;




// [DEBUG] Field: FIRST_USABLE_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_USABLE_DATE = 450101;




// [DEBUG] Field: GROUP_NO_ACTION, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NO_ACTION = 1;




// [DEBUG] Field: GROUP_PRO_RATA, is_external=, is_static_class=False, static_prefix=
public const int GROUP_PRO_RATA = 2;




// [DEBUG] Field: GROUP_NON_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NON_OLAB_FUND_DEFAULT = 3;




// [DEBUG] Field: GROUP_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_OLAB_FUND_DEFAULT = 4;




// [DEBUG] Field: USE_EARLIER_PRICE_NONE, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_NONE = " ";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_DAY, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_DAY = "D";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_SESSION, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_SESSION = "S";




// [DEBUG] Field: D34Record, is_external=, is_static_class=False, static_prefix=
private D34Record _D34Record = new D34Record();




// [DEBUG] Field: D37Record, is_external=, is_static_class=False, static_prefix=
private D37Record _D37Record = new D37Record();




// [DEBUG] Field: D37RecordFormat2, is_external=, is_static_class=False, static_prefix=
private D37RecordFormat2 _D37RecordFormat2 = new D37RecordFormat2();




// [DEBUG] Field: EqtpathLinkage, is_external=, is_static_class=False, static_prefix=
private EqtpathLinkage _EqtpathLinkage = new EqtpathLinkage();




// [DEBUG] Field: ADMIN_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string ADMIN_DATA_PATH = "EQADMIN";




// [DEBUG] Field: USER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string USER_DATA_PATH = "EQUSER";




// [DEBUG] Field: MASTER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string MASTER_DATA_PATH = "EQMASTER";




// [DEBUG] Field: GetCurrencyLinkage, is_external=, is_static_class=False, static_prefix=
private GetCurrencyLinkage _GetCurrencyLinkage = new GetCurrencyLinkage();




// [DEBUG] Field: WsReportFileName, is_external=, is_static_class=False, static_prefix=
private string _WsReportFileName ="";




// [DEBUG] Field: WsReportFile, is_external=, is_static_class=False, static_prefix=
private WsReportFile _WsReportFile = new WsReportFile();




// [DEBUG] Field: WsFileStatus, is_external=, is_static_class=False, static_prefix=
private string _WsFileStatus ="";




// [DEBUG] Field: WsTime, is_external=, is_static_class=False, static_prefix=
private WsTime _WsTime = new WsTime();




// [DEBUG] Field: WsDate, is_external=, is_static_class=False, static_prefix=
private WsDate _WsDate = new WsDate();




// [DEBUG] Field: WsWhenCompiled, is_external=, is_static_class=False, static_prefix=
private WsWhenCompiled _WsWhenCompiled = new WsWhenCompiled();




// [DEBUG] Field: WsLogMessageArea, is_external=, is_static_class=False, static_prefix=
private WsLogMessageArea _WsLogMessageArea = new WsLogMessageArea();




// [DEBUG] Field: WsMessage1, is_external=, is_static_class=False, static_prefix=
private WsMessage1 _WsMessage1 = new WsMessage1();




// [DEBUG] Field: WsIssuersNameTable, is_external=, is_static_class=False, static_prefix=
private WsIssuersNameTable _WsIssuersNameTable = new WsIssuersNameTable();




// [DEBUG] Field: WSub1, is_external=, is_static_class=False, static_prefix=
private int _WSub1 =0;




// [DEBUG] Field: WsDescriptionTable, is_external=, is_static_class=False, static_prefix=
private WsDescriptionTable _WsDescriptionTable = new WsDescriptionTable();




// [DEBUG] Field: WSub2, is_external=, is_static_class=False, static_prefix=
private int _WSub2 =0;




// [DEBUG] Field: WsSourceCategoryCode, is_external=, is_static_class=False, static_prefix=
private string _WsSourceCategoryCode ="";


// 88-level condition checks for WsSourceCategoryCode
public bool IsWsSourceCategoryCodeEp()
{
    if (this._WsSourceCategoryCode == "'EP'") return true;
    return false;
}
public bool IsWsSourceCategoryCodeWc()
{
    if (this._WsSourceCategoryCode == "'WC'") return true;
    return false;
}


// [DEBUG] Field: CheckCatLinkage, is_external=, is_static_class=False, static_prefix=
private CheckCatLinkage _CheckCatLinkage = new CheckCatLinkage();




// [DEBUG] Field: WsReportItems, is_external=, is_static_class=False, static_prefix=
private WsReportItems _WsReportItems = new WsReportItems();




// Getter and Setter methods

// Standard Getter
public string GetProgramName()
{
    return _ProgramName;
}

// Standard Setter
public void SetProgramName(string value)
{
    _ProgramName = value;
}

// Get<>AsString()
public string GetProgramNameAsString()
{
    return _ProgramName.PadRight(12);
}

// Set<>AsString()
public void SetProgramNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _ProgramName = value;
}

// Standard Getter
public string GetVersionNumber()
{
    return _VersionNumber;
}

// Standard Setter
public void SetVersionNumber(string value)
{
    _VersionNumber = value;
}

// Get<>AsString()
public string GetVersionNumberAsString()
{
    return _VersionNumber.PadRight(4);
}

// Set<>AsString()
public void SetVersionNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _VersionNumber = value;
}

// Standard Getter
public string GetWsProcessFlag()
{
    return _WsProcessFlag;
}

// Standard Setter
public void SetWsProcessFlag(string value)
{
    _WsProcessFlag = value;
}

// Get<>AsString()
public string GetWsProcessFlagAsString()
{
    return _WsProcessFlag.PadRight(1);
}

// Set<>AsString()
public void SetWsProcessFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsProcessFlag = value;
}

// Standard Getter
public string GetWsMasterFileAlreadyOpen()
{
    return _WsMasterFileAlreadyOpen;
}

// Standard Setter
public void SetWsMasterFileAlreadyOpen(string value)
{
    _WsMasterFileAlreadyOpen = value;
}

// Get<>AsString()
public string GetWsMasterFileAlreadyOpenAsString()
{
    return _WsMasterFileAlreadyOpen.PadRight(1);
}

// Set<>AsString()
public void SetWsMasterFileAlreadyOpenAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMasterFileAlreadyOpen = value;
}

// Standard Getter
public string GetWsCompanyFileAlreadyOpen()
{
    return _WsCompanyFileAlreadyOpen;
}

// Standard Setter
public void SetWsCompanyFileAlreadyOpen(string value)
{
    _WsCompanyFileAlreadyOpen = value;
}

// Get<>AsString()
public string GetWsCompanyFileAlreadyOpenAsString()
{
    return _WsCompanyFileAlreadyOpen.PadRight(1);
}

// Set<>AsString()
public void SetWsCompanyFileAlreadyOpenAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCompanyFileAlreadyOpen = value;
}

// Standard Getter
public string GetWsTrcodesFileAlreadyOpen()
{
    return _WsTrcodesFileAlreadyOpen;
}

// Standard Setter
public void SetWsTrcodesFileAlreadyOpen(string value)
{
    _WsTrcodesFileAlreadyOpen = value;
}

// Get<>AsString()
public string GetWsTrcodesFileAlreadyOpenAsString()
{
    return _WsTrcodesFileAlreadyOpen.PadRight(1);
}

// Set<>AsString()
public void SetWsTrcodesFileAlreadyOpenAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsTrcodesFileAlreadyOpen = value;
}

// Standard Getter
public string GetWsSearchTrCode()
{
    return _WsSearchTrCode;
}

// Standard Setter
public void SetWsSearchTrCode(string value)
{
    _WsSearchTrCode = value;
}

// Get<>AsString()
public string GetWsSearchTrCodeAsString()
{
    return _WsSearchTrCode.PadRight(2);
}

// Set<>AsString()
public void SetWsSearchTrCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsSearchTrCode = value;
}

// Standard Getter
public string GetWPrevCurrencySort()
{
    return _WPrevCurrencySort;
}

// Standard Setter
public void SetWPrevCurrencySort(string value)
{
    _WPrevCurrencySort = value;
}

// Get<>AsString()
public string GetWPrevCurrencySortAsString()
{
    return _WPrevCurrencySort.PadRight(1);
}

// Set<>AsString()
public void SetWPrevCurrencySortAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WPrevCurrencySort = value;
}

// Standard Getter
public string GetWCurrencyChanged()
{
    return _WCurrencyChanged;
}

// Standard Setter
public void SetWCurrencyChanged(string value)
{
    _WCurrencyChanged = value;
}

// Get<>AsString()
public string GetWCurrencyChangedAsString()
{
    return _WCurrencyChanged.PadRight(1);
}

// Set<>AsString()
public void SetWCurrencyChangedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WCurrencyChanged = value;
}

// Standard Getter
public D94Record GetD94Record()
{
    return _D94Record;
}

// Standard Setter
public void SetD94Record(D94Record value)
{
    _D94Record = value;
}

// Get<>AsString()
public string GetD94RecordAsString()
{
    return _D94Record != null ? _D94Record.GetD94RecordAsString() : "";
}

// Set<>AsString()
public void SetD94RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D94Record == null)
    {
        _D94Record = new D94Record();
    }
    _D94Record.SetD94RecordAsString(value);
}

// Standard Getter
public ElcggioLink1 GetElcggioLink1()
{
    return _ElcggioLink1;
}

// Standard Setter
public void SetElcggioLink1(ElcggioLink1 value)
{
    _ElcggioLink1 = value;
}

// Get<>AsString()
public string GetElcggioLink1AsString()
{
    return _ElcggioLink1 != null ? _ElcggioLink1.GetElcggioLink1AsString() : "";
}

// Set<>AsString()
public void SetElcggioLink1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcggioLink1 == null)
    {
        _ElcggioLink1 = new ElcggioLink1();
    }
    _ElcggioLink1.SetElcggioLink1AsString(value);
}

// Standard Getter
public ElcggioLink2 GetElcggioLink2()
{
    return _ElcggioLink2;
}

// Standard Setter
public void SetElcggioLink2(ElcggioLink2 value)
{
    _ElcggioLink2 = value;
}

// Get<>AsString()
public string GetElcggioLink2AsString()
{
    return _ElcggioLink2 != null ? _ElcggioLink2.GetElcggioLink2AsString() : "";
}

// Set<>AsString()
public void SetElcggioLink2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcggioLink2 == null)
    {
        _ElcggioLink2 = new ElcggioLink2();
    }
    _ElcggioLink2.SetElcggioLink2AsString(value);
}

// Standard Getter
public CommonLinkage GetCommonLinkage()
{
    return _CommonLinkage;
}

// Standard Setter
public void SetCommonLinkage(CommonLinkage value)
{
    _CommonLinkage = value;
}

// Get<>AsString()
public string GetCommonLinkageAsString()
{
    return _CommonLinkage != null ? _CommonLinkage.GetCommonLinkageAsString() : "";
}

// Set<>AsString()
public void SetCommonLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CommonLinkage == null)
    {
        _CommonLinkage = new CommonLinkage();
    }
    _CommonLinkage.SetCommonLinkageAsString(value);
}

// Standard Getter
public D34Record GetD34Record()
{
    return _D34Record;
}

// Standard Setter
public void SetD34Record(D34Record value)
{
    _D34Record = value;
}

// Get<>AsString()
public string GetD34RecordAsString()
{
    return _D34Record != null ? _D34Record.GetD34RecordAsString() : "";
}

// Set<>AsString()
public void SetD34RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D34Record == null)
    {
        _D34Record = new D34Record();
    }
    _D34Record.SetD34RecordAsString(value);
}

// Standard Getter
public D37Record GetD37Record()
{
    return _D37Record;
}

// Standard Setter
public void SetD37Record(D37Record value)
{
    _D37Record = value;
}

// Get<>AsString()
public string GetD37RecordAsString()
{
    return _D37Record != null ? _D37Record.GetD37RecordAsString() : "";
}

// Set<>AsString()
public void SetD37RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D37Record == null)
    {
        _D37Record = new D37Record();
    }
    _D37Record.SetD37RecordAsString(value);
}

// Standard Getter
public D37RecordFormat2 GetD37RecordFormat2()
{
    return _D37RecordFormat2;
}

// Standard Setter
public void SetD37RecordFormat2(D37RecordFormat2 value)
{
    _D37RecordFormat2 = value;
}

// Get<>AsString()
public string GetD37RecordFormat2AsString()
{
    return _D37RecordFormat2 != null ? _D37RecordFormat2.GetD37RecordFormat2AsString() : "";
}

// Set<>AsString()
public void SetD37RecordFormat2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D37RecordFormat2 == null)
    {
        _D37RecordFormat2 = new D37RecordFormat2();
    }
    _D37RecordFormat2.SetD37RecordFormat2AsString(value);
}

// Standard Getter
public EqtpathLinkage GetEqtpathLinkage()
{
    return _EqtpathLinkage;
}

// Standard Setter
public void SetEqtpathLinkage(EqtpathLinkage value)
{
    _EqtpathLinkage = value;
}

// Get<>AsString()
public string GetEqtpathLinkageAsString()
{
    return _EqtpathLinkage != null ? _EqtpathLinkage.GetEqtpathLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqtpathLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqtpathLinkage == null)
    {
        _EqtpathLinkage = new EqtpathLinkage();
    }
    _EqtpathLinkage.SetEqtpathLinkageAsString(value);
}

// Standard Getter
public GetCurrencyLinkage GetGetCurrencyLinkage()
{
    return _GetCurrencyLinkage;
}

// Standard Setter
public void SetGetCurrencyLinkage(GetCurrencyLinkage value)
{
    _GetCurrencyLinkage = value;
}

// Get<>AsString()
public string GetGetCurrencyLinkageAsString()
{
    return _GetCurrencyLinkage != null ? _GetCurrencyLinkage.GetGetCurrencyLinkageAsString() : "";
}

// Set<>AsString()
public void SetGetCurrencyLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_GetCurrencyLinkage == null)
    {
        _GetCurrencyLinkage = new GetCurrencyLinkage();
    }
    _GetCurrencyLinkage.SetGetCurrencyLinkageAsString(value);
}

// Standard Getter
public string GetWsReportFileName()
{
    return _WsReportFileName;
}

// Standard Setter
public void SetWsReportFileName(string value)
{
    _WsReportFileName = value;
}

// Get<>AsString()
public string GetWsReportFileNameAsString()
{
    return _WsReportFileName.PadRight(256);
}

// Set<>AsString()
public void SetWsReportFileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsReportFileName = value;
}

// Standard Getter
public WsReportFile GetWsReportFile()
{
    return _WsReportFile;
}

// Standard Setter
public void SetWsReportFile(WsReportFile value)
{
    _WsReportFile = value;
}

// Get<>AsString()
public string GetWsReportFileAsString()
{
    return _WsReportFile != null ? _WsReportFile.GetWsReportFileAsString() : "";
}

// Set<>AsString()
public void SetWsReportFileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsReportFile == null)
    {
        _WsReportFile = new WsReportFile();
    }
    _WsReportFile.SetWsReportFileAsString(value);
}

// Standard Getter
public string GetWsFileStatus()
{
    return _WsFileStatus;
}

// Standard Setter
public void SetWsFileStatus(string value)
{
    _WsFileStatus = value;
}

// Get<>AsString()
public string GetWsFileStatusAsString()
{
    return _WsFileStatus.PadRight(2);
}

// Set<>AsString()
public void SetWsFileStatusAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsFileStatus = value;
}

// Standard Getter
public WsTime GetWsTime()
{
    return _WsTime;
}

// Standard Setter
public void SetWsTime(WsTime value)
{
    _WsTime = value;
}

// Get<>AsString()
public string GetWsTimeAsString()
{
    return _WsTime != null ? _WsTime.GetWsTimeAsString() : "";
}

// Set<>AsString()
public void SetWsTimeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsTime == null)
    {
        _WsTime = new WsTime();
    }
    _WsTime.SetWsTimeAsString(value);
}

// Standard Getter
public WsDate GetWsDate()
{
    return _WsDate;
}

// Standard Setter
public void SetWsDate(WsDate value)
{
    _WsDate = value;
}

// Get<>AsString()
public string GetWsDateAsString()
{
    return _WsDate != null ? _WsDate.GetWsDateAsString() : "";
}

// Set<>AsString()
public void SetWsDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsDate == null)
    {
        _WsDate = new WsDate();
    }
    _WsDate.SetWsDateAsString(value);
}

// Standard Getter
public WsWhenCompiled GetWsWhenCompiled()
{
    return _WsWhenCompiled;
}

// Standard Setter
public void SetWsWhenCompiled(WsWhenCompiled value)
{
    _WsWhenCompiled = value;
}

// Get<>AsString()
public string GetWsWhenCompiledAsString()
{
    return _WsWhenCompiled != null ? _WsWhenCompiled.GetWsWhenCompiledAsString() : "";
}

// Set<>AsString()
public void SetWsWhenCompiledAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsWhenCompiled == null)
    {
        _WsWhenCompiled = new WsWhenCompiled();
    }
    _WsWhenCompiled.SetWsWhenCompiledAsString(value);
}

// Standard Getter
public WsLogMessageArea GetWsLogMessageArea()
{
    return _WsLogMessageArea;
}

// Standard Setter
public void SetWsLogMessageArea(WsLogMessageArea value)
{
    _WsLogMessageArea = value;
}

// Get<>AsString()
public string GetWsLogMessageAreaAsString()
{
    return _WsLogMessageArea != null ? _WsLogMessageArea.GetWsLogMessageAreaAsString() : "";
}

// Set<>AsString()
public void SetWsLogMessageAreaAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsLogMessageArea == null)
    {
        _WsLogMessageArea = new WsLogMessageArea();
    }
    _WsLogMessageArea.SetWsLogMessageAreaAsString(value);
}

// Standard Getter
public WsMessage1 GetWsMessage1()
{
    return _WsMessage1;
}

// Standard Setter
public void SetWsMessage1(WsMessage1 value)
{
    _WsMessage1 = value;
}

// Get<>AsString()
public string GetWsMessage1AsString()
{
    return _WsMessage1 != null ? _WsMessage1.GetWsMessage1AsString() : "";
}

// Set<>AsString()
public void SetWsMessage1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsMessage1 == null)
    {
        _WsMessage1 = new WsMessage1();
    }
    _WsMessage1.SetWsMessage1AsString(value);
}

// Standard Getter
public WsIssuersNameTable GetWsIssuersNameTable()
{
    return _WsIssuersNameTable;
}

// Standard Setter
public void SetWsIssuersNameTable(WsIssuersNameTable value)
{
    _WsIssuersNameTable = value;
}

// Get<>AsString()
public string GetWsIssuersNameTableAsString()
{
    return _WsIssuersNameTable != null ? _WsIssuersNameTable.GetWsIssuersNameTableAsString() : "";
}

// Set<>AsString()
public void SetWsIssuersNameTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIssuersNameTable == null)
    {
        _WsIssuersNameTable = new WsIssuersNameTable();
    }
    _WsIssuersNameTable.SetWsIssuersNameTableAsString(value);
}

// Standard Getter
public int GetWSub1()
{
    return _WSub1;
}

// Standard Setter
public void SetWSub1(int value)
{
    _WSub1 = value;
}

// Get<>AsString()
public string GetWSub1AsString()
{
    return _WSub1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWSub1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WSub1 = parsed;
}

// Standard Getter
public WsDescriptionTable GetWsDescriptionTable()
{
    return _WsDescriptionTable;
}

// Standard Setter
public void SetWsDescriptionTable(WsDescriptionTable value)
{
    _WsDescriptionTable = value;
}

// Get<>AsString()
public string GetWsDescriptionTableAsString()
{
    return _WsDescriptionTable != null ? _WsDescriptionTable.GetWsDescriptionTableAsString() : "";
}

// Set<>AsString()
public void SetWsDescriptionTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsDescriptionTable == null)
    {
        _WsDescriptionTable = new WsDescriptionTable();
    }
    _WsDescriptionTable.SetWsDescriptionTableAsString(value);
}

// Standard Getter
public int GetWSub2()
{
    return _WSub2;
}

// Standard Setter
public void SetWSub2(int value)
{
    _WSub2 = value;
}

// Get<>AsString()
public string GetWSub2AsString()
{
    return _WSub2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWSub2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WSub2 = parsed;
}

// Standard Getter
public string GetWsSourceCategoryCode()
{
    return _WsSourceCategoryCode;
}

// Standard Setter
public void SetWsSourceCategoryCode(string value)
{
    _WsSourceCategoryCode = value;
}

// Get<>AsString()
public string GetWsSourceCategoryCodeAsString()
{
    return _WsSourceCategoryCode.PadRight(2);
}

// Set<>AsString()
public void SetWsSourceCategoryCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsSourceCategoryCode = value;
}

// Standard Getter
public CheckCatLinkage GetCheckCatLinkage()
{
    return _CheckCatLinkage;
}

// Standard Setter
public void SetCheckCatLinkage(CheckCatLinkage value)
{
    _CheckCatLinkage = value;
}

// Get<>AsString()
public string GetCheckCatLinkageAsString()
{
    return _CheckCatLinkage != null ? _CheckCatLinkage.GetCheckCatLinkageAsString() : "";
}

// Set<>AsString()
public void SetCheckCatLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CheckCatLinkage == null)
    {
        _CheckCatLinkage = new CheckCatLinkage();
    }
    _CheckCatLinkage.SetCheckCatLinkageAsString(value);
}

// Standard Getter
public WsReportItems GetWsReportItems()
{
    return _WsReportItems;
}

// Standard Setter
public void SetWsReportItems(WsReportItems value)
{
    _WsReportItems = value;
}

// Get<>AsString()
public string GetWsReportItemsAsString()
{
    return _WsReportItems != null ? _WsReportItems.GetWsReportItemsAsString() : "";
}

// Set<>AsString()
public void SetWsReportItemsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsReportItems == null)
    {
        _WsReportItems = new WsReportItems();
    }
    _WsReportItems.SetWsReportItemsAsString(value);
}


}}